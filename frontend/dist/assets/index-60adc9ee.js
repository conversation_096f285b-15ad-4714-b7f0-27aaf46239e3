var Ss=Object.defineProperty;var Cs=(e,t,r)=>t in e?Ss(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Q=(e,t,r)=>(Cs(e,typeof t!="symbol"?t+"":t,r),r);import{r as S,a as Rs,R as Es}from"./vendor-b1791c80.js";import{u as Bt,Q as ks,a as Ts}from"./query-5a5acc5e.js";import{R as It,D as te,C as pt,a as pe,A as Ce,S as Re,b as Ee,F as xt,c as As,d as Os,B as zt,T as $e,e as gt,f as Ps,g as _s,L as Ls,h as Us,i as Fs,W as bt,j as Ms,M as Bs,U as Is,X as zs,k as Ds,l as qs,P as $s}from"./icons-faa21fa3.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(a){if(a.ep)return;a.ep=!0;const o=r(a);fetch(a.href,o)}})();var Dt={exports:{}},Oe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vs=S,Hs=Symbol.for("react.element"),Ws=Symbol.for("react.fragment"),Js=Object.prototype.hasOwnProperty,Gs=Vs.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ks={key:!0,ref:!0,__self:!0,__source:!0};function qt(e,t,r){var n,a={},o=null,i=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)Js.call(t,n)&&!Ks.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)a[n]===void 0&&(a[n]=t[n]);return{$$typeof:Hs,type:e,key:o,ref:i,props:a,_owner:Gs.current}}Oe.Fragment=Ws;Oe.jsx=qt;Oe.jsxs=qt;Dt.exports=Oe;var s=Dt.exports,Ge={},yt=Rs;Ge.createRoot=yt.createRoot,Ge.hydrateRoot=yt.hydrateRoot;function $t(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=$t(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Vt(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=$t(e))&&(n&&(n+=" "),n+=t);return n}const st="-",Xs=e=>{const t=Zs(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:i=>{const l=i.split(st);return l[0]===""&&l.length!==1&&l.shift(),Ht(l,t)||Qs(i)},getConflictingClassGroupIds:(i,l)=>{const m=r[i]||[];return l&&n[i]?[...m,...n[i]]:m}}},Ht=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),a=n?Ht(e.slice(1),n):void 0;if(a)return a;if(t.validators.length===0)return;const o=e.join(st);return(i=t.validators.find(({validator:l})=>l(o)))==null?void 0:i.classGroupId},jt=/^\[(.+)\]$/,Qs=e=>{if(jt.test(e)){const t=jt.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Zs=e=>{const{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return er(Object.entries(e.classGroups),r).forEach(([o,i])=>{Ke(i,n,o,t)}),n},Ke=(e,t,r,n)=>{e.forEach(a=>{if(typeof a=="string"){const o=a===""?t:wt(t,a);o.classGroupId=r;return}if(typeof a=="function"){if(Ys(a)){Ke(a(n),t,r,n);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([o,i])=>{Ke(i,wt(t,o),r,n)})})},wt=(e,t)=>{let r=e;return t.split(st).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Ys=e=>e.isThemeGetter,er=(e,t)=>t?e.map(([r,n])=>{const a=n.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,l])=>[t+i,l])):o);return[r,a]}):e,tr=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const a=(o,i)=>{r.set(o,i),t++,t>e&&(t=0,n=r,r=new Map)};return{get(o){let i=r.get(o);if(i!==void 0)return i;if((i=n.get(o))!==void 0)return a(o,i),i},set(o,i){r.has(o)?r.set(o,i):a(o,i)}}},Wt="!",sr=e=>{const{separator:t,experimentalParseClassName:r}=e,n=t.length===1,a=t[0],o=t.length,i=l=>{const m=[];let c=0,d=0,f;for(let h=0;h<l.length;h++){let j=l[h];if(c===0){if(j===a&&(n||l.slice(h,h+o)===t)){m.push(l.slice(d,h)),d=h+o;continue}if(j==="/"){f=h;continue}}j==="["?c++:j==="]"&&c--}const g=m.length===0?l:l.substring(d),v=g.startsWith(Wt),p=v?g.substring(1):g,x=f&&f>d?f-d:void 0;return{modifiers:m,hasImportantModifier:v,baseClassName:p,maybePostfixModifierPosition:x}};return r?l=>r({className:l,parseClassName:i}):i},rr=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(n=>{n[0]==="["?(t.push(...r.sort(),n),r=[]):r.push(n)}),t.push(...r.sort()),t},nr=e=>({cache:tr(e.cacheSize),parseClassName:sr(e),...Xs(e)}),ar=/\s+/,or=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a}=t,o=[],i=e.trim().split(ar);let l="";for(let m=i.length-1;m>=0;m-=1){const c=i[m],{modifiers:d,hasImportantModifier:f,baseClassName:g,maybePostfixModifierPosition:v}=r(c);let p=!!v,x=n(p?g.substring(0,v):g);if(!x){if(!p){l=c+(l.length>0?" "+l:l);continue}if(x=n(g),!x){l=c+(l.length>0?" "+l:l);continue}p=!1}const h=rr(d).join(":"),j=f?h+Wt:h,N=j+x;if(o.includes(N))continue;o.push(N);const C=a(x,p);for(let L=0;L<C.length;++L){const b=C[L];o.push(j+b)}l=c+(l.length>0?" "+l:l)}return l};function ir(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Jt(t))&&(n&&(n+=" "),n+=r);return n}const Jt=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=Jt(e[n]))&&(r&&(r+=" "),r+=t);return r};function lr(e,...t){let r,n,a,o=i;function i(m){const c=t.reduce((d,f)=>f(d),e());return r=nr(c),n=r.cache.get,a=r.cache.set,o=l,l(m)}function l(m){const c=n(m);if(c)return c;const d=or(m,r);return a(m,d),d}return function(){return o(ir.apply(null,arguments))}}const R=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Gt=/^\[(?:([a-z-]+):)?(.+)\]$/i,cr=/^\d+\/\d+$/,dr=new Set(["px","full","screen"]),ur=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,mr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,hr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,fr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,pr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,X=e=>re(e)||dr.has(e)||cr.test(e),Z=e=>ie(e,"length",Nr),re=e=>!!e&&!Number.isNaN(Number(e)),Ve=e=>ie(e,"number",re),ue=e=>!!e&&Number.isInteger(Number(e)),xr=e=>e.endsWith("%")&&re(e.slice(0,-1)),w=e=>Gt.test(e),Y=e=>ur.test(e),gr=new Set(["length","size","percentage"]),br=e=>ie(e,gr,Kt),yr=e=>ie(e,"position",Kt),jr=new Set(["image","url"]),wr=e=>ie(e,jr,Cr),vr=e=>ie(e,"",Sr),me=()=>!0,ie=(e,t,r)=>{const n=Gt.exec(e);return n?n[1]?typeof t=="string"?n[1]===t:t.has(n[1]):r(n[2]):!1},Nr=e=>mr.test(e)&&!hr.test(e),Kt=()=>!1,Sr=e=>fr.test(e),Cr=e=>pr.test(e),Rr=()=>{const e=R("colors"),t=R("spacing"),r=R("blur"),n=R("brightness"),a=R("borderColor"),o=R("borderRadius"),i=R("borderSpacing"),l=R("borderWidth"),m=R("contrast"),c=R("grayscale"),d=R("hueRotate"),f=R("invert"),g=R("gap"),v=R("gradientColorStops"),p=R("gradientColorStopPositions"),x=R("inset"),h=R("margin"),j=R("opacity"),N=R("padding"),C=R("saturate"),L=R("scale"),b=R("sepia"),T=R("skew"),$=R("space"),F=R("translate"),G=()=>["auto","contain","none"],ze=()=>["auto","hidden","clip","visible","scroll"],De=()=>["auto",w,t],k=()=>[w,t],ut=()=>["",X,Z],be=()=>["auto",re,w],mt=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],ye=()=>["solid","dashed","dotted","double","none"],ht=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],qe=()=>["start","end","center","between","around","evenly","stretch"],de=()=>["","0",w],ft=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[re,w];return{cacheSize:500,separator:":",theme:{colors:[me],spacing:[X,Z],blur:["none","",Y,w],brightness:K(),borderColor:[e],borderRadius:["none","","full",Y,w],borderSpacing:k(),borderWidth:ut(),contrast:K(),grayscale:de(),hueRotate:K(),invert:de(),gap:k(),gradientColorStops:[e],gradientColorStopPositions:[xr,Z],inset:De(),margin:De(),opacity:K(),padding:k(),saturate:K(),scale:K(),sepia:de(),skew:K(),space:k(),translate:k()},classGroups:{aspect:[{aspect:["auto","square","video",w]}],container:["container"],columns:[{columns:[Y]}],"break-after":[{"break-after":ft()}],"break-before":[{"break-before":ft()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...mt(),w]}],overflow:[{overflow:ze()}],"overflow-x":[{"overflow-x":ze()}],"overflow-y":[{"overflow-y":ze()}],overscroll:[{overscroll:G()}],"overscroll-x":[{"overscroll-x":G()}],"overscroll-y":[{"overscroll-y":G()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",ue,w]}],basis:[{basis:De()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",w]}],grow:[{grow:de()}],shrink:[{shrink:de()}],order:[{order:["first","last","none",ue,w]}],"grid-cols":[{"grid-cols":[me]}],"col-start-end":[{col:["auto",{span:["full",ue,w]},w]}],"col-start":[{"col-start":be()}],"col-end":[{"col-end":be()}],"grid-rows":[{"grid-rows":[me]}],"row-start-end":[{row:["auto",{span:[ue,w]},w]}],"row-start":[{"row-start":be()}],"row-end":[{"row-end":be()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",w]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",w]}],gap:[{gap:[g]}],"gap-x":[{"gap-x":[g]}],"gap-y":[{"gap-y":[g]}],"justify-content":[{justify:["normal",...qe()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...qe(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...qe(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[N]}],px:[{px:[N]}],py:[{py:[N]}],ps:[{ps:[N]}],pe:[{pe:[N]}],pt:[{pt:[N]}],pr:[{pr:[N]}],pb:[{pb:[N]}],pl:[{pl:[N]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[$]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[$]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",w,t]}],"min-w":[{"min-w":[w,t,"min","max","fit"]}],"max-w":[{"max-w":[w,t,"none","full","min","max","fit","prose",{screen:[Y]},Y]}],h:[{h:[w,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[w,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[w,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[w,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Y,Z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ve]}],"font-family":[{font:[me]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",w]}],"line-clamp":[{"line-clamp":["none",re,Ve]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",X,w]}],"list-image":[{"list-image":["none",w]}],"list-style-type":[{list:["none","disc","decimal",w]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[j]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[j]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ye(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",X,Z]}],"underline-offset":[{"underline-offset":["auto",X,w]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",w]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",w]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[j]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...mt(),yr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",br]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},wr]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[j]}],"border-style":[{border:[...ye(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[j]}],"divide-style":[{divide:ye()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["",...ye()]}],"outline-offset":[{"outline-offset":[X,w]}],"outline-w":[{outline:[X,Z]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:ut()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[j]}],"ring-offset-w":[{"ring-offset":[X,Z]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Y,vr]}],"shadow-color":[{shadow:[me]}],opacity:[{opacity:[j]}],"mix-blend":[{"mix-blend":[...ht(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ht()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[m]}],"drop-shadow":[{"drop-shadow":["","none",Y,w]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[C]}],sepia:[{sepia:[b]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[m]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[j]}],"backdrop-saturate":[{"backdrop-saturate":[C]}],"backdrop-sepia":[{"backdrop-sepia":[b]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",w]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",w]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",w]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[L]}],"scale-x":[{"scale-x":[L]}],"scale-y":[{"scale-y":[L]}],rotate:[{rotate:[ue,w]}],"translate-x":[{"translate-x":[F]}],"translate-y":[{"translate-y":[F]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",w]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",w]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",w]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[X,Z,Ve]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Er=lr(Rr);function U(...e){return Er(Vt(e))}const E=S.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:U("rounded-lg bg-white text-gray-950 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200",e),...t}));E.displayName="Card";const O=S.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:U("flex flex-col space-y-1.5 p-6",e),...t}));O.displayName="CardHeader";const P=S.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:U("text-2xl font-semibold leading-none tracking-tight",e),...t}));P.displayName="CardTitle";const M=S.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:U("text-sm text-gray-500",e),...t}));M.displayName="CardDescription";const A=S.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:U("p-6 pt-0",e),...t}));A.displayName="CardContent";const kr=S.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:U("flex items-center p-6 pt-0",e),...t}));kr.displayName="CardFooter";const vt=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Nt=Vt,Xt=(e,t)=>r=>{var n;if((t==null?void 0:t.variants)==null)return Nt(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:a,defaultVariants:o}=t,i=Object.keys(a).map(c=>{const d=r==null?void 0:r[c],f=o==null?void 0:o[c];if(d===null)return null;const g=vt(d)||vt(f);return a[c][g]}),l=r&&Object.entries(r).reduce((c,d)=>{let[f,g]=d;return g===void 0||(c[f]=g),c},{}),m=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((c,d)=>{let{class:f,className:g,...v}=d;return Object.entries(v).every(p=>{let[x,h]=p;return Array.isArray(h)?h.includes({...o,...l}[x]):{...o,...l}[x]===h})?[...c,f,g]:c},[]);return Nt(e,i,m,r==null?void 0:r.class,r==null?void 0:r.className)},Tr=Xt("inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50",{variants:{variant:{default:"bg-indigo-100 text-indigo-700 hover:bg-indigo-200",secondary:"bg-gray-50 text-gray-700 hover:bg-gray-100",destructive:"bg-red-100 text-red-700 hover:bg-red-200",outline:"border border-gray-200 text-gray-700 hover:bg-gray-50",success:"bg-green-100 text-green-700 hover:bg-green-200",warning:"bg-yellow-100 text-yellow-700 hover:bg-yellow-200"}},defaultVariants:{variant:"default"}});function B({className:e,variant:t,...r}){return s.jsx("div",{className:U(Tr({variant:t}),e),...r})}const Qt=S.forwardRef(({className:e,value:t,...r},n)=>s.jsx("div",{ref:n,className:U("relative h-3 w-full overflow-hidden rounded-full bg-gray-100",e),...r,children:s.jsx("div",{className:"h-full w-full flex-1 bg-gradient-to-r from-indigo-500 to-indigo-600 transition-all duration-300 ease-out",style:{transform:`translateX(-${100-(t||0)}%)`}})}));Qt.displayName="Progress";const Ar=Xt("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-opacity-50 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-indigo-600 text-white hover:bg-indigo-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-300",secondary:"bg-gray-50 text-gray-700 hover:bg-gray-100 hover:text-gray-900",ghost:"text-gray-600 hover:bg-indigo-50 hover:text-indigo-700",link:"text-indigo-600 underline-offset-4 hover:underline hover:text-indigo-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),z=S.forwardRef(({className:e,variant:t,size:r,...n},a)=>s.jsx("button",{className:U(Ar({variant:t,size:r,className:e})),ref:a,...n}));z.displayName="Button";function Zt(e,t){return function(){return e.apply(t,arguments)}}const{toString:Or}=Object.prototype,{getPrototypeOf:rt}=Object,{iterator:Pe,toStringTag:Yt}=Symbol,_e=(e=>t=>{const r=Or.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),H=e=>(e=e.toLowerCase(),t=>_e(t)===e),Le=e=>t=>typeof t===e,{isArray:le}=Array,xe=Le("undefined");function Pr(e){return e!==null&&!xe(e)&&e.constructor!==null&&!xe(e.constructor)&&D(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const es=H("ArrayBuffer");function _r(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&es(e.buffer),t}const Lr=Le("string"),D=Le("function"),ts=Le("number"),Ue=e=>e!==null&&typeof e=="object",Ur=e=>e===!0||e===!1,we=e=>{if(_e(e)!=="object")return!1;const t=rt(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Yt in e)&&!(Pe in e)},Fr=H("Date"),Mr=H("File"),Br=H("Blob"),Ir=H("FileList"),zr=e=>Ue(e)&&D(e.pipe),Dr=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||D(e.append)&&((t=_e(e))==="formdata"||t==="object"&&D(e.toString)&&e.toString()==="[object FormData]"))},qr=H("URLSearchParams"),[$r,Vr,Hr,Wr]=["ReadableStream","Request","Response","Headers"].map(H),Jr=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ge(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,a;if(typeof e!="object"&&(e=[e]),le(e))for(n=0,a=e.length;n<a;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(n=0;n<i;n++)l=o[n],t.call(null,e[l],l,e)}}function ss(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,a;for(;n-- >0;)if(a=r[n],t===a.toLowerCase())return a;return null}const ee=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),rs=e=>!xe(e)&&e!==ee;function Xe(){const{caseless:e}=rs(this)&&this||{},t={},r=(n,a)=>{const o=e&&ss(t,a)||a;we(t[o])&&we(n)?t[o]=Xe(t[o],n):we(n)?t[o]=Xe({},n):le(n)?t[o]=n.slice():t[o]=n};for(let n=0,a=arguments.length;n<a;n++)arguments[n]&&ge(arguments[n],r);return t}const Gr=(e,t,r,{allOwnKeys:n}={})=>(ge(t,(a,o)=>{r&&D(a)?e[o]=Zt(a,r):e[o]=a},{allOwnKeys:n}),e),Kr=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Xr=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Qr=(e,t,r,n)=>{let a,o,i;const l={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!n||n(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=r!==!1&&rt(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Zr=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Yr=e=>{if(!e)return null;if(le(e))return e;let t=e.length;if(!ts(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},en=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&rt(Uint8Array)),tn=(e,t)=>{const n=(e&&e[Pe]).call(e);let a;for(;(a=n.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},sn=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},rn=H("HTMLFormElement"),nn=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,a){return n.toUpperCase()+a}),St=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),an=H("RegExp"),ns=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};ge(r,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(n[o]=i||a)}),Object.defineProperties(e,n)},on=e=>{ns(e,(t,r)=>{if(D(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(D(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},ln=(e,t)=>{const r={},n=a=>{a.forEach(o=>{r[o]=!0})};return le(e)?n(e):n(String(e).split(t)),r},cn=()=>{},dn=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function un(e){return!!(e&&D(e.append)&&e[Yt]==="FormData"&&e[Pe])}const mn=e=>{const t=new Array(10),r=(n,a)=>{if(Ue(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[a]=n;const o=le(n)?[]:{};return ge(n,(i,l)=>{const m=r(i,a+1);!xe(m)&&(o[l]=m)}),t[a]=void 0,o}}return n};return r(e,0)},hn=H("AsyncFunction"),fn=e=>e&&(Ue(e)||D(e))&&D(e.then)&&D(e.catch),as=((e,t)=>e?setImmediate:t?((r,n)=>(ee.addEventListener("message",({source:a,data:o})=>{a===ee&&o===r&&n.length&&n.shift()()},!1),a=>{n.push(a),ee.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",D(ee.postMessage)),pn=typeof queueMicrotask<"u"?queueMicrotask.bind(ee):typeof process<"u"&&process.nextTick||as,xn=e=>e!=null&&D(e[Pe]),u={isArray:le,isArrayBuffer:es,isBuffer:Pr,isFormData:Dr,isArrayBufferView:_r,isString:Lr,isNumber:ts,isBoolean:Ur,isObject:Ue,isPlainObject:we,isReadableStream:$r,isRequest:Vr,isResponse:Hr,isHeaders:Wr,isUndefined:xe,isDate:Fr,isFile:Mr,isBlob:Br,isRegExp:an,isFunction:D,isStream:zr,isURLSearchParams:qr,isTypedArray:en,isFileList:Ir,forEach:ge,merge:Xe,extend:Gr,trim:Jr,stripBOM:Kr,inherits:Xr,toFlatObject:Qr,kindOf:_e,kindOfTest:H,endsWith:Zr,toArray:Yr,forEachEntry:tn,matchAll:sn,isHTMLForm:rn,hasOwnProperty:St,hasOwnProp:St,reduceDescriptors:ns,freezeMethods:on,toObjectSet:ln,toCamelCase:nn,noop:cn,toFiniteNumber:dn,findKey:ss,global:ee,isContextDefined:rs,isSpecCompliantForm:un,toJSONObject:mn,isAsyncFn:hn,isThenable:fn,setImmediate:as,asap:pn,isIterable:xn};function y(e,t,r,n,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),a&&(this.response=a,this.status=a.status?a.status:null)}u.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:u.toJSONObject(this.config),code:this.code,status:this.status}}});const os=y.prototype,is={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{is[e]={value:e}});Object.defineProperties(y,is);Object.defineProperty(os,"isAxiosError",{value:!0});y.from=(e,t,r,n,a,o)=>{const i=Object.create(os);return u.toFlatObject(e,i,function(m){return m!==Error.prototype},l=>l!=="isAxiosError"),y.call(i,e.message,t,r,n,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const gn=null;function Qe(e){return u.isPlainObject(e)||u.isArray(e)}function ls(e){return u.endsWith(e,"[]")?e.slice(0,-2):e}function Ct(e,t,r){return e?e.concat(t).map(function(a,o){return a=ls(a),!r&&o?"["+a+"]":a}).join(r?".":""):t}function bn(e){return u.isArray(e)&&!e.some(Qe)}const yn=u.toFlatObject(u,{},null,function(t){return/^is[A-Z]/.test(t)});function Fe(e,t,r){if(!u.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=u.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,h){return!u.isUndefined(h[x])});const n=r.metaTokens,a=r.visitor||d,o=r.dots,i=r.indexes,m=(r.Blob||typeof Blob<"u"&&Blob)&&u.isSpecCompliantForm(t);if(!u.isFunction(a))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(u.isDate(p))return p.toISOString();if(u.isBoolean(p))return p.toString();if(!m&&u.isBlob(p))throw new y("Blob is not supported. Use a Buffer instead.");return u.isArrayBuffer(p)||u.isTypedArray(p)?m&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function d(p,x,h){let j=p;if(p&&!h&&typeof p=="object"){if(u.endsWith(x,"{}"))x=n?x:x.slice(0,-2),p=JSON.stringify(p);else if(u.isArray(p)&&bn(p)||(u.isFileList(p)||u.endsWith(x,"[]"))&&(j=u.toArray(p)))return x=ls(x),j.forEach(function(C,L){!(u.isUndefined(C)||C===null)&&t.append(i===!0?Ct([x],L,o):i===null?x:x+"[]",c(C))}),!1}return Qe(p)?!0:(t.append(Ct(h,x,o),c(p)),!1)}const f=[],g=Object.assign(yn,{defaultVisitor:d,convertValue:c,isVisitable:Qe});function v(p,x){if(!u.isUndefined(p)){if(f.indexOf(p)!==-1)throw Error("Circular reference detected in "+x.join("."));f.push(p),u.forEach(p,function(j,N){(!(u.isUndefined(j)||j===null)&&a.call(t,j,u.isString(N)?N.trim():N,x,g))===!0&&v(j,x?x.concat(N):[N])}),f.pop()}}if(!u.isObject(e))throw new TypeError("data must be an object");return v(e),t}function Rt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function nt(e,t){this._pairs=[],e&&Fe(e,this,t)}const cs=nt.prototype;cs.append=function(t,r){this._pairs.push([t,r])};cs.toString=function(t){const r=t?function(n){return t.call(this,n,Rt)}:Rt;return this._pairs.map(function(a){return r(a[0])+"="+r(a[1])},"").join("&")};function jn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ds(e,t,r){if(!t)return e;const n=r&&r.encode||jn;u.isFunction(r)&&(r={serialize:r});const a=r&&r.serialize;let o;if(a?o=a(t,r):o=u.isURLSearchParams(t)?t.toString():new nt(t,r).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class wn{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){u.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Et=wn,us={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},vn=typeof URLSearchParams<"u"?URLSearchParams:nt,Nn=typeof FormData<"u"?FormData:null,Sn=typeof Blob<"u"?Blob:null,Cn={isBrowser:!0,classes:{URLSearchParams:vn,FormData:Nn,Blob:Sn},protocols:["http","https","file","blob","url","data"]},at=typeof window<"u"&&typeof document<"u",Ze=typeof navigator=="object"&&navigator||void 0,Rn=at&&(!Ze||["ReactNative","NativeScript","NS"].indexOf(Ze.product)<0),En=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),kn=at&&window.location.href||"http://localhost",Tn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:at,hasStandardBrowserEnv:Rn,hasStandardBrowserWebWorkerEnv:En,navigator:Ze,origin:kn},Symbol.toStringTag,{value:"Module"})),I={...Tn,...Cn};function An(e,t){return Fe(e,new I.classes.URLSearchParams,Object.assign({visitor:function(r,n,a,o){return I.isNode&&u.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function On(e){return u.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Pn(e){const t={},r=Object.keys(e);let n;const a=r.length;let o;for(n=0;n<a;n++)o=r[n],t[o]=e[o];return t}function ms(e){function t(r,n,a,o){let i=r[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),m=o>=r.length;return i=!i&&u.isArray(a)?a.length:i,m?(u.hasOwnProp(a,i)?a[i]=[a[i],n]:a[i]=n,!l):((!a[i]||!u.isObject(a[i]))&&(a[i]=[]),t(r,n,a[i],o)&&u.isArray(a[i])&&(a[i]=Pn(a[i])),!l)}if(u.isFormData(e)&&u.isFunction(e.entries)){const r={};return u.forEachEntry(e,(n,a)=>{t(On(n),a,r,0)}),r}return null}function _n(e,t,r){if(u.isString(e))try{return(t||JSON.parse)(e),u.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const ot={transitional:us,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",a=n.indexOf("application/json")>-1,o=u.isObject(t);if(o&&u.isHTMLForm(t)&&(t=new FormData(t)),u.isFormData(t))return a?JSON.stringify(ms(t)):t;if(u.isArrayBuffer(t)||u.isBuffer(t)||u.isStream(t)||u.isFile(t)||u.isBlob(t)||u.isReadableStream(t))return t;if(u.isArrayBufferView(t))return t.buffer;if(u.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return An(t,this.formSerializer).toString();if((l=u.isFileList(t))||n.indexOf("multipart/form-data")>-1){const m=this.env&&this.env.FormData;return Fe(l?{"files[]":t}:t,m&&new m,this.formSerializer)}}return o||a?(r.setContentType("application/json",!1),_n(t)):t}],transformResponse:[function(t){const r=this.transitional||ot.transitional,n=r&&r.forcedJSONParsing,a=this.responseType==="json";if(u.isResponse(t)||u.isReadableStream(t))return t;if(t&&u.isString(t)&&(n&&!this.responseType||a)){const i=!(r&&r.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?y.from(l,y.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:I.classes.FormData,Blob:I.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};u.forEach(["delete","get","head","post","put","patch"],e=>{ot.headers[e]={}});const it=ot,Ln=u.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Un=e=>{const t={};let r,n,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),r=i.substring(0,a).trim().toLowerCase(),n=i.substring(a+1).trim(),!(!r||t[r]&&Ln[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},kt=Symbol("internals");function he(e){return e&&String(e).trim().toLowerCase()}function ve(e){return e===!1||e==null?e:u.isArray(e)?e.map(ve):String(e)}function Fn(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Mn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function He(e,t,r,n,a){if(u.isFunction(n))return n.call(this,t,r);if(a&&(t=r),!!u.isString(t)){if(u.isString(n))return t.indexOf(n)!==-1;if(u.isRegExp(n))return n.test(t)}}function Bn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function In(e,t){const r=u.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(a,o,i){return this[n].call(this,t,a,o,i)},configurable:!0})})}class Me{constructor(t){t&&this.set(t)}set(t,r,n){const a=this;function o(l,m,c){const d=he(m);if(!d)throw new Error("header name must be a non-empty string");const f=u.findKey(a,d);(!f||a[f]===void 0||c===!0||c===void 0&&a[f]!==!1)&&(a[f||m]=ve(l))}const i=(l,m)=>u.forEach(l,(c,d)=>o(c,d,m));if(u.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(u.isString(t)&&(t=t.trim())&&!Mn(t))i(Un(t),r);else if(u.isObject(t)&&u.isIterable(t)){let l={},m,c;for(const d of t){if(!u.isArray(d))throw TypeError("Object iterator must return a key-value pair");l[c=d[0]]=(m=l[c])?u.isArray(m)?[...m,d[1]]:[m,d[1]]:d[1]}i(l,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=he(t),t){const n=u.findKey(this,t);if(n){const a=this[n];if(!r)return a;if(r===!0)return Fn(a);if(u.isFunction(r))return r.call(this,a,n);if(u.isRegExp(r))return r.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=he(t),t){const n=u.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||He(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let a=!1;function o(i){if(i=he(i),i){const l=u.findKey(n,i);l&&(!r||He(n,n[l],l,r))&&(delete n[l],a=!0)}}return u.isArray(t)?t.forEach(o):o(t),a}clear(t){const r=Object.keys(this);let n=r.length,a=!1;for(;n--;){const o=r[n];(!t||He(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const r=this,n={};return u.forEach(this,(a,o)=>{const i=u.findKey(n,o);if(i){r[i]=ve(a),delete r[o];return}const l=t?Bn(o):String(o).trim();l!==o&&delete r[o],r[l]=ve(a),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return u.forEach(this,(n,a)=>{n!=null&&n!==!1&&(r[a]=t&&u.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(a=>n.set(a)),n}static accessor(t){const n=(this[kt]=this[kt]={accessors:{}}).accessors,a=this.prototype;function o(i){const l=he(i);n[l]||(In(a,i),n[l]=!0)}return u.isArray(t)?t.forEach(o):o(t),this}}Me.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);u.reduceDescriptors(Me.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});u.freezeMethods(Me);const V=Me;function We(e,t){const r=this||it,n=t||r,a=V.from(n.headers);let o=n.data;return u.forEach(e,function(l){o=l.call(r,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function hs(e){return!!(e&&e.__CANCEL__)}function ce(e,t,r){y.call(this,e??"canceled",y.ERR_CANCELED,t,r),this.name="CanceledError"}u.inherits(ce,y,{__CANCEL__:!0});function fs(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new y("Request failed with status code "+r.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function zn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Dn(e,t){e=e||10;const r=new Array(e),n=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(m){const c=Date.now(),d=n[o];i||(i=c),r[a]=m,n[a]=c;let f=o,g=0;for(;f!==a;)g+=r[f++],f=f%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),c-i<t)return;const v=d&&c-d;return v?Math.round(g*1e3/v):void 0}}function qn(e,t){let r=0,n=1e3/t,a,o;const i=(c,d=Date.now())=>{r=d,a=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const d=Date.now(),f=d-r;f>=n?i(c,d):(a=c,o||(o=setTimeout(()=>{o=null,i(a)},n-f)))},()=>a&&i(a)]}const ke=(e,t,r=3)=>{let n=0;const a=Dn(50,250);return qn(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,m=i-n,c=a(m),d=i<=l;n=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:m,rate:c||void 0,estimated:c&&l&&d?(l-i)/c:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},r)},Tt=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},At=e=>(...t)=>u.asap(()=>e(...t)),$n=I.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,I.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(I.origin),I.navigator&&/(msie|trident)/i.test(I.navigator.userAgent)):()=>!0,Vn=I.hasStandardBrowserEnv?{write(e,t,r,n,a,o){const i=[e+"="+encodeURIComponent(t)];u.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),u.isString(n)&&i.push("path="+n),u.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Hn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Wn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ps(e,t,r){let n=!Hn(t);return e&&(n||r==!1)?Wn(e,t):t}const Ot=e=>e instanceof V?{...e}:e;function se(e,t){t=t||{};const r={};function n(c,d,f,g){return u.isPlainObject(c)&&u.isPlainObject(d)?u.merge.call({caseless:g},c,d):u.isPlainObject(d)?u.merge({},d):u.isArray(d)?d.slice():d}function a(c,d,f,g){if(u.isUndefined(d)){if(!u.isUndefined(c))return n(void 0,c,f,g)}else return n(c,d,f,g)}function o(c,d){if(!u.isUndefined(d))return n(void 0,d)}function i(c,d){if(u.isUndefined(d)){if(!u.isUndefined(c))return n(void 0,c)}else return n(void 0,d)}function l(c,d,f){if(f in t)return n(c,d);if(f in e)return n(void 0,c)}const m={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(c,d,f)=>a(Ot(c),Ot(d),f,!0)};return u.forEach(Object.keys(Object.assign({},e,t)),function(d){const f=m[d]||a,g=f(e[d],t[d],d);u.isUndefined(g)&&f!==l||(r[d]=g)}),r}const xs=e=>{const t=se({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=V.from(i),t.url=ds(ps(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let m;if(u.isFormData(r)){if(I.hasStandardBrowserEnv||I.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((m=i.getContentType())!==!1){const[c,...d]=m?m.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...d].join("; "))}}if(I.hasStandardBrowserEnv&&(n&&u.isFunction(n)&&(n=n(t)),n||n!==!1&&$n(t.url))){const c=a&&o&&Vn.read(o);c&&i.set(a,c)}return t},Jn=typeof XMLHttpRequest<"u",Gn=Jn&&function(e){return new Promise(function(r,n){const a=xs(e);let o=a.data;const i=V.from(a.headers).normalize();let{responseType:l,onUploadProgress:m,onDownloadProgress:c}=a,d,f,g,v,p;function x(){v&&v(),p&&p(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let h=new XMLHttpRequest;h.open(a.method.toUpperCase(),a.url,!0),h.timeout=a.timeout;function j(){if(!h)return;const C=V.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),b={data:!l||l==="text"||l==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:C,config:e,request:h};fs(function($){r($),x()},function($){n($),x()},b),h=null}"onloadend"in h?h.onloadend=j:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(j)},h.onabort=function(){h&&(n(new y("Request aborted",y.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new y("Network Error",y.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let L=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const b=a.transitional||us;a.timeoutErrorMessage&&(L=a.timeoutErrorMessage),n(new y(L,b.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,h)),h=null},o===void 0&&i.setContentType(null),"setRequestHeader"in h&&u.forEach(i.toJSON(),function(L,b){h.setRequestHeader(b,L)}),u.isUndefined(a.withCredentials)||(h.withCredentials=!!a.withCredentials),l&&l!=="json"&&(h.responseType=a.responseType),c&&([g,p]=ke(c,!0),h.addEventListener("progress",g)),m&&h.upload&&([f,v]=ke(m),h.upload.addEventListener("progress",f),h.upload.addEventListener("loadend",v)),(a.cancelToken||a.signal)&&(d=C=>{h&&(n(!C||C.type?new ce(null,e,h):C),h.abort(),h=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const N=zn(a.url);if(N&&I.protocols.indexOf(N)===-1){n(new y("Unsupported protocol "+N+":",y.ERR_BAD_REQUEST,e));return}h.send(o||null)})},Kn=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,a;const o=function(c){if(!a){a=!0,l();const d=c instanceof Error?c:this.reason;n.abort(d instanceof y?d:new ce(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:m}=n;return m.unsubscribe=()=>u.asap(l),m}},Xn=Kn,Qn=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let n=0,a;for(;n<r;)a=n+t,yield e.slice(n,a),n=a},Zn=async function*(e,t){for await(const r of Yn(e))yield*Qn(r,t)},Yn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Pt=(e,t,r,n)=>{const a=Zn(e,t);let o=0,i,l=m=>{i||(i=!0,n&&n(m))};return new ReadableStream({async pull(m){try{const{done:c,value:d}=await a.next();if(c){l(),m.close();return}let f=d.byteLength;if(r){let g=o+=f;r(g)}m.enqueue(new Uint8Array(d))}catch(c){throw l(c),c}},cancel(m){return l(m),a.return()}},{highWaterMark:2})},Be=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",gs=Be&&typeof ReadableStream=="function",ea=Be&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),bs=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ta=gs&&bs(()=>{let e=!1;const t=new Request(I.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),_t=64*1024,Ye=gs&&bs(()=>u.isReadableStream(new Response("").body)),Te={stream:Ye&&(e=>e.body)};Be&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Te[t]&&(Te[t]=u.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,n)})})})(new Response);const sa=async e=>{if(e==null)return 0;if(u.isBlob(e))return e.size;if(u.isSpecCompliantForm(e))return(await new Request(I.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(u.isArrayBufferView(e)||u.isArrayBuffer(e))return e.byteLength;if(u.isURLSearchParams(e)&&(e=e+""),u.isString(e))return(await ea(e)).byteLength},ra=async(e,t)=>{const r=u.toFiniteNumber(e.getContentLength());return r??sa(t)},na=Be&&(async e=>{let{url:t,method:r,data:n,signal:a,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:m,responseType:c,headers:d,withCredentials:f="same-origin",fetchOptions:g}=xs(e);c=c?(c+"").toLowerCase():"text";let v=Xn([a,o&&o.toAbortSignal()],i),p;const x=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let h;try{if(m&&ta&&r!=="get"&&r!=="head"&&(h=await ra(d,n))!==0){let b=new Request(t,{method:"POST",body:n,duplex:"half"}),T;if(u.isFormData(n)&&(T=b.headers.get("content-type"))&&d.setContentType(T),b.body){const[$,F]=Tt(h,ke(At(m)));n=Pt(b.body,_t,$,F)}}u.isString(f)||(f=f?"include":"omit");const j="credentials"in Request.prototype;p=new Request(t,{...g,signal:v,method:r.toUpperCase(),headers:d.normalize().toJSON(),body:n,duplex:"half",credentials:j?f:void 0});let N=await fetch(p,g);const C=Ye&&(c==="stream"||c==="response");if(Ye&&(l||C&&x)){const b={};["status","statusText","headers"].forEach(G=>{b[G]=N[G]});const T=u.toFiniteNumber(N.headers.get("content-length")),[$,F]=l&&Tt(T,ke(At(l),!0))||[];N=new Response(Pt(N.body,_t,$,()=>{F&&F(),x&&x()}),b)}c=c||"text";let L=await Te[u.findKey(Te,c)||"text"](N,e);return!C&&x&&x(),await new Promise((b,T)=>{fs(b,T,{data:L,headers:V.from(N.headers),status:N.status,statusText:N.statusText,config:e,request:p})})}catch(j){throw x&&x(),j&&j.name==="TypeError"&&/Load failed|fetch/i.test(j.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,p),{cause:j.cause||j}):y.from(j,j&&j.code,e,p)}}),et={http:gn,xhr:Gn,fetch:na};u.forEach(et,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Lt=e=>`- ${e}`,aa=e=>u.isFunction(e)||e===null||e===!1,ys={getAdapter:e=>{e=u.isArray(e)?e:[e];const{length:t}=e;let r,n;const a={};for(let o=0;o<t;o++){r=e[o];let i;if(n=r,!aa(r)&&(n=et[(i=String(r)).toLowerCase()],n===void 0))throw new y(`Unknown adapter '${i}'`);if(n)break;a[i||"#"+o]=n}if(!n){const o=Object.entries(a).map(([l,m])=>`adapter ${l} `+(m===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Lt).join(`
`):" "+Lt(o[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:et};function Je(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ce(null,e)}function Ut(e){return Je(e),e.headers=V.from(e.headers),e.data=We.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ys.getAdapter(e.adapter||it.adapter)(e).then(function(n){return Je(e),n.data=We.call(e,e.transformResponse,n),n.headers=V.from(n.headers),n},function(n){return hs(n)||(Je(e),n&&n.response&&(n.response.data=We.call(e,e.transformResponse,n.response),n.response.headers=V.from(n.response.headers))),Promise.reject(n)})}const js="1.10.0",Ie={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ie[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Ft={};Ie.transitional=function(t,r,n){function a(o,i){return"[Axios v"+js+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,l)=>{if(t===!1)throw new y(a(i," has been removed"+(r?" in "+r:"")),y.ERR_DEPRECATED);return r&&!Ft[i]&&(Ft[i]=!0,console.warn(a(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,i,l):!0}};Ie.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function oa(e,t,r){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let a=n.length;for(;a-- >0;){const o=n[a],i=t[o];if(i){const l=e[o],m=l===void 0||i(l,o,e);if(m!==!0)throw new y("option "+o+" must be "+m,y.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new y("Unknown option "+o,y.ERR_BAD_OPTION)}}const Ne={assertOptions:oa,validators:Ie},W=Ne.validators;class Ae{constructor(t){this.defaults=t||{},this.interceptors={request:new Et,response:new Et}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=se(this.defaults,r);const{transitional:n,paramsSerializer:a,headers:o}=r;n!==void 0&&Ne.assertOptions(n,{silentJSONParsing:W.transitional(W.boolean),forcedJSONParsing:W.transitional(W.boolean),clarifyTimeoutError:W.transitional(W.boolean)},!1),a!=null&&(u.isFunction(a)?r.paramsSerializer={serialize:a}:Ne.assertOptions(a,{encode:W.function,serialize:W.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Ne.assertOptions(r,{baseUrl:W.spelling("baseURL"),withXsrfToken:W.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=o&&u.merge(o.common,o[r.method]);o&&u.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),r.headers=V.concat(i,o);const l=[];let m=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(r)===!1||(m=m&&x.synchronous,l.unshift(x.fulfilled,x.rejected))});const c=[];this.interceptors.response.forEach(function(x){c.push(x.fulfilled,x.rejected)});let d,f=0,g;if(!m){const p=[Ut.bind(this),void 0];for(p.unshift.apply(p,l),p.push.apply(p,c),g=p.length,d=Promise.resolve(r);f<g;)d=d.then(p[f++],p[f++]);return d}g=l.length;let v=r;for(f=0;f<g;){const p=l[f++],x=l[f++];try{v=p(v)}catch(h){x.call(this,h);break}}try{d=Ut.call(this,v)}catch(p){return Promise.reject(p)}for(f=0,g=c.length;f<g;)d=d.then(c[f++],c[f++]);return d}getUri(t){t=se(this.defaults,t);const r=ps(t.baseURL,t.url,t.allowAbsoluteUrls);return ds(r,t.params,t.paramsSerializer)}}u.forEach(["delete","get","head","options"],function(t){Ae.prototype[t]=function(r,n){return this.request(se(n||{},{method:t,url:r,data:(n||{}).data}))}});u.forEach(["post","put","patch"],function(t){function r(n){return function(o,i,l){return this.request(se(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Ae.prototype[t]=r(),Ae.prototype[t+"Form"]=r(!0)});const Se=Ae;class lt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(a=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](a);n._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(l=>{n.subscribe(l),o=l}).then(a);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,l){n.reason||(n.reason=new ce(o,i,l),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new lt(function(a){t=a}),cancel:t}}}const ia=lt;function la(e){return function(r){return e.apply(null,r)}}function ca(e){return u.isObject(e)&&e.isAxiosError===!0}const tt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(tt).forEach(([e,t])=>{tt[t]=e});const da=tt;function ws(e){const t=new Se(e),r=Zt(Se.prototype.request,t);return u.extend(r,Se.prototype,t,{allOwnKeys:!0}),u.extend(r,t,null,{allOwnKeys:!0}),r.create=function(a){return ws(se(e,a))},r}const _=ws(it);_.Axios=Se;_.CanceledError=ce;_.CancelToken=ia;_.isCancel=hs;_.VERSION=js;_.toFormData=Fe;_.AxiosError=y;_.Cancel=_.CanceledError;_.all=function(t){return Promise.all(t)};_.spread=la;_.isAxiosError=ca;_.mergeConfig=se;_.AxiosHeaders=V;_.formToJSON=e=>ms(u.isHTMLForm(e)?new FormData(e):e);_.getAdapter=ys.getAdapter;_.HttpStatusCode=da;_.default=_;const ua=_,ma={}.VITE_API_BASE_URL||"/api",q=ua.create({baseURL:ma,timeout:3e4,headers:{"Content-Type":"application/json"}});q.interceptors.request.use(e=>{const t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));q.interceptors.response.use(e=>e,e=>{var t;return((t=e.response)==null?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/login"),Promise.reject(e)});const vs=async()=>(await q.get("/v1/unified/health")).data,Mt=()=>{var l,m,c;const{data:e,isLoading:t,refetch:r}=Bt({queryKey:["health"],queryFn:vs,refetchInterval:3e4}),n=((l=e==null?void 0:e.databases)==null?void 0:l.filter(d=>d.status==="healthy").length)||0,a=((m=e==null?void 0:e.databases)==null?void 0:m.length)||0,o=a-n,i=d=>{switch(d){case"healthy":return s.jsxs(B,{variant:"success",className:"flex items-center gap-1",children:[s.jsx(pt,{className:"h-3 w-3"}),"정상"]});case"unhealthy":return s.jsxs(B,{variant:"destructive",className:"flex items-center gap-1",children:[s.jsx(pe,{className:"h-3 w-3"}),"비정상"]});default:return s.jsx(B,{variant:"outline",children:"알 수 없음"})}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"대시보드"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"Vector Database Manager 시스템 전체 현황을 모니터링합니다."})]}),s.jsxs(z,{onClick:()=>r(),disabled:t,variant:"outline",size:"sm",children:[s.jsx(It,{className:`h-4 w-4 mr-2 ${t?"animate-spin":""}`}),"새로고침"]})]}),s.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[s.jsxs(E,{children:[s.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(P,{className:"text-sm font-medium",children:"전체 데이터베이스"}),s.jsx(te,{className:"h-4 w-4 text-gray-500"})]}),s.jsxs(A,{children:[s.jsx("div",{className:"text-2xl font-bold",children:a}),s.jsx("p",{className:"text-xs text-gray-500",children:"총 연결된 데이터베이스 수"})]})]}),s.jsxs(E,{children:[s.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(P,{className:"text-sm font-medium",children:"정상 상태"}),s.jsx(pt,{className:"h-4 w-4 text-green-600"})]}),s.jsxs(A,{children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:n}),s.jsx("p",{className:"text-xs text-gray-500",children:"정상 작동 중인 데이터베이스"})]})]}),s.jsxs(E,{children:[s.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(P,{className:"text-sm font-medium",children:"문제 상태"}),s.jsx(pe,{className:"h-4 w-4 text-red-600"})]}),s.jsxs(A,{children:[s.jsx("div",{className:"text-2xl font-bold text-red-600",children:o}),s.jsx("p",{className:"text-xs text-gray-500",children:"문제가 있는 데이터베이스"})]})]}),s.jsxs(E,{children:[s.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(P,{className:"text-sm font-medium",children:"시스템 상태"}),s.jsx(Ce,{className:"h-4 w-4 text-gray-500"})]}),s.jsxs(A,{children:[s.jsxs("div",{className:"text-2xl font-bold",children:[a>0?Math.round(n/a*100):0,"%"]}),s.jsx(Qt,{value:a>0?n/a*100:0,className:"mt-2"})]})]})]}),s.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"데이터베이스 상태"}),s.jsx(M,{children:"각 데이터베이스의 실시간 상태를 확인합니다."})]}),s.jsx(A,{children:s.jsxs("div",{className:"space-y-4",children:[(c=e==null?void 0:e.databases)==null?void 0:c.map(d=>s.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:d.type==="vector"?"🔍":d.type==="search"?"🔎":"📄"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:d.name}),s.jsx("div",{className:"text-sm text-gray-500 capitalize",children:d.type})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[i(d.status),d.response_time&&s.jsxs("span",{className:"text-xs text-gray-500",children:[d.response_time.toFixed(0),"ms"]})]})]},d.name)),(!(e!=null&&e.databases)||e.databases.length===0)&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"연결된 데이터베이스가 없습니다."})]})})]}),s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"최근 활동"}),s.jsx(M,{children:"시스템의 최근 활동 내역입니다."})]}),s.jsx(A,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[s.jsx(Ce,{className:"h-8 w-8 text-blue-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"시스템 시작됨"}),s.jsx("div",{className:"text-sm text-gray-500",children:"Vector DB Manager가 시작되었습니다."})]})]}),s.jsxs("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[s.jsx(te,{className:"h-8 w-8 text-green-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"데이터베이스 연결 확인"}),s.jsx("div",{className:"text-sm text-gray-500",children:"모든 데이터베이스 연결 상태를 확인했습니다."})]})]})]})})]})]})]})},ha=()=>{var o,i,l,m;const{data:e,isLoading:t,refetch:r}=Bt({queryKey:["health"],queryFn:vs,refetchInterval:3e4}),n=c=>{switch(c){case"healthy":return s.jsx(B,{variant:"success",children:"정상"});case"unhealthy":return s.jsx(B,{variant:"destructive",children:"비정상"});default:return s.jsx(B,{variant:"outline",children:"알 수 없음"})}},a=c=>{switch(c){case"vector":return{icon:"🔍",label:"벡터 DB",color:"text-purple-600"};case"search":return{icon:"🔎",label:"검색 엔진",color:"text-blue-600"};case"document":return{icon:"📄",label:"문서 DB",color:"text-green-600"};default:return{icon:"💾",label:"데이터베이스",color:"text-gray-600"}}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"데이터베이스 관리"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"연결된 모든 데이터베이스의 상태를 관리하고 모니터링합니다."})]}),s.jsxs(z,{onClick:()=>r(),disabled:t,variant:"outline",size:"sm",children:[s.jsx(It,{className:`h-4 w-4 mr-2 ${t?"animate-spin":""}`}),"새로고침"]})]}),s.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:(o=e==null?void 0:e.databases)==null?void 0:o.map(c=>{const d=a(c.type);return s.jsxs(E,{className:"hover:shadow-md transition-all duration-200 border-gray-100",children:[s.jsx(O,{className:"pb-3",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"text-2xl",children:d.icon}),s.jsxs("div",{children:[s.jsx(P,{className:"text-lg text-gray-900",children:c.name}),s.jsx(M,{className:d.color,children:d.label})]})]}),n(c.status)]})}),s.jsxs(A,{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"URL:"}),s.jsx("code",{className:"text-xs bg-gray-50 text-gray-700 px-2 py-1 rounded",children:c.url})]}),c.response_time&&s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"응답 시간:"}),s.jsxs("span",{className:"font-medium text-gray-900",children:[c.response_time.toFixed(0),"ms"]})]}),c.error&&s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"오류:"}),s.jsx("p",{className:"text-red-600 text-xs mt-1 p-2 bg-red-50 rounded border-0",children:c.error})]})]}),s.jsxs("div",{className:"flex gap-2 pt-2",children:[s.jsxs(z,{variant:"outline",size:"sm",className:"flex-1",children:[s.jsx(Ce,{className:"h-4 w-4 mr-2"}),"상태 확인"]}),s.jsxs(z,{variant:"outline",size:"sm",className:"flex-1",children:[s.jsx(Re,{className:"h-4 w-4 mr-2"}),"설정"]})]})]})]},c.name)})}),(!(e!=null&&e.databases)||e.databases.length===0)&&s.jsx(E,{children:s.jsx(A,{className:"py-12",children:s.jsxs("div",{className:"text-center",children:[s.jsx(te,{className:"h-12 w-12 text-gray-500 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium mb-2",children:"연결된 데이터베이스가 없습니다"}),s.jsx("p",{className:"text-gray-500 mb-4",children:"설정에서 데이터베이스를 추가하여 시작하세요."}),s.jsxs(z,{children:[s.jsx(Re,{className:"h-4 w-4 mr-2"}),"설정으로 이동"]})]})})}),(e==null?void 0:e.databases)&&e.databases.length>0&&s.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"데이터베이스 통계"}),s.jsx(M,{children:"타입별 데이터베이스 분포"})]}),s.jsx(A,{children:s.jsx("div",{className:"space-y-3",children:["vector","search","document"].map(c=>{var g;const d=((g=e.databases)==null?void 0:g.filter(v=>v.type===c).length)||0,f=a(c);return d>0?s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-lg",children:f.icon}),s.jsx("span",{className:`font-medium ${f.color}`,children:f.label})]}),s.jsxs(B,{variant:"outline",children:[d,"개"]})]},c):null})})})]}),s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"연결 상태"}),s.jsx(M,{children:"데이터베이스별 연결 상태 요약"})]}),s.jsx(A,{children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-gray-500",children:"정상"}),s.jsx("div",{className:"flex items-center space-x-2",children:s.jsxs(B,{variant:"success",children:[((i=e.databases)==null?void 0:i.filter(c=>c.status==="healthy").length)||0,"개"]})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-gray-500",children:"비정상"}),s.jsx("div",{className:"flex items-center space-x-2",children:s.jsxs(B,{variant:"destructive",children:[((l=e.databases)==null?void 0:l.filter(c=>c.status==="unhealthy").length)||0,"개"]})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-gray-500",children:"평균 응답시간"}),s.jsx("span",{className:"font-medium",children:(m=e.databases)!=null&&m.length?Math.round(e.databases.filter(c=>c.response_time).reduce((c,d)=>c+(d.response_time||0),0)/e.databases.filter(c=>c.response_time).length)+"ms":"N/A"})]})]})})]})]})]})},ne=S.forwardRef(({className:e,type:t,...r},n)=>s.jsx("input",{type:t,className:U("flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 placeholder:text-gray-500 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 focus:outline-none transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50",e),ref:n,...r}));ne.displayName="Input";const ct=S.createContext(void 0),Ns=({defaultValue:e,value:t,onValueChange:r,children:n,className:a})=>{const[o,i]=S.useState(e||""),l=t??o,m=r??i;return s.jsx(ct.Provider,{value:{value:l,onValueChange:m},children:s.jsx("div",{className:a,children:n})})},dt=S.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:U("inline-flex h-10 items-center justify-center rounded-lg bg-gray-50 p-1 text-gray-500",e),...t}));dt.displayName="TabsList";const ae=S.forwardRef(({className:e,value:t,...r},n)=>{const a=S.useContext(ct);if(!a)throw new Error("TabsTrigger must be used within Tabs");const o=a.value===t;return s.jsx("button",{ref:n,className:U("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-opacity-50 disabled:pointer-events-none disabled:opacity-50",o?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900 hover:bg-gray-100",e),onClick:()=>a.onValueChange(t),...r})});ae.displayName="TabsTrigger";const oe=S.forwardRef(({className:e,value:t,...r},n)=>{const a=S.useContext(ct);if(!a)throw new Error("TabsContent must be used within Tabs");return a.value!==t?null:s.jsx("div",{ref:n,className:U("mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-opacity-50",e),...r})});oe.displayName="TabsContent";const fa=()=>{const[e,t]=S.useState(""),[r,n]=S.useState([]),[a,o]=S.useState(!1),[i,l]=S.useState(""),m=async()=>{if(e.trim()){o(!0);try{setTimeout(()=>{n([{id:1,database:"Elasticsearch",content:"검색 결과 예시 내용입니다.",score:.95,metadata:{type:"document"}},{id:2,database:"Meilisearch",content:"또 다른 검색 결과입니다.",score:.87,metadata:{type:"article"}}]),o(!1)},1e3)}catch{o(!1)}}},c=async()=>{if(i.trim()){o(!0);try{setTimeout(()=>{n([{id:1,database:"Weaviate",content:"벡터 유사도 검색 결과입니다.",score:.92,similarity:.92,metadata:{type:"vector"}},{id:2,database:"Qdrant",content:"유사한 벡터 데이터를 찾았습니다.",score:.88,similarity:.88,metadata:{type:"embedding"}}]),o(!1)},1e3)}catch{o(!1)}}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"검색 콘솔"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"통합 검색 및 벡터 유사도 검색을 수행할 수 있습니다."})]}),s.jsxs(Ns,{defaultValue:"text",className:"space-y-6",children:[s.jsxs(dt,{className:"grid w-full grid-cols-2",children:[s.jsxs(ae,{value:"text",className:"flex items-center gap-2",children:[s.jsx(Ee,{className:"h-4 w-4"}),"텍스트 검색"]}),s.jsxs(ae,{value:"vector",className:"flex items-center gap-2",children:[s.jsx(te,{className:"h-4 w-4"}),"벡터 검색"]})]}),s.jsx(oe,{value:"text",className:"space-y-6",children:s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsxs(P,{className:"flex items-center gap-2",children:[s.jsx(Ee,{className:"h-5 w-5"}),"텍스트 검색"]}),s.jsx(M,{children:"연결된 모든 검색 엔진에서 텍스트를 검색합니다."})]}),s.jsxs(A,{className:"space-y-4",children:[s.jsxs("div",{className:"flex gap-2",children:[s.jsx(ne,{placeholder:"검색할 텍스트를 입력하세요...",value:e,onChange:d=>t(d.target.value),onKeyDown:d=>d.key==="Enter"&&m(),className:"flex-1"}),s.jsx(z,{onClick:m,disabled:a||!e.trim(),children:a?"검색 중...":"검색"})]}),s.jsxs("div",{className:"flex gap-2 text-sm text-gray-500",children:[s.jsx(xt,{className:"h-4 w-4"}),s.jsx("span",{children:"검색 옵션: 전체 데이터베이스, 결과 수 제한 10개"})]})]})]})}),s.jsx(oe,{value:"vector",className:"space-y-6",children:s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsxs(P,{className:"flex items-center gap-2",children:[s.jsx(te,{className:"h-5 w-5"}),"벡터 유사도 검색"]}),s.jsx(M,{children:"벡터 데이터베이스에서 유사한 벡터를 검색합니다."})]}),s.jsxs(A,{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"벡터 입력 (쉼표로 구분)"}),s.jsx(ne,{placeholder:"예: 0.1, 0.2, 0.3, 0.4, 0.5...",value:i,onChange:d=>l(d.target.value),className:"font-mono text-sm"})]}),s.jsx("div",{className:"flex gap-2",children:s.jsx(z,{onClick:c,disabled:a||!i.trim(),className:"flex-1",children:a?"검색 중...":"벡터 검색"})}),s.jsxs("div",{className:"flex gap-2 text-sm text-gray-500",children:[s.jsx(xt,{className:"h-4 w-4"}),s.jsx("span",{children:"유사도 임계값: 0.8, 최대 결과: 10개"})]})]})]})})]}),r.length>0&&s.jsxs(E,{children:[s.jsx(O,{children:s.jsxs(P,{className:"flex items-center justify-between",children:[s.jsx("span",{children:"검색 결과"}),s.jsxs(B,{variant:"outline",children:[r.length,"개 결과"]})]})}),s.jsx(A,{children:s.jsx("div",{className:"space-y-4",children:r.map(d=>s.jsxs("div",{className:"border rounded-lg p-4 space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(B,{variant:"outline",children:d.database}),d.metadata.type&&s.jsx(B,{variant:"secondary",children:d.metadata.type})]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[d.similarity&&s.jsxs("span",{children:["유사도: ",(d.similarity*100).toFixed(1),"%"]}),s.jsxs("span",{children:["점수: ",d.score.toFixed(2)]})]})]}),s.jsx("div",{className:"text-sm",children:s.jsx("p",{children:d.content})}),s.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[s.jsx(As,{className:"h-3 w-3"}),s.jsx("span",{children:"방금 전"})]})]},d.id))})})]}),s.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"최근 검색"}),s.jsx(M,{children:"최근에 수행한 검색 쿼리들입니다."})]}),s.jsx(A,{children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between p-2 hover:bg-muted rounded",children:[s.jsx("span",{className:"text-sm",children:"machine learning"}),s.jsx("span",{className:"text-xs text-gray-500",children:"5분 전"})]}),s.jsxs("div",{className:"flex items-center justify-between p-2 hover:bg-muted rounded",children:[s.jsx("span",{className:"text-sm",children:"vector database"}),s.jsx("span",{className:"text-xs text-gray-500",children:"1시간 전"})]}),s.jsxs("div",{className:"flex items-center justify-between p-2 hover:bg-muted rounded",children:[s.jsx("span",{className:"text-sm",children:"neural networks"}),s.jsx("span",{className:"text-xs text-gray-500",children:"2시간 전"})]})]})})]}),s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"검색 통계"}),s.jsx(M,{children:"오늘의 검색 활동 요약입니다."})]}),s.jsx(A,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"총 검색 수"}),s.jsx("span",{className:"font-medium",children:"127"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"텍스트 검색"}),s.jsx("span",{className:"font-medium",children:"89"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"벡터 검색"}),s.jsx("span",{className:"font-medium",children:"38"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"평균 응답시간"}),s.jsx("span",{className:"font-medium",children:"245ms"})]})]})})]})]})]})},J=S.forwardRef(({className:e,checked:t=!1,onCheckedChange:r,disabled:n,...a},o)=>s.jsx("button",{type:"button",role:"switch","aria-checked":t,disabled:n,className:U("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-opacity-50 disabled:cursor-not-allowed disabled:opacity-50",t?"bg-indigo-600":"bg-gray-200 hover:bg-gray-300",e),onClick:()=>r==null?void 0:r(!t),ref:o,...a,children:s.jsx("span",{className:U("pointer-events-none block h-5 w-5 rounded-full bg-white shadow-sm transition-transform duration-200",t?"translate-x-5":"translate-x-0")})}));J.displayName="Switch";const pa=()=>{const[e,t]=S.useState({api:{baseUrl:"http://localhost:7200",timeout:30,retries:3},databases:{weaviate:{url:"http://localhost:7210",enabled:!0},qdrant:{url:"http://localhost:7211",enabled:!0},chroma:{url:"http://localhost:7212",enabled:!0},elasticsearch:{url:"http://localhost:7213",enabled:!0},meilisearch:{url:"http://localhost:7214",enabled:!0},mongodb:{url:"mongodb://localhost:7215",enabled:!0}},notifications:{healthCheck:!0,errors:!0,performance:!1}}),r=(i,l,m)=>{t(c=>({...c,[i]:{...c[i],[l]:m}}))},n=(i,l)=>{t(m=>({...m,databases:{...m.databases,[i]:{...m.databases[i],enabled:l}}}))},a=()=>{console.log("Saving settings:",e)},o=async i=>{console.log("Testing connection for:",i)};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"설정"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"시스템 설정 및 데이터베이스 연결을 관리합니다."})]}),s.jsxs(z,{onClick:a,className:"flex items-center gap-2",children:[s.jsx(Os,{className:"h-4 w-4"}),"설정 저장"]})]}),s.jsxs(Ns,{defaultValue:"databases",className:"space-y-6",children:[s.jsxs(dt,{className:"grid w-full grid-cols-3",children:[s.jsxs(ae,{value:"databases",className:"flex items-center gap-2",children:[s.jsx(te,{className:"h-4 w-4"}),"데이터베이스"]}),s.jsxs(ae,{value:"api",className:"flex items-center gap-2",children:[s.jsx(Re,{className:"h-4 w-4"}),"API 설정"]}),s.jsxs(ae,{value:"notifications",className:"flex items-center gap-2",children:[s.jsx(zt,{className:"h-4 w-4"}),"알림"]})]}),s.jsx(oe,{value:"databases",className:"space-y-6",children:s.jsxs("div",{className:"grid gap-6",children:[s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"벡터 데이터베이스"}),s.jsx(M,{children:"벡터 검색을 지원하는 데이터베이스들의 연결 설정입니다."})]}),s.jsx(A,{className:"space-y-4",children:["weaviate","qdrant","chroma"].map(i=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:"🔍"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium capitalize",children:i}),s.jsx("div",{className:"text-sm text-gray-500",children:e.databases[i].url})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(J,{checked:e.databases[i].enabled,onCheckedChange:l=>n(i,l)}),s.jsxs(z,{variant:"outline",size:"sm",onClick:()=>o(i),children:[s.jsx($e,{className:"h-4 w-4 mr-2"}),"테스트"]})]})]},i))})]}),s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"검색 엔진"}),s.jsx(M,{children:"텍스트 검색을 지원하는 검색 엔진들의 연결 설정입니다."})]}),s.jsx(A,{className:"space-y-4",children:["elasticsearch","meilisearch"].map(i=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:"🔎"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium capitalize",children:i}),s.jsx("div",{className:"text-sm text-gray-500",children:e.databases[i].url})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(J,{checked:e.databases[i].enabled,onCheckedChange:l=>n(i,l)}),s.jsxs(z,{variant:"outline",size:"sm",onClick:()=>o(i),children:[s.jsx($e,{className:"h-4 w-4 mr-2"}),"테스트"]})]})]},i))})]}),s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"문서 데이터베이스"}),s.jsx(M,{children:"문서 저장소로 사용되는 데이터베이스들의 연결 설정입니다."})]}),s.jsx(A,{className:"space-y-4",children:["mongodb"].map(i=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:"📄"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium capitalize",children:i}),s.jsx("div",{className:"text-sm text-gray-500",children:e.databases[i].url})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(J,{checked:e.databases[i].enabled,onCheckedChange:l=>n(i,l)}),s.jsxs(z,{variant:"outline",size:"sm",onClick:()=>o(i),children:[s.jsx($e,{className:"h-4 w-4 mr-2"}),"테스트"]})]})]},i))})]})]})}),s.jsxs(oe,{value:"api",className:"space-y-6",children:[s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"API 연결 설정"}),s.jsx(M,{children:"백엔드 API 서버와의 연결 설정을 관리합니다."})]}),s.jsxs(A,{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"Base URL"}),s.jsx(ne,{value:e.api.baseUrl,onChange:i=>r("api","baseUrl",i.target.value),placeholder:"http://localhost:7200"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"타임아웃 (초)"}),s.jsx(ne,{type:"number",value:e.api.timeout,onChange:i=>r("api","timeout",parseInt(i.target.value))})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"재시도 횟수"}),s.jsx(ne,{type:"number",value:e.api.retries,onChange:i=>r("api","retries",parseInt(i.target.value))})]})]})]})]}),s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"보안 설정"}),s.jsx(M,{children:"API 인증 및 보안 관련 설정입니다."})]}),s.jsxs(A,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"API 키 인증"}),s.jsx("div",{className:"text-sm text-gray-500",children:"API 요청에 인증 키를 포함합니다."})]}),s.jsx(J,{})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"HTTPS 강제"}),s.jsx("div",{className:"text-sm text-gray-500",children:"모든 API 요청을 HTTPS로 강제합니다."})]}),s.jsx(J,{})]})]})]})]}),s.jsx(oe,{value:"notifications",className:"space-y-6",children:s.jsxs(E,{children:[s.jsxs(O,{children:[s.jsx(P,{children:"알림 설정"}),s.jsx(M,{children:"시스템 이벤트에 대한 알림을 설정합니다."})]}),s.jsxs(A,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"헬스체크 알림"}),s.jsx("div",{className:"text-sm text-gray-500",children:"데이터베이스 연결 상태 변경 시 알림을 받습니다."})]}),s.jsx(J,{checked:e.notifications.healthCheck,onCheckedChange:i=>r("notifications","healthCheck",i)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"오류 알림"}),s.jsx("div",{className:"text-sm text-gray-500",children:"시스템 오류 발생 시 즉시 알림을 받습니다."})]}),s.jsx(J,{checked:e.notifications.errors,onCheckedChange:i=>r("notifications","errors",i)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:"성능 알림"}),s.jsx("div",{className:"text-sm text-gray-500",children:"응답 시간 등 성능 이슈 발생 시 알림을 받습니다."})]}),s.jsx(J,{checked:e.notifications.performance,onCheckedChange:i=>r("notifications","performance",i)})]})]})]})})]})]})};class xa{constructor(){Q(this,"baseUrl","/api/v1/ollama")}async healthCheck(){return(await q.get(`${this.baseUrl}/health`)).data}async listModels(){return(await q.get(`${this.baseUrl}/models`)).data}async extractMetadata(t){return(await q.post(`${this.baseUrl}/extract`,t)).data}async extractMetadataBatch(t){return(await q.post(`${this.baseUrl}/extract/batch`,t)).data}async createEmbedding(t,r="nomic-embed-text"){return(await q.post(`${this.baseUrl}/embedding`,null,{params:{text:t,model:r}})).data}async rerankDocuments(t,r,n="bge-reranker-base"){return(await q.post(`${this.baseUrl}/rerank`,r,{params:{query:t,model:n}})).data}async getQueueStatus(){return(await q.get(`${this.baseUrl}/queue/status`)).data}async getWebSocketStats(){return(await q.get(`${this.baseUrl}/websocket/stats`)).data}async testConnection(){return(await q.post(`${this.baseUrl}/test/connection`)).data}async getInfo(){return(await q.get(`${this.baseUrl}/`)).data}}class ga{constructor(){Q(this,"ws",null);Q(this,"url");Q(this,"reconnectAttempts",0);Q(this,"maxReconnectAttempts",5);Q(this,"reconnectDelay",1e3);Q(this,"listeners",new Map);const t=window.location.protocol==="https:"?"wss:":"ws:",r=window.location.host;this.url=`${t}//${r}/api/v1/ollama/ws`}connect(){return new Promise((t,r)=>{try{this.ws=new WebSocket(this.url),this.ws.onopen=()=>{console.log("Ollama WebSocket connected"),this.reconnectAttempts=0,this.emit("connected",{}),t()},this.ws.onmessage=n=>{try{const a=JSON.parse(n.data);this.emit(a.type||"message",a)}catch(a){console.error("Failed to parse WebSocket message:",a)}},this.ws.onclose=()=>{console.log("Ollama WebSocket disconnected"),this.emit("disconnected",{}),this.attemptReconnect()},this.ws.onerror=n=>{console.error("Ollama WebSocket error:",n),this.emit("error",{error:n}),r(n)}}catch(n){r(n)}})}disconnect(){this.ws&&(this.ws.close(),this.ws=null)}send(t){this.ws&&this.ws.readyState===WebSocket.OPEN?this.ws.send(JSON.stringify(t)):console.warn("WebSocket is not connected")}on(t,r){this.listeners.has(t)||this.listeners.set(t,[]),this.listeners.get(t).push(r)}off(t,r){const n=this.listeners.get(t);if(n){const a=n.indexOf(r);a>-1&&n.splice(a,1)}}emit(t,r){const n=this.listeners.get(t);n&&n.forEach(a=>a(r))}attemptReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.connect().catch(t=>{console.error("Reconnection failed:",t)})},this.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.emit("max_reconnect_attempts",{}))}ping(){this.send({type:"ping"})}requestStatus(){this.send({type:"request_status"})}isConnected(){return this.ws!==null&&this.ws.readyState===WebSocket.OPEN}}const je=new xa,fe=new ga,ba=()=>{const[e,t]=S.useState({text:"",model:"gemma3:1b",extract_summary:!0}),[r,n]=S.useState(!1),[a,o]=S.useState(null),[i,l]=S.useState(null),[m,c]=S.useState({ollama_connected:!1,websocket_connected:!1,last_check:""}),[d,f]=S.useState([]),[g,v]=S.useState(null);S.useEffect(()=>(p(),()=>{fe.disconnect()}),[]);const p=async()=>{try{const b=await je.healthCheck();c(F=>({...F,ollama_connected:b.data.overall_healthy,last_check:new Date().toISOString()}));const T=await je.listModels();f(T.data.models);const $=await je.getQueueStatus();v($.data),fe.on("connected",()=>{c(F=>({...F,websocket_connected:!0}))}),fe.on("disconnected",()=>{c(F=>({...F,websocket_connected:!1}))}),fe.on("ollama_update",F=>{var G;(G=F.data)!=null&&G.queue&&v(F.data.queue)}),await fe.connect()}catch(b){console.error("Failed to initialize connection:",b),l("Ollama 서비스 연결에 실패했습니다.")}},x=b=>{t(T=>({...T,text:b})),l(null)},h=async()=>{try{const b=await navigator.clipboard.readText();b&&x(b)}catch{l("클립보드 접근 권한이 필요합니다.")}},j=()=>{t({text:"",model:"gemma3:1b",extract_summary:!0}),o(null),l(null)},N=async()=>{if(!e.text.trim()){l("텍스트를 입력해주세요.");return}n(!0),l(null);try{const b=await je.extractMetadata({content:e.text,model:e.model,extract_summary:e.extract_summary});b.success?(o(b),t(T=>({...T,text:""}))):l(b.message||"메타데이터 추출에 실패했습니다.")}catch(b){l("API 요청 중 오류가 발생했습니다."),console.error("Submit error:",b)}finally{n(!1)}},C=()=>m.ollama_connected&&m.websocket_connected?s.jsx(bt,{className:"h-4 w-4 text-green-600"}):m.ollama_connected?s.jsx(bt,{className:"h-4 w-4 text-yellow-600"}):s.jsx(Ms,{className:"h-4 w-4 text-red-600"}),L=()=>m.ollama_connected&&m.websocket_connected?"연결됨":m.ollama_connected?"HTTP만 연결됨":"연결 안됨";return s.jsxs("div",{className:"p-6 max-w-4xl mx-auto space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"텍스트 메타데이터 추출"}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[C(),s.jsx("span",{className:"text-sm text-gray-600",children:L()})]}),s.jsx(B,{variant:"outline",className:"text-sm",children:"Ollama 메타데이터 추출"})]})]}),g&&s.jsx(E,{className:"p-4 bg-blue-50",children:s.jsxs("div",{className:"flex items-center gap-4 text-sm",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(gt,{className:"h-4 w-4 text-blue-600"}),s.jsx("span",{className:"font-medium",children:"큐 상태:"})]}),s.jsxs("span",{children:["대기: ",g.pending]}),s.jsxs("span",{children:["처리중: ",g.processing]}),s.jsxs("span",{children:["완료: ",g.completed]}),g.failed>0&&s.jsxs("span",{className:"text-red-600",children:["실패: ",g.failed]})]})}),s.jsx(E,{className:"p-6",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"텍스트 내용 *"}),s.jsxs("div",{className:"relative",children:[s.jsx("textarea",{value:e.text,onChange:b=>x(b.target.value),placeholder:"메타데이터를 추출할 텍스트를 여기에 입력하거나 붙여넣으세요...",className:"w-full min-h-[200px] resize-y px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),s.jsxs("div",{className:"absolute top-2 right-2 flex gap-2",children:[s.jsx(z,{type:"button",variant:"ghost",size:"sm",onClick:h,className:"h-8 w-8 p-0",title:"클립보드에서 붙여넣기",children:s.jsx(Ps,{className:"h-4 w-4"})}),s.jsx(z,{type:"button",variant:"ghost",size:"sm",onClick:j,className:"h-8 w-8 p-0",title:"모두 지우기",children:s.jsx(_s,{className:"h-4 w-4"})})]})]}),e.text&&s.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[e.text.split(`
`).length,"줄, ",e.text.length,"자"]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"사용할 모델"}),s.jsx("select",{value:e.model,onChange:b=>t(T=>({...T,model:b.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:d.length>0?d.map(b=>s.jsx("option",{value:b,children:b},b)):s.jsx("option",{value:"gemma3:1b",children:"gemma3:1b (기본값)"})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"요약 포함"}),s.jsx(J,{checked:e.extract_summary,onCheckedChange:b=>t(T=>({...T,extract_summary:b}))})]})]}),i&&s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(pe,{className:"h-4 w-4 text-red-600 mr-2"}),s.jsx("p",{className:"text-sm text-red-800",children:i})]})}),s.jsxs("div",{className:"flex gap-3",children:[s.jsxs(z,{onClick:N,disabled:r||!e.text.trim(),className:"flex items-center gap-2",children:[r?s.jsx(Ls,{className:"h-4 w-4 animate-spin"}):s.jsx(Us,{className:"h-4 w-4"}),r?"처리 중...":"메타데이터 추출"]}),s.jsx(z,{type:"button",variant:"outline",onClick:j,children:"초기화"})]})]})}),a&&s.jsxs(E,{className:"p-6",children:[s.jsxs("h2",{className:"text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[s.jsx(gt,{className:"h-5 w-5"}),"추출된 메타데이터"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"처리 상태"}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsxs(B,{variant:a.success?"default":"destructive",children:[a.success?s.jsx(Fs,{className:"h-4 w-4"}):s.jsx(pe,{className:"h-4 w-4"}),a.success?"성공":"실패"]})})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"처리 시간"}),s.jsx("p",{className:"text-sm text-gray-900",children:new Date(a.timestamp).toLocaleString()})]})]}),a.success&&a.data&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"제목"}),s.jsx("p",{className:"text-lg font-medium text-gray-900",children:a.data.title})]}),a.data.category&&s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"카테고리"}),s.jsx(B,{variant:"outline",children:a.data.category})]}),a.data.keywords&&a.data.keywords.length>0&&s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"키워드"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:a.data.keywords.map((b,T)=>s.jsx(B,{variant:"secondary",children:b},T))})]}),a.data.summary&&s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"요약"}),s.jsx("p",{className:"text-sm text-gray-700 bg-blue-50 rounded-md p-3",children:a.data.summary})]}),a.data.metadata&&s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-600",children:"추가 메타데이터"}),s.jsx("pre",{className:"text-xs text-gray-600 bg-gray-50 rounded-md p-3 overflow-auto",children:JSON.stringify(a.data.metadata,null,2)})]})]}),!a.success&&a.message&&s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(pe,{className:"h-4 w-4 text-red-600 mr-2"}),s.jsx("p",{className:"text-sm text-red-800",children:a.message})]})})]})]})]})},ya=({onMenuClick:e})=>s.jsx("header",{className:"fixed top-0 left-0 right-0 z-50 bg-white",children:s.jsxs("div",{className:"flex items-center justify-between h-16 px-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{onClick:e,onMouseEnter:t=>{t.currentTarget.style.backgroundColor="rgb(238, 242, 255)",t.currentTarget.style.color="rgb(67, 56, 202)"},onMouseLeave:t=>{t.currentTarget.style.backgroundColor="transparent",t.currentTarget.style.color="rgb(75, 85, 99)"},style:{color:"rgb(75, 85, 99)"},className:"lg:hidden w-10 h-10 flex items-center justify-center rounded-md transition-colors",children:s.jsx(Bs,{className:"h-6 w-6"})}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-sm",children:"VDB"})}),s.jsx("h1",{className:"text-xl font-semibold text-gray-900 hidden sm:block",children:"Vector DB Manager"})]})]}),s.jsx("div",{className:"hidden md:flex flex-1 max-w-md mx-8",children:s.jsxs("div",{className:"relative w-full",children:[s.jsx(Ee,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"검색...",className:"w-full pl-10 pr-4 py-2 bg-gray-50 rounded-lg border-0 focus:bg-white focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-all duration-200"})]})}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsxs("button",{className:"relative w-10 h-10 flex items-center justify-center rounded-md transition-colors",style:{color:"rgb(75, 85, 99)"},onMouseEnter:t=>{t.currentTarget.style.backgroundColor="rgb(238, 242, 255)",t.currentTarget.style.color="rgb(67, 56, 202)"},onMouseLeave:t=>{t.currentTarget.style.backgroundColor="transparent",t.currentTarget.style.color="rgb(75, 85, 99)"},children:[s.jsx(zt,{className:"h-5 w-5"}),s.jsx("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center",children:"3"})]}),s.jsx("button",{className:"w-10 h-10 flex items-center justify-center rounded-md transition-colors",style:{color:"rgb(75, 85, 99)"},onMouseEnter:t=>{t.currentTarget.style.backgroundColor="rgb(238, 242, 255)",t.currentTarget.style.color="rgb(67, 56, 202)"},onMouseLeave:t=>{t.currentTarget.style.backgroundColor="transparent",t.currentTarget.style.color="rgb(75, 85, 99)"},children:s.jsx(Is,{className:"h-5 w-5"})}),s.jsxs("div",{className:"flex items-center space-x-2 ml-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full"}),s.jsx("span",{className:"text-sm font-medium text-gray-700 hidden sm:block",children:"Admin"})]})]})]})}),ja=({isOpen:e,onClose:t,currentPage:r,onPageChange:n,collapsed:a=!1,onToggleCollapse:o})=>{const i=[{id:"dashboard",name:"대시보드",icon:Ce},{id:"databases",name:"데이터베이스",icon:te},{id:"input",name:"입력",icon:$s},{id:"search",name:"검색 콘솔",icon:Ee},{id:"settings",name:"설정",icon:Re}];return s.jsxs(s.Fragment,{children:[e&&s.jsx("div",{className:"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden",onClick:t}),s.jsxs("div",{className:U("fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] bg-white transition-all duration-300 ease-in-out","lg:translate-x-0",e?"translate-x-0":"-translate-x-full",a?"lg:w-16":"lg:w-64","w-64"),children:[s.jsx("div",{className:"absolute top-4 right-4 lg:hidden",children:s.jsx("button",{onClick:t,style:{color:"rgb(107, 114, 128)"},onMouseEnter:l=>{l.currentTarget.style.backgroundColor="rgb(238, 242, 255)",l.currentTarget.style.color="rgb(67, 56, 202)"},onMouseLeave:l=>{l.currentTarget.style.backgroundColor="transparent",l.currentTarget.style.color="rgb(107, 114, 128)"},className:"h-10 w-10 flex items-center justify-center rounded-md transition-colors",children:s.jsx(zs,{className:"h-5 w-5"})})}),o&&s.jsx("div",{className:"absolute -right-3 top-8 hidden lg:block",children:s.jsx("button",{onClick:o,style:{color:"rgb(107, 114, 128)",backgroundColor:"white"},onMouseEnter:l=>{l.currentTarget.style.backgroundColor="rgb(238, 242, 255)",l.currentTarget.style.color="rgb(67, 56, 202)"},onMouseLeave:l=>{l.currentTarget.style.backgroundColor="white",l.currentTarget.style.color="rgb(107, 114, 128)"},className:"h-6 w-6 rounded-full flex items-center justify-center transition-colors",children:a?s.jsx(Ds,{className:"h-3 w-3"}):s.jsx(qs,{className:"h-3 w-3"})})}),s.jsx("nav",{className:"p-4",children:s.jsx("div",{className:"space-y-1",children:i.map(l=>{const m=l.icon,c=r===l.id;return s.jsxs("button",{onClick:()=>n(l.id),style:{backgroundColor:c?"rgb(238, 242, 255)":"transparent",color:c?"rgb(67, 56, 202)":"rgb(75, 85, 99)"},onMouseEnter:d=>{c||(d.currentTarget.style.backgroundColor="rgb(238, 242, 255)",d.currentTarget.style.color="rgb(67, 56, 202)")},onMouseLeave:d=>{c||(d.currentTarget.style.backgroundColor="transparent",d.currentTarget.style.color="rgb(75, 85, 99)")},className:U("w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 border-0 outline-none",a&&"lg:justify-center lg:px-2"),title:a?l.name:void 0,children:[s.jsx(m,{className:U("h-5 w-5 flex-shrink-0 transition-colors duration-200",!a&&"mr-3"),style:{color:c?"rgb(67, 56, 202)":"rgb(107, 114, 128)"}}),!a&&s.jsx("span",{className:"truncate",children:l.name})]},l.id)})})}),!a&&s.jsx("div",{className:"absolute bottom-4 left-4 right-4",children:s.jsxs("div",{className:"bg-indigo-50 rounded-lg p-3",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsx("span",{className:"text-xs text-indigo-700 font-medium",children:"시스템 정상"})]}),s.jsx("div",{className:"text-xs text-indigo-600 mt-1",children:"5/6 데이터베이스 연결됨"})]})})]})]})},wa=({children:e,sidebarCollapsed:t=!1})=>s.jsx("main",{className:U("fixed top-16 right-0 bottom-0 transition-all duration-300 ease-in-out bg-gray-50",t?"lg:left-16":"lg:left-64","left-0"),children:s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"p-6",children:e})})}),va=({children:e,currentPage:t,onPageChange:r})=>{const[n,a]=S.useState(!1),[o,i]=S.useState(!1),l=()=>{a(!n)},m=()=>{a(!1)},c=()=>{i(!o)},d=f=>{r(f),window.innerWidth<1024&&a(!1)};return s.jsxs("div",{className:"h-screen overflow-hidden bg-gray-50",children:[s.jsx(ya,{onMenuClick:l}),s.jsx(ja,{isOpen:n,onClose:m,currentPage:t,onPageChange:d,collapsed:o,onToggleCollapse:c}),s.jsx(wa,{sidebarCollapsed:o,children:e})]})},Na=()=>{const[e,t]=S.useState("dashboard"),r=a=>{t(a)},n=()=>{switch(e){case"dashboard":return s.jsx(Mt,{});case"databases":return s.jsx(ha,{});case"input":return s.jsx(ba,{});case"search":return s.jsx(fa,{});case"settings":return s.jsx(pa,{});default:return s.jsx(Mt,{})}};return s.jsx(va,{currentPage:e,onPageChange:r,children:n()})};const Sa=new ks({defaultOptions:{queries:{staleTime:1e3*60*5,cacheTime:1e3*60*10,retry:2,refetchOnWindowFocus:!1}}});Ge.createRoot(document.getElementById("root")).render(s.jsx(Es.StrictMode,{children:s.jsx(Ts,{client:Sa,children:s.jsx(Na,{})})}));
//# sourceMappingURL=index-60adc9ee.js.map
