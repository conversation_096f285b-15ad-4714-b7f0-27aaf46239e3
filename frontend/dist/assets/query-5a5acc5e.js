import{r as P}from"./vendor-b1791c80.js";class T{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){const t={listener:e};return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}const A=typeof window>"u"||"Deno"in window;function w(){}function Re(s,e){return typeof s=="function"?s(e):s}function G(s){return typeof s=="number"&&s>=0&&s!==1/0}function oe(s,e){return Math.max(s+(e||0)-Date.now(),0)}function I(s,e,t){return k(s)?typeof e=="function"?{...t,queryKey:s,queryFn:e}:{...e,queryKey:s}:s}function q(s,e,t){return k(s)?[{...e,queryKey:s},t]:[s||{},e]}function Z(s,e){const{type:t="all",exact:r,fetchStatus:i,predicate:n,queryKey:u,stale:a}=s;if(k(u)){if(r){if(e.queryHash!==J(u,e.options))return!1}else if(!K(e.queryKey,u))return!1}if(t!=="all"){const l=e.isActive();if(t==="active"&&!l||t==="inactive"&&l)return!1}return!(typeof a=="boolean"&&e.isStale()!==a||typeof i<"u"&&i!==e.state.fetchStatus||n&&!n(e))}function $(s,e){const{exact:t,fetching:r,predicate:i,mutationKey:n}=s;if(k(n)){if(!e.options.mutationKey)return!1;if(t){if(D(e.options.mutationKey)!==D(n))return!1}else if(!K(e.options.mutationKey,n))return!1}return!(typeof r=="boolean"&&e.state.status==="loading"!==r||i&&!i(e))}function J(s,e){return((e==null?void 0:e.queryKeyHashFn)||D)(s)}function D(s){return JSON.stringify(s,(e,t)=>V(t)?Object.keys(t).sort().reduce((r,i)=>(r[i]=t[i],r),{}):t)}function K(s,e){return le(s,e)}function le(s,e){return s===e?!0:typeof s!=typeof e?!1:s&&e&&typeof s=="object"&&typeof e=="object"?!Object.keys(e).some(t=>!le(s[t],e[t])):!1}function ce(s,e){if(s===e)return s;const t=ee(s)&&ee(e);if(t||V(s)&&V(e)){const r=t?s.length:Object.keys(s).length,i=t?e:Object.keys(e),n=i.length,u=t?[]:{};let a=0;for(let l=0;l<n;l++){const y=t?l:i[l];u[y]=ce(s[y],e[y]),u[y]===s[y]&&a++}return r===n&&a===r?s:u}return e}function _(s,e){if(s&&!e||e&&!s)return!1;for(const t in s)if(s[t]!==e[t])return!1;return!0}function ee(s){return Array.isArray(s)&&s.length===Object.keys(s).length}function V(s){if(!te(s))return!1;const e=s.constructor;if(typeof e>"u")return!0;const t=e.prototype;return!(!te(t)||!t.hasOwnProperty("isPrototypeOf"))}function te(s){return Object.prototype.toString.call(s)==="[object Object]"}function k(s){return Array.isArray(s)}function he(s){return new Promise(e=>{setTimeout(e,s)})}function se(s){he(0).then(s)}function Oe(){if(typeof AbortController=="function")return new AbortController}function z(s,e,t){return t.isDataEqual!=null&&t.isDataEqual(s,e)?s:typeof t.structuralSharing=="function"?t.structuralSharing(s,e):t.structuralSharing!==!1?ce(s,e):e}class Se extends T{constructor(){super(),this.setup=e=>{if(!A&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),window.addEventListener("focus",t,!1),()=>{window.removeEventListener("visibilitychange",t),window.removeEventListener("focus",t)}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){if(!this.hasListeners()){var e;(e=this.cleanup)==null||e.call(this),this.cleanup=void 0}}setEventListener(e){var t;this.setup=e,(t=this.cleanup)==null||t.call(this),this.cleanup=e(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()})}setFocused(e){this.focused!==e&&(this.focused=e,this.onFocus())}onFocus(){this.listeners.forEach(({listener:e})=>{e()})}isFocused(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)}}const L=new Se,re=["online","offline"];class Pe extends T{constructor(){super(),this.setup=e=>{if(!A&&window.addEventListener){const t=()=>e();return re.forEach(r=>{window.addEventListener(r,t,!1)}),()=>{re.forEach(r=>{window.removeEventListener(r,t)})}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){if(!this.hasListeners()){var e;(e=this.cleanup)==null||e.call(this),this.cleanup=void 0}}setEventListener(e){var t;this.setup=e,(t=this.cleanup)==null||t.call(this),this.cleanup=e(r=>{typeof r=="boolean"?this.setOnline(r):this.onOnline()})}setOnline(e){this.online!==e&&(this.online=e,this.onOnline())}onOnline(){this.listeners.forEach(({listener:e})=>{e()})}isOnline(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine}}const j=new Pe;function Fe(s){return Math.min(1e3*2**s,3e4)}function N(s){return(s??"online")==="online"?j.isOnline():!0}class de{constructor(e){this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}}function U(s){return s instanceof de}function fe(s){let e=!1,t=0,r=!1,i,n,u;const a=new Promise((c,b)=>{n=c,u=b}),l=c=>{r||(f(new de(c)),s.abort==null||s.abort())},y=()=>{e=!0},o=()=>{e=!1},d=()=>!L.isFocused()||s.networkMode!=="always"&&!j.isOnline(),m=c=>{r||(r=!0,s.onSuccess==null||s.onSuccess(c),i==null||i(),n(c))},f=c=>{r||(r=!0,s.onError==null||s.onError(c),i==null||i(),u(c))},g=()=>new Promise(c=>{i=b=>{const F=r||!d();return F&&c(b),F},s.onPause==null||s.onPause()}).then(()=>{i=void 0,r||s.onContinue==null||s.onContinue()}),R=()=>{if(r)return;let c;try{c=s.fn()}catch(b){c=Promise.reject(b)}Promise.resolve(c).then(m).catch(b=>{var F,C;if(r)return;const O=(F=s.retry)!=null?F:3,E=(C=s.retryDelay)!=null?C:Fe,v=typeof E=="function"?E(t,b):E,p=O===!0||typeof O=="number"&&t<O||typeof O=="function"&&O(t,b);if(e||!p){f(b);return}t++,s.onFail==null||s.onFail(t,b),he(v).then(()=>{if(d())return g()}).then(()=>{e?f(b):R()})})};return N(s.networkMode)?R():g().then(R),{promise:a,cancel:l,continue:()=>(i==null?void 0:i())?a:Promise.resolve(),cancelRetry:y,continueRetry:o}}const X=console;function Qe(){let s=[],e=0,t=o=>{o()},r=o=>{o()};const i=o=>{let d;e++;try{d=o()}finally{e--,e||a()}return d},n=o=>{e?s.push(o):se(()=>{t(o)})},u=o=>(...d)=>{n(()=>{o(...d)})},a=()=>{const o=s;s=[],o.length&&se(()=>{r(()=>{o.forEach(d=>{t(d)})})})};return{batch:i,batchCalls:u,schedule:n,setNotifyFunction:o=>{t=o},setBatchNotifyFunction:o=>{r=o}}}const S=Qe();class ye{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),G(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(e){this.cacheTime=Math.max(this.cacheTime||0,e??(A?1/0:5*60*1e3))}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}class we extends ye{constructor(e){super(),this.abortSignalConsumed=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.logger=e.logger||X,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||Ee(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.cache.remove(this)}setData(e,t){const r=z(this.state.data,e,this.options);return this.dispatch({data:r,type:"success",dataUpdatedAt:t==null?void 0:t.updatedAt,manual:t==null?void 0:t.manual}),r}setState(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})}cancel(e){var t;const r=this.promise;return(t=this.retryer)==null||t.cancel(e),r?r.then(w).catch(w):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(e=>e.options.enabled!==!1)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(e=>e.getCurrentResult().isStale)}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!oe(this.state.dataUpdatedAt,e)}onFocus(){var e;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t&&t.refetch({cancelRefetch:!1}),(e=this.retryer)==null||e.continue()}onOnline(){var e;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t&&t.refetch({cancelRefetch:!1}),(e=this.retryer)==null||e.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(e,t){var r,i;if(this.state.fetchStatus!=="idle"){if(this.state.dataUpdatedAt&&t!=null&&t.cancelRefetch)this.cancel({silent:!0});else if(this.promise){var n;return(n=this.retryer)==null||n.continueRetry(),this.promise}}if(e&&this.setOptions(e),!this.options.queryFn){const f=this.observers.find(g=>g.options.queryFn);f&&this.setOptions(f.options)}const u=Oe(),a={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},l=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>{if(u)return this.abortSignalConsumed=!0,u.signal}})};l(a);const y=()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(a)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'"),o={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:y};if(l(o),(r=this.options.behavior)==null||r.onFetch(o),this.revertState=this.state,this.state.fetchStatus==="idle"||this.state.fetchMeta!==((i=o.fetchOptions)==null?void 0:i.meta)){var d;this.dispatch({type:"fetch",meta:(d=o.fetchOptions)==null?void 0:d.meta})}const m=f=>{if(U(f)&&f.silent||this.dispatch({type:"error",error:f}),!U(f)){var g,R,c,b;(g=(R=this.cache.config).onError)==null||g.call(R,f,this),(c=(b=this.cache.config).onSettled)==null||c.call(b,this.state.data,f,this)}this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=fe({fn:o.fetchFn,abort:u==null?void 0:u.abort.bind(u),onSuccess:f=>{var g,R,c,b;if(typeof f>"u"){m(new Error(this.queryHash+" data is undefined"));return}this.setData(f),(g=(R=this.cache.config).onSuccess)==null||g.call(R,f,this),(c=(b=this.cache.config).onSettled)==null||c.call(b,f,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:m,onFail:(f,g)=>{this.dispatch({type:"failed",failureCount:f,error:g})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(e){const t=r=>{var i,n;switch(e.type){case"failed":return{...r,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:(i=e.meta)!=null?i:null,fetchStatus:N(this.options.networkMode)?"fetching":"paused",...!r.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...r,data:e.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:(n=e.dataUpdatedAt)!=null?n:Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const u=e.error;return U(u)&&u.revert&&this.revertState?{...this.revertState,fetchStatus:"idle"}:{...r,error:u,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:u,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...e.state}}};this.state=t(this.state),S.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate(e)}),this.cache.notify({query:this,type:"updated",action:e})})}}function Ee(s){const e=typeof s.initialData=="function"?s.initialData():s.initialData,t=typeof e<"u",r=t?typeof s.initialDataUpdatedAt=="function"?s.initialDataUpdatedAt():s.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:t?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:t?"success":"loading",fetchStatus:"idle"}}class qe extends T{constructor(e){super(),this.config=e||{},this.queries=[],this.queriesMap={}}build(e,t,r){var i;const n=t.queryKey,u=(i=t.queryHash)!=null?i:J(n,t);let a=this.get(u);return a||(a=new we({cache:this,logger:e.getLogger(),queryKey:n,queryHash:u,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(a)),a}add(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"added",query:e}))}remove(e){const t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter(r=>r!==e),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"removed",query:e}))}clear(){S.batch(()=>{this.queries.forEach(e=>{this.remove(e)})})}get(e){return this.queriesMap[e]}getAll(){return this.queries}find(e,t){const[r]=q(e,t);return typeof r.exact>"u"&&(r.exact=!0),this.queries.find(i=>Z(r,i))}findAll(e,t){const[r]=q(e,t);return Object.keys(r).length>0?this.queries.filter(i=>Z(r,i)):this.queries}notify(e){S.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}onFocus(){S.batch(()=>{this.queries.forEach(e=>{e.onFocus()})})}onOnline(){S.batch(()=>{this.queries.forEach(e=>{e.onOnline()})})}}class De extends ye{constructor(e){super(),this.defaultOptions=e.defaultOptions,this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.logger=e.logger||X,this.observers=[],this.state=e.state||xe(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(e){this.dispatch({type:"setState",state:e})}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.observers=this.observers.filter(t=>t!==e),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.observers.length||(this.state.status==="loading"?this.scheduleGc():this.mutationCache.remove(this))}continue(){var e,t;return(e=(t=this.retryer)==null?void 0:t.continue())!=null?e:this.execute()}async execute(){const e=()=>{var p;return this.retryer=fe({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(h,Q)=>{this.dispatch({type:"failed",failureCount:h,error:Q})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:(p=this.options.retry)!=null?p:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise},t=this.state.status==="loading";try{var r,i,n,u,a,l,y,o;if(!t){var d,m,f,g;this.dispatch({type:"loading",variables:this.options.variables}),await((d=(m=this.mutationCache.config).onMutate)==null?void 0:d.call(m,this.state.variables,this));const h=await((f=(g=this.options).onMutate)==null?void 0:f.call(g,this.state.variables));h!==this.state.context&&this.dispatch({type:"loading",context:h,variables:this.state.variables})}const p=await e();return await((r=(i=this.mutationCache.config).onSuccess)==null?void 0:r.call(i,p,this.state.variables,this.state.context,this)),await((n=(u=this.options).onSuccess)==null?void 0:n.call(u,p,this.state.variables,this.state.context)),await((a=(l=this.mutationCache.config).onSettled)==null?void 0:a.call(l,p,null,this.state.variables,this.state.context,this)),await((y=(o=this.options).onSettled)==null?void 0:y.call(o,p,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:p}),p}catch(p){try{var R,c,b,F,C,O,E,v;throw await((R=(c=this.mutationCache.config).onError)==null?void 0:R.call(c,p,this.state.variables,this.state.context,this)),await((b=(F=this.options).onError)==null?void 0:b.call(F,p,this.state.variables,this.state.context)),await((C=(O=this.mutationCache.config).onSettled)==null?void 0:C.call(O,void 0,p,this.state.variables,this.state.context,this)),await((E=(v=this.options).onSettled)==null?void 0:E.call(v,void 0,p,this.state.variables,this.state.context)),p}finally{this.dispatch({type:"error",error:p})}}}dispatch(e){const t=r=>{switch(e.type){case"failed":return{...r,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"loading":return{...r,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!N(this.options.networkMode),status:"loading",variables:e.variables};case"success":return{...r,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:e.error,failureCount:r.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"};case"setState":return{...r,...e.state}}};this.state=t(this.state),S.batch(()=>{this.observers.forEach(r=>{r.onMutationUpdate(e)}),this.mutationCache.notify({mutation:this,type:"updated",action:e})})}}function xe(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}class Me extends T{constructor(e){super(),this.config=e||{},this.mutations=[],this.mutationId=0}build(e,t,r){const i=new De({mutationCache:this,logger:e.getLogger(),mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:r,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0});return this.add(i),i}add(e){this.mutations.push(e),this.notify({type:"added",mutation:e})}remove(e){this.mutations=this.mutations.filter(t=>t!==e),this.notify({type:"removed",mutation:e})}clear(){S.batch(()=>{this.mutations.forEach(e=>{this.remove(e)})})}getAll(){return this.mutations}find(e){return typeof e.exact>"u"&&(e.exact=!0),this.mutations.find(t=>$(e,t))}findAll(e){return this.mutations.filter(t=>$(e,t))}notify(e){S.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}resumePausedMutations(){var e;return this.resuming=((e=this.resuming)!=null?e:Promise.resolve()).then(()=>{const t=this.mutations.filter(r=>r.state.isPaused);return S.batch(()=>t.reduce((r,i)=>r.then(()=>i.continue().catch(w)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}function Ie(){return{onFetch:s=>{s.fetchFn=()=>{var e,t,r,i,n,u;const a=(e=s.fetchOptions)==null||(t=e.meta)==null?void 0:t.refetchPage,l=(r=s.fetchOptions)==null||(i=r.meta)==null?void 0:i.fetchMore,y=l==null?void 0:l.pageParam,o=(l==null?void 0:l.direction)==="forward",d=(l==null?void 0:l.direction)==="backward",m=((n=s.state.data)==null?void 0:n.pages)||[],f=((u=s.state.data)==null?void 0:u.pageParams)||[];let g=f,R=!1;const c=v=>{Object.defineProperty(v,"signal",{enumerable:!0,get:()=>{var p;if((p=s.signal)!=null&&p.aborted)R=!0;else{var h;(h=s.signal)==null||h.addEventListener("abort",()=>{R=!0})}return s.signal}})},b=s.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+s.options.queryHash+"'")),F=(v,p,h,Q)=>(g=Q?[p,...g]:[...g,p],Q?[h,...v]:[...v,h]),C=(v,p,h,Q)=>{if(R)return Promise.reject("Cancelled");if(typeof h>"u"&&!p&&v.length)return Promise.resolve(v);const M={queryKey:s.queryKey,pageParam:h,meta:s.options.meta};c(M);const B=b(M);return Promise.resolve(B).then(Ce=>F(v,h,Ce,Q))};let O;if(!m.length)O=C([]);else if(o){const v=typeof y<"u",p=v?y:ie(s.options,m);O=C(m,v,p)}else if(d){const v=typeof y<"u",p=v?y:Ae(s.options,m);O=C(m,v,p,!0)}else{g=[];const v=typeof s.options.getNextPageParam>"u";O=(a&&m[0]?a(m[0],0,m):!0)?C([],v,f[0]):Promise.resolve(F([],f[0],m[0]));for(let h=1;h<m.length;h++)O=O.then(Q=>{if(a&&m[h]?a(m[h],h,m):!0){const B=v?f[h]:ie(s.options,Q);return C(Q,v,B)}return Promise.resolve(F(Q,f[h],m[h]))})}return O.then(v=>({pages:v,pageParams:g}))}}}}function ie(s,e){return s.getNextPageParam==null?void 0:s.getNextPageParam(e[e.length-1],e)}function Ae(s,e){return s.getPreviousPageParam==null?void 0:s.getPreviousPageParam(e[0],e)}class ht{constructor(e={}){this.queryCache=e.queryCache||new qe,this.mutationCache=e.mutationCache||new Me,this.logger=e.logger||X,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,this.mountCount===1&&(this.unsubscribeFocus=L.subscribe(()=>{L.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=j.subscribe(()=>{j.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var e,t;this.mountCount--,this.mountCount===0&&((e=this.unsubscribeFocus)==null||e.call(this),this.unsubscribeFocus=void 0,(t=this.unsubscribeOnline)==null||t.call(this),this.unsubscribeOnline=void 0)}isFetching(e,t){const[r]=q(e,t);return r.fetchStatus="fetching",this.queryCache.findAll(r).length}isMutating(e){return this.mutationCache.findAll({...e,fetching:!0}).length}getQueryData(e,t){var r;return(r=this.queryCache.find(e,t))==null?void 0:r.state.data}ensureQueryData(e,t,r){const i=I(e,t,r),n=this.getQueryData(i.queryKey);return n?Promise.resolve(n):this.fetchQuery(i)}getQueriesData(e){return this.getQueryCache().findAll(e).map(({queryKey:t,state:r})=>{const i=r.data;return[t,i]})}setQueryData(e,t,r){const i=this.queryCache.find(e),n=i==null?void 0:i.state.data,u=Re(t,n);if(typeof u>"u")return;const a=I(e),l=this.defaultQueryOptions(a);return this.queryCache.build(this,l).setData(u,{...r,manual:!0})}setQueriesData(e,t,r){return S.batch(()=>this.getQueryCache().findAll(e).map(({queryKey:i})=>[i,this.setQueryData(i,t,r)]))}getQueryState(e,t){var r;return(r=this.queryCache.find(e,t))==null?void 0:r.state}removeQueries(e,t){const[r]=q(e,t),i=this.queryCache;S.batch(()=>{i.findAll(r).forEach(n=>{i.remove(n)})})}resetQueries(e,t,r){const[i,n]=q(e,t,r),u=this.queryCache,a={type:"active",...i};return S.batch(()=>(u.findAll(i).forEach(l=>{l.reset()}),this.refetchQueries(a,n)))}cancelQueries(e,t,r){const[i,n={}]=q(e,t,r);typeof n.revert>"u"&&(n.revert=!0);const u=S.batch(()=>this.queryCache.findAll(i).map(a=>a.cancel(n)));return Promise.all(u).then(w).catch(w)}invalidateQueries(e,t,r){const[i,n]=q(e,t,r);return S.batch(()=>{var u,a;if(this.queryCache.findAll(i).forEach(y=>{y.invalidate()}),i.refetchType==="none")return Promise.resolve();const l={...i,type:(u=(a=i.refetchType)!=null?a:i.type)!=null?u:"active"};return this.refetchQueries(l,n)})}refetchQueries(e,t,r){const[i,n]=q(e,t,r),u=S.batch(()=>this.queryCache.findAll(i).filter(l=>!l.isDisabled()).map(l=>{var y;return l.fetch(void 0,{...n,cancelRefetch:(y=n==null?void 0:n.cancelRefetch)!=null?y:!0,meta:{refetchPage:i.refetchPage}})}));let a=Promise.all(u).then(w);return n!=null&&n.throwOnError||(a=a.catch(w)),a}fetchQuery(e,t,r){const i=I(e,t,r),n=this.defaultQueryOptions(i);typeof n.retry>"u"&&(n.retry=!1);const u=this.queryCache.build(this,n);return u.isStaleByTime(n.staleTime)?u.fetch(n):Promise.resolve(u.state.data)}prefetchQuery(e,t,r){return this.fetchQuery(e,t,r).then(w).catch(w)}fetchInfiniteQuery(e,t,r){const i=I(e,t,r);return i.behavior=Ie(),this.fetchQuery(i)}prefetchInfiniteQuery(e,t,r){return this.fetchInfiniteQuery(e,t,r).then(w).catch(w)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(e){this.defaultOptions=e}setQueryDefaults(e,t){const r=this.queryDefaults.find(i=>D(e)===D(i.queryKey));r?r.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})}getQueryDefaults(e){if(!e)return;const t=this.queryDefaults.find(r=>K(e,r.queryKey));return t==null?void 0:t.defaultOptions}setMutationDefaults(e,t){const r=this.mutationDefaults.find(i=>D(e)===D(i.mutationKey));r?r.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})}getMutationDefaults(e){if(!e)return;const t=this.mutationDefaults.find(r=>K(e,r.mutationKey));return t==null?void 0:t.defaultOptions}defaultQueryOptions(e){if(e!=null&&e._defaulted)return e;const t={...this.defaultOptions.queries,...this.getQueryDefaults(e==null?void 0:e.queryKey),...e,_defaulted:!0};return!t.queryHash&&t.queryKey&&(t.queryHash=J(t.queryKey,t)),typeof t.refetchOnReconnect>"u"&&(t.refetchOnReconnect=t.networkMode!=="always"),typeof t.useErrorBoundary>"u"&&(t.useErrorBoundary=!!t.suspense),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...this.defaultOptions.mutations,...this.getMutationDefaults(e==null?void 0:e.mutationKey),...e,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}class Te extends T{constructor(e,t){super(),this.client=e,this.options=t,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(t)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.currentQuery.addObserver(this),ne(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return W(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return W(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(e,t){const r=this.options,i=this.currentQuery;if(this.options=this.client.defaultQueryOptions(e),_(r,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),this.updateQuery();const n=this.hasListeners();n&&ue(this.currentQuery,i,this.options,r)&&this.executeFetch(),this.updateResult(t),n&&(this.currentQuery!==i||this.options.enabled!==r.enabled||this.options.staleTime!==r.staleTime)&&this.updateStaleTimeout();const u=this.computeRefetchInterval();n&&(this.currentQuery!==i||this.options.enabled!==r.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)}getOptimisticResult(e){const t=this.client.getQueryCache().build(this.client,e),r=this.createResult(t,e);return Ke(this,r,e)&&(this.currentResult=r,this.currentResultOptions=this.options,this.currentResultState=this.currentQuery.state),r}getCurrentResult(){return this.currentResult}trackResult(e){const t={};return Object.keys(e).forEach(r=>{Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(r),e[r])})}),t}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch({refetchPage:e,...t}={}){return this.fetch({...t,meta:{refetchPage:e}})}fetchOptimistic(e){const t=this.client.defaultQueryOptions(e),r=this.client.getQueryCache().build(this.client,t);return r.isFetchingOptimistic=!0,r.fetch().then(()=>this.createResult(r,t))}fetch(e){var t;return this.executeFetch({...e,cancelRefetch:(t=e.cancelRefetch)!=null?t:!0}).then(()=>(this.updateResult(),this.currentResult))}executeFetch(e){this.updateQuery();let t=this.currentQuery.fetch(this.options,e);return e!=null&&e.throwOnError||(t=t.catch(w)),t}updateStaleTimeout(){if(this.clearStaleTimeout(),A||this.currentResult.isStale||!G(this.options.staleTime))return;const t=oe(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(()=>{this.currentResult.isStale||this.updateResult()},t)}computeRefetchInterval(){var e;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(e=this.options.refetchInterval)!=null?e:!1}updateRefetchInterval(e){this.clearRefetchInterval(),this.currentRefetchInterval=e,!(A||this.options.enabled===!1||!G(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(()=>{(this.options.refetchIntervalInBackground||L.isFocused())&&this.executeFetch()},this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(e,t){const r=this.currentQuery,i=this.options,n=this.currentResult,u=this.currentResultState,a=this.currentResultOptions,l=e!==r,y=l?e.state:this.currentQueryInitialState,o=l?this.currentResult:this.previousQueryResult,{state:d}=e;let{dataUpdatedAt:m,error:f,errorUpdatedAt:g,fetchStatus:R,status:c}=d,b=!1,F=!1,C;if(t._optimisticResults){const h=this.hasListeners(),Q=!h&&ne(e,t),M=h&&ue(e,r,t,i);(Q||M)&&(R=N(e.options.networkMode)?"fetching":"paused",m||(c="loading")),t._optimisticResults==="isRestoring"&&(R="idle")}if(t.keepPreviousData&&!d.dataUpdatedAt&&o!=null&&o.isSuccess&&c!=="error")C=o.data,m=o.dataUpdatedAt,c=o.status,b=!0;else if(t.select&&typeof d.data<"u")if(n&&d.data===(u==null?void 0:u.data)&&t.select===this.selectFn)C=this.selectResult;else try{this.selectFn=t.select,C=t.select(d.data),C=z(n==null?void 0:n.data,C,t),this.selectResult=C,this.selectError=null}catch(h){this.selectError=h}else C=d.data;if(typeof t.placeholderData<"u"&&typeof C>"u"&&c==="loading"){let h;if(n!=null&&n.isPlaceholderData&&t.placeholderData===(a==null?void 0:a.placeholderData))h=n.data;else if(h=typeof t.placeholderData=="function"?t.placeholderData():t.placeholderData,t.select&&typeof h<"u")try{h=t.select(h),this.selectError=null}catch(Q){this.selectError=Q}typeof h<"u"&&(c="success",C=z(n==null?void 0:n.data,h,t),F=!0)}this.selectError&&(f=this.selectError,C=this.selectResult,g=Date.now(),c="error");const O=R==="fetching",E=c==="loading",v=c==="error";return{status:c,fetchStatus:R,isLoading:E,isSuccess:c==="success",isError:v,isInitialLoading:E&&O,data:C,dataUpdatedAt:m,error:f,errorUpdatedAt:g,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>y.dataUpdateCount||d.errorUpdateCount>y.errorUpdateCount,isFetching:O,isRefetching:O&&!E,isLoadingError:v&&d.dataUpdatedAt===0,isPaused:R==="paused",isPlaceholderData:F,isPreviousData:b,isRefetchError:v&&d.dataUpdatedAt!==0,isStale:Y(e,t),refetch:this.refetch,remove:this.remove}}updateResult(e){const t=this.currentResult,r=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,_(r,t))return;this.currentResult=r;const i={cache:!0},n=()=>{if(!t)return!0;const{notifyOnChangeProps:u}=this.options,a=typeof u=="function"?u():u;if(a==="all"||!a&&!this.trackedProps.size)return!0;const l=new Set(a??this.trackedProps);return this.options.useErrorBoundary&&l.add("error"),Object.keys(this.currentResult).some(y=>{const o=y;return this.currentResult[o]!==t[o]&&l.has(o)})};(e==null?void 0:e.listeners)!==!1&&n()&&(i.listeners=!0),this.notify({...i,...e})}updateQuery(){const e=this.client.getQueryCache().build(this.client,this.options);if(e===this.currentQuery)return;const t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(t==null||t.removeObserver(this),e.addObserver(this))}onQueryUpdate(e){const t={};e.type==="success"?t.onSuccess=!e.manual:e.type==="error"&&!U(e.error)&&(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()}notify(e){S.batch(()=>{if(e.onSuccess){var t,r,i,n;(t=(r=this.options).onSuccess)==null||t.call(r,this.currentResult.data),(i=(n=this.options).onSettled)==null||i.call(n,this.currentResult.data,null)}else if(e.onError){var u,a,l,y;(u=(a=this.options).onError)==null||u.call(a,this.currentResult.error),(l=(y=this.options).onSettled)==null||l.call(y,void 0,this.currentResult.error)}e.listeners&&this.listeners.forEach(({listener:o})=>{o(this.currentResult)}),e.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})})}}function Ue(s,e){return e.enabled!==!1&&!s.state.dataUpdatedAt&&!(s.state.status==="error"&&e.retryOnMount===!1)}function ne(s,e){return Ue(s,e)||s.state.dataUpdatedAt>0&&W(s,e,e.refetchOnMount)}function W(s,e,t){if(e.enabled!==!1){const r=typeof t=="function"?t(s):t;return r==="always"||r!==!1&&Y(s,e)}return!1}function ue(s,e,t,r){return t.enabled!==!1&&(s!==e||r.enabled===!1)&&(!t.suspense||s.state.status!=="error")&&Y(s,t)}function Y(s,e){return s.isStaleByTime(e.staleTime)}function Ke(s,e,t){return t.keepPreviousData?!1:t.placeholderData!==void 0?e.isPlaceholderData:!_(s.getCurrentResult(),e)}var pe={exports:{}},ve={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var x=P;function Le(s,e){return s===e&&(s!==0||1/s===1/e)||s!==s&&e!==e}var je=typeof Object.is=="function"?Object.is:Le,ke=x.useState,Ne=x.useEffect,Be=x.useLayoutEffect,He=x.useDebugValue;function Ge(s,e){var t=e(),r=ke({inst:{value:t,getSnapshot:e}}),i=r[0].inst,n=r[1];return Be(function(){i.value=t,i.getSnapshot=e,H(i)&&n({inst:i})},[s,t,e]),Ne(function(){return H(i)&&n({inst:i}),s(function(){H(i)&&n({inst:i})})},[s]),He(t),t}function H(s){var e=s.getSnapshot;s=s.value;try{var t=e();return!je(s,t)}catch{return!0}}function _e(s,e){return e()}var Ve=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?_e:Ge;ve.useSyncExternalStore=x.useSyncExternalStore!==void 0?x.useSyncExternalStore:Ve;pe.exports=ve;var ze=pe.exports;const We=ze.useSyncExternalStore,ae=P.createContext(void 0),me=P.createContext(!1);function be(s,e){return s||(e&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=ae),window.ReactQueryClientContext):ae)}const Je=({context:s}={})=>{const e=P.useContext(be(s,P.useContext(me)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},dt=({client:s,children:e,context:t,contextSharing:r=!1})=>{P.useEffect(()=>(s.mount(),()=>{s.unmount()}),[s]);const i=be(t,r);return P.createElement(me.Provider,{value:!t&&r},P.createElement(i.Provider,{value:s},e))},ge=P.createContext(!1),Xe=()=>P.useContext(ge);ge.Provider;function Ye(){let s=!1;return{clearReset:()=>{s=!1},reset:()=>{s=!0},isReset:()=>s}}const Ze=P.createContext(Ye()),$e=()=>P.useContext(Ze);function et(s,e){return typeof s=="function"?s(...e):!!s}const tt=(s,e)=>{(s.suspense||s.useErrorBoundary)&&(e.isReset()||(s.retryOnMount=!1))},st=s=>{P.useEffect(()=>{s.clearReset()},[s])},rt=({result:s,errorResetBoundary:e,useErrorBoundary:t,query:r})=>s.isError&&!e.isReset()&&!s.isFetching&&et(t,[s.error,r]),it=s=>{s.suspense&&typeof s.staleTime!="number"&&(s.staleTime=1e3)},nt=(s,e)=>s.isLoading&&s.isFetching&&!e,ut=(s,e,t)=>(s==null?void 0:s.suspense)&&nt(e,t),at=(s,e,t)=>e.fetchOptimistic(s).then(({data:r})=>{s.onSuccess==null||s.onSuccess(r),s.onSettled==null||s.onSettled(r,null)}).catch(r=>{t.clearReset(),s.onError==null||s.onError(r),s.onSettled==null||s.onSettled(void 0,r)});function ot(s,e){const t=Je({context:s.context}),r=Xe(),i=$e(),n=t.defaultQueryOptions(s);n._optimisticResults=r?"isRestoring":"optimistic",n.onError&&(n.onError=S.batchCalls(n.onError)),n.onSuccess&&(n.onSuccess=S.batchCalls(n.onSuccess)),n.onSettled&&(n.onSettled=S.batchCalls(n.onSettled)),it(n),tt(n,i),st(i);const[u]=P.useState(()=>new e(t,n)),a=u.getOptimisticResult(n);if(We(P.useCallback(l=>{const y=r?()=>{}:u.subscribe(S.batchCalls(l));return u.updateResult(),y},[u,r]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),P.useEffect(()=>{u.setOptions(n,{listeners:!1})},[n,u]),ut(n,a,r))throw at(n,u,i);if(rt({result:a,errorResetBoundary:i,useErrorBoundary:n.useErrorBoundary,query:u.getCurrentQuery()}))throw a.error;return n.notifyOnChangeProps?a:u.trackResult(a)}function ft(s,e,t){const r=I(s,e,t);return ot(r,Te)}export{ht as Q,dt as a,ft as u};
//# sourceMappingURL=query-5a5acc5e.js.map
