{"version": 3, "file": "query-5a5acc5e.js", "sources": ["../../node_modules/@tanstack/query-core/build/lib/subscribable.mjs", "../../node_modules/@tanstack/query-core/build/lib/utils.mjs", "../../node_modules/@tanstack/query-core/build/lib/focusManager.mjs", "../../node_modules/@tanstack/query-core/build/lib/onlineManager.mjs", "../../node_modules/@tanstack/query-core/build/lib/retryer.mjs", "../../node_modules/@tanstack/query-core/build/lib/logger.mjs", "../../node_modules/@tanstack/query-core/build/lib/notifyManager.mjs", "../../node_modules/@tanstack/query-core/build/lib/removable.mjs", "../../node_modules/@tanstack/query-core/build/lib/query.mjs", "../../node_modules/@tanstack/query-core/build/lib/queryCache.mjs", "../../node_modules/@tanstack/query-core/build/lib/mutation.mjs", "../../node_modules/@tanstack/query-core/build/lib/mutationCache.mjs", "../../node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs", "../../node_modules/@tanstack/query-core/build/lib/queryClient.mjs", "../../node_modules/@tanstack/query-core/build/lib/queryObserver.mjs", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.js", "../../node_modules/use-sync-external-store/shim/index.js", "../../node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs", "../../node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs", "../../node_modules/@tanstack/react-query/build/lib/isRestoring.mjs", "../../node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs", "../../node_modules/@tanstack/react-query/build/lib/utils.mjs", "../../node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs", "../../node_modules/@tanstack/react-query/build/lib/suspense.mjs", "../../node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs", "../../node_modules/@tanstack/react-query/build/lib/useQuery.mjs"], "sourcesContent": ["class Subscribable {\n  constructor() {\n    this.listeners = new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n\n  subscribe(listener) {\n    const identity = {\n      listener\n    };\n    this.listeners.add(identity);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(identity);\n      this.onUnsubscribe();\n    };\n  }\n\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n\n  onSubscribe() {// Do nothing\n  }\n\n  onUnsubscribe() {// Do nothing\n  }\n\n}\n\nexport { Subscribable };\n//# sourceMappingURL=subscribable.mjs.map\n", "// TYPES\n// UTILS\nconst isServer = typeof window === 'undefined' || 'Deno' in window;\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3,\n      queryKey: arg1,\n      queryFn: arg2\n    };\n  }\n\n  return { ...arg2,\n    queryKey: arg1\n  };\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3,\n        mutationKey: arg1,\n        mutationFn: arg2\n      };\n    }\n\n    return { ...arg2,\n      mutationKey: arg1\n    };\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2,\n      mutationFn: arg1\n    };\n  }\n\n  return { ...arg1\n  };\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    queryKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    mutationKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive();\n\n    if (type === 'active' && !isActive) {\n      return false;\n    }\n\n    if (type === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetchStatus !== 'undefined' && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    fetching,\n    predicate,\n    mutationKey\n  } = filters;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\n\nfunction hashQueryKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(a, b);\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]));\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aSize = array ? a.length : Object.keys(a).length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  const ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  const prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  sleep(0).then(callback);\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n\n  return;\n}\nfunction replaceData(prevData, data, options) {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual != null && options.isDataEqual(prevData, data)) {\n    return prevData;\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data);\n  }\n\n  return data;\n}\n\nexport { difference, functionalUpdate, getAbortController, hashQueryKey, hashQueryKeyByOptions, isError, isPlainArray, isPlainObject, isQueryKey, isServer, isValidTimeout, matchMutation, matchQuery, noop, parseFilterArgs, parseMutationArgs, parseMutationFilterArgs, parseQueryArgs, partialDeepEqual, partialMatchKey, replaceAt, replaceData, replaceEqualDeep, scheduleMicrotask, shallowEqualObjects, sleep, timeUntilStale };\n//# sourceMappingURL=utils.mjs.map\n", "import { Subscribable } from './subscribable.mjs';\nimport { isServer } from './utils.mjs';\n\nclass FocusManager extends Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onFocus => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus(); // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n\n  setFocused(focused) {\n    const changed = this.focused !== focused;\n\n    if (changed) {\n      this.focused = focused;\n      this.onFocus();\n    }\n  }\n\n  onFocus() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  }\n\n}\nconst focusManager = new FocusManager();\n\nexport { FocusManager, focusManager };\n//# sourceMappingURL=focusManager.mjs.map\n", "import { Subscribable } from './subscribable.mjs';\nimport { isServer } from './utils.mjs';\n\nconst onlineEvents = ['online', 'offline'];\nclass OnlineManager extends Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onOnline => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline(); // Listen to online\n\n\n        onlineEvents.forEach(event => {\n          window.addEventListener(event, listener, false);\n        });\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach(event => {\n            window.removeEventListener(event, listener);\n          });\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(online => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online);\n      } else {\n        this.onOnline();\n      }\n    });\n  }\n\n  setOnline(online) {\n    const changed = this.online !== online;\n\n    if (changed) {\n      this.online = online;\n      this.onOnline();\n    }\n  }\n\n  onOnline() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  }\n\n}\nconst onlineManager = new OnlineManager();\n\nexport { OnlineManager, onlineManager };\n//# sourceMappingURL=onlineManager.mjs.map\n", "import { focusManager } from './focusManager.mjs';\nimport { onlineManager } from './onlineManager.mjs';\nimport { sleep } from './utils.mjs';\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * 2 ** failureCount, 30000);\n}\n\nfunction canFetch(networkMode) {\n  return (networkMode != null ? networkMode : 'online') === 'online' ? onlineManager.isOnline() : true;\n}\nclass CancelledError {\n  constructor(options) {\n    this.revert = options == null ? void 0 : options.revert;\n    this.silent = options == null ? void 0 : options.silent;\n  }\n\n}\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  let promiseResolve;\n  let promiseReject;\n  const promise = new Promise((outerResolve, outerReject) => {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort == null ? void 0 : config.abort();\n    }\n  };\n\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n\n  const shouldPause = () => !focusManager.isFocused() || config.networkMode !== 'always' && !onlineManager.isOnline();\n\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        const canContinue = isResolved || !shouldPause();\n\n        if (canContinue) {\n          continueResolve(value);\n        }\n\n        return canContinue;\n      };\n\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(() => {\n      continueFn = undefined;\n\n      if (!isResolved) {\n        config.onContinue == null ? void 0 : config.onContinue();\n      }\n    });\n  }; // Create loop function\n\n\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return;\n    }\n\n    let promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      const retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      const retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      const delay = typeof retryDelay === 'function' ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === 'number' && failureCount < retry || typeof retry === 'function' && retry(failureCount, error);\n\n      if (isRetryCancelled || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(() => {\n        if (shouldPause()) {\n          return pause();\n        }\n\n        return;\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  if (canFetch(config.networkMode)) {\n    run();\n  } else {\n    pause().then(run);\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn == null ? void 0 : continueFn();\n      return didContinue ? promise : Promise.resolve();\n    },\n    cancelRetry,\n    continueRetry\n  };\n}\n\nexport { CancelledError, canFetch, createRetryer, isCancelledError };\n//# sourceMappingURL=retryer.mjs.map\n", "const defaultLogger = console;\n\nexport { defaultLogger };\n//# sourceMappingURL=logger.mjs.map\n", "import { scheduleMicrotask } from './utils.mjs';\n\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n\n  let notifyFn = callback => {\n    callback();\n  };\n\n  let batchNotifyFn = callback => {\n    callback();\n  };\n\n  const batch = callback => {\n    let result;\n    transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      transactions--;\n\n      if (!transactions) {\n        flush();\n      }\n    }\n\n    return result;\n  };\n\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n\n\n  const batchCalls = callback => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args);\n      });\n    };\n  };\n\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n\n\n  const setNotifyFunction = fn => {\n    notifyFn = fn;\n  };\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n\n\n  const setBatchNotifyFunction = fn => {\n    batchNotifyFn = fn;\n  };\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction\n  };\n} // SINGLETON\n\nconst notifyManager = createNotifyManager();\n\nexport { createNotifyManager, notifyManager };\n//# sourceMappingURL=notifyManager.mjs.map\n", "import { isValidTimeout, isServer } from './utils.mjs';\n\nclass Removable {\n  destroy() {\n    this.clearGcTimeout();\n  }\n\n  scheduleGc() {\n    this.clearGcTimeout();\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.cacheTime);\n    }\n  }\n\n  updateCacheTime(newCacheTime) {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : isServer ? Infinity : 5 * 60 * 1000);\n  }\n\n  clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  }\n\n}\n\nexport { Removable };\n//# sourceMappingURL=removable.mjs.map\n", "import { replaceData, noop, timeUntilStale, getAbortController } from './utils.mjs';\nimport { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { createRetryer, isCancelledError, canFetch } from './retryer.mjs';\nimport { Removable } from './removable.mjs';\n\n// CLASS\nclass Query extends Removable {\n  constructor(config) {\n    super();\n    this.abortSignalConsumed = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.logger = config.logger || defaultLogger;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || getDefaultState(this.options);\n    this.state = this.initialState;\n    this.scheduleGc();\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this);\n    }\n  }\n\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options); // Set data and mark it as cached\n\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt,\n      manual: options == null ? void 0 : options.manual\n    });\n    return data;\n  }\n\n  setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state,\n      setStateOptions\n    });\n  }\n\n  cancel(options) {\n    var _this$retryer;\n\n    const promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n\n  reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  }\n\n  isActive() {\n    return this.observers.some(observer => observer.options.enabled !== false);\n  }\n\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n\n  isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(observer => observer.getCurrentResult().isStale);\n  }\n\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n\n  onFocus() {\n    var _this$retryer2;\n\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  }\n\n  onOnline() {\n    var _this$retryer3;\n\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        this.scheduleGc();\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  getObserversCount() {\n    return this.observers.length;\n  }\n\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  }\n\n  fetch(options, fetchOptions) {\n    var _this$options$behavio, _context$fetchOptions;\n\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\"As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']\");\n      }\n    }\n\n    const abortController = getAbortController(); // Create query function context\n\n    const queryFnContext = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    }; // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n\n    const addSignalProperty = object => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true;\n            return abortController.signal;\n          }\n\n          return undefined;\n        }\n      });\n    };\n\n    addSignalProperty(queryFnContext); // Create fetch function\n\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\"Missing queryFn for queryKey '\" + this.options.queryHash + \"'\");\n      }\n\n      this.abortSignalConsumed = false;\n      return this.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context); // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (this.state.fetchStatus === 'idle' || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    }\n\n    const onError = error => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n\n      if (!isCancelledError(error)) {\n        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;\n\n        // Notify cache callback\n        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error, this);\n        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error, this);\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc();\n      }\n\n      this.isFetchingOptimistic = false;\n    }; // Try to fetch the data\n\n\n    this.retryer = createRetryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),\n      onSuccess: data => {\n        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;\n\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\"Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: \" + this.queryHash);\n          }\n\n          onError(new Error(this.queryHash + \" data is undefined\"));\n          return;\n        }\n\n        this.setData(data); // Notify cache callback\n\n        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);\n        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc();\n        }\n\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({\n          type: 'failed',\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: () => {\n        this.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      var _action$meta, _action$dataUpdatedAt;\n\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            fetchStatus: 'paused'\n          };\n\n        case 'continue':\n          return { ...state,\n            fetchStatus: 'fetching'\n          };\n\n        case 'fetch':\n          return { ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n            fetchStatus: canFetch(this.options.networkMode) ? 'fetching' : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading'\n            })\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n\n        case 'error':\n          const error = action.error;\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState,\n              fetchStatus: 'idle'\n            };\n          }\n\n          return { ...state,\n            error: error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error'\n          };\n\n        case 'invalidate':\n          return { ...state,\n            isInvalidated: true\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action);\n      });\n      this.cache.notify({\n        query: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\n\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n  const hasData = typeof data !== 'undefined';\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle'\n  };\n}\n\nexport { Query };\n//# sourceMappingURL=query.mjs.map\n", "import { hashQueryKeyByOptions, parseFilterArgs, matchQuery } from './utils.mjs';\nimport { Query } from './query.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\n// CLASS\nclass QueryCache extends Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.queries = [];\n    this.queriesMap = {};\n  }\n\n  build(client, options, state) {\n    var _options$queryHash;\n\n    const queryKey = options.queryKey;\n    const queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n\n    return query;\n  }\n\n  add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'added',\n        query\n      });\n    }\n  }\n\n  remove(query) {\n    const queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(x => x !== query);\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'removed',\n        query\n      });\n    }\n  }\n\n  clear() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n\n  get(queryHash) {\n    return this.queriesMap[queryHash];\n  }\n\n  getAll() {\n    return this.queries;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(query => matchQuery(filters, query));\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    return Object.keys(filters).length > 0 ? this.queries.filter(query => matchQuery(filters, query)) : this.queries;\n  }\n\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  onFocus() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n\n  onOnline() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n\n}\n\nexport { QueryCache };\n//# sourceMappingURL=queryCache.mjs.map\n", "import { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Removable } from './removable.mjs';\nimport { createRetryer, canFetch } from './retryer.mjs';\n\n// CLASS\nclass Mutation extends Removable {\n  constructor(config) {\n    super();\n    this.defaultOptions = config.defaultOptions;\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.logger = config.logger || defaultLogger;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state\n    });\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the mutation from being garbage collected\n\n      this.clearGcTimeout();\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    this.observers = this.observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer\n    });\n  }\n\n  optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc();\n      } else {\n        this.mutationCache.remove(this);\n      }\n    }\n  }\n\n  continue() {\n    var _this$retryer$continu, _this$retryer;\n\n    return (_this$retryer$continu = (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.continue()) != null ? _this$retryer$continu : this.execute();\n  }\n\n  async execute() {\n    const executeMutation = () => {\n      var _this$options$retry;\n\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found');\n          }\n\n          return this.options.mutationFn(this.state.variables);\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({\n            type: 'failed',\n            failureCount,\n            error\n          });\n        },\n        onPause: () => {\n          this.dispatch({\n            type: 'pause'\n          });\n        },\n        onContinue: () => {\n          this.dispatch({\n            type: 'continue'\n          });\n        },\n        retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode\n      });\n      return this.retryer.promise;\n    };\n\n    const restored = this.state.status === 'loading';\n\n    try {\n      var _this$mutationCache$c3, _this$mutationCache$c4, _this$options$onSucce, _this$options2, _this$mutationCache$c5, _this$mutationCache$c6, _this$options$onSettl, _this$options3;\n\n      if (!restored) {\n        var _this$mutationCache$c, _this$mutationCache$c2, _this$options$onMutat, _this$options;\n\n        this.dispatch({\n          type: 'loading',\n          variables: this.options.variables\n        }); // Notify cache callback\n\n        await ((_this$mutationCache$c = (_this$mutationCache$c2 = this.mutationCache.config).onMutate) == null ? void 0 : _this$mutationCache$c.call(_this$mutationCache$c2, this.state.variables, this));\n        const context = await ((_this$options$onMutat = (_this$options = this.options).onMutate) == null ? void 0 : _this$options$onMutat.call(_this$options, this.state.variables));\n\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables\n          });\n        }\n      }\n\n      const data = await executeMutation(); // Notify cache callback\n\n      await ((_this$mutationCache$c3 = (_this$mutationCache$c4 = this.mutationCache.config).onSuccess) == null ? void 0 : _this$mutationCache$c3.call(_this$mutationCache$c4, data, this.state.variables, this.state.context, this));\n      await ((_this$options$onSucce = (_this$options2 = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options2, data, this.state.variables, this.state.context)); // Notify cache callback\n\n      await ((_this$mutationCache$c5 = (_this$mutationCache$c6 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c5.call(_this$mutationCache$c6, data, null, this.state.variables, this.state.context, this));\n      await ((_this$options$onSettl = (_this$options3 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options3, data, null, this.state.variables, this.state.context));\n      this.dispatch({\n        type: 'success',\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        var _this$mutationCache$c7, _this$mutationCache$c8, _this$options$onError, _this$options4, _this$mutationCache$c9, _this$mutationCache$c10, _this$options$onSettl2, _this$options5;\n\n        // Notify cache callback\n        await ((_this$mutationCache$c7 = (_this$mutationCache$c8 = this.mutationCache.config).onError) == null ? void 0 : _this$mutationCache$c7.call(_this$mutationCache$c8, error, this.state.variables, this.state.context, this));\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n\n        await ((_this$options$onError = (_this$options4 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options4, error, this.state.variables, this.state.context)); // Notify cache callback\n\n        await ((_this$mutationCache$c9 = (_this$mutationCache$c10 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c9.call(_this$mutationCache$c10, undefined, error, this.state.variables, this.state.context, this));\n        await ((_this$options$onSettl2 = (_this$options5 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options5, undefined, error, this.state.variables, this.state.context));\n        throw error;\n      } finally {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n    }\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            isPaused: true\n          };\n\n        case 'continue':\n          return { ...state,\n            isPaused: false\n          };\n\n        case 'loading':\n          return { ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false\n          };\n\n        case 'error':\n          return { ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error'\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nexport { Mutation, getDefaultState };\n//# sourceMappingURL=mutation.mjs.map\n", "import { notify<PERSON>anager } from './notifyManager.mjs';\nimport { Mutation } from './mutation.mjs';\nimport { matchMutation, noop } from './utils.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\n// CLASS\nclass MutationCache extends Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.mutations = [];\n    this.mutationId = 0;\n  }\n\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined\n    });\n    this.add(mutation);\n    return mutation;\n  }\n\n  add(mutation) {\n    this.mutations.push(mutation);\n    this.notify({\n      type: 'added',\n      mutation\n    });\n  }\n\n  remove(mutation) {\n    this.mutations = this.mutations.filter(x => x !== mutation);\n    this.notify({\n      type: 'removed',\n      mutation\n    });\n  }\n\n  clear() {\n    notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation);\n      });\n    });\n  }\n\n  getAll() {\n    return this.mutations;\n  }\n\n  find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(mutation => matchMutation(filters, mutation));\n  }\n\n  findAll(filters) {\n    return this.mutations.filter(mutation => matchMutation(filters, mutation));\n  }\n\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  resumePausedMutations() {\n    var _this$resuming;\n\n    this.resuming = ((_this$resuming = this.resuming) != null ? _this$resuming : Promise.resolve()).then(() => {\n      const pausedMutations = this.mutations.filter(x => x.state.isPaused);\n      return notifyManager.batch(() => pausedMutations.reduce((promise, mutation) => promise.then(() => mutation.continue().catch(noop)), Promise.resolve()));\n    }).then(() => {\n      this.resuming = undefined;\n    });\n    return this.resuming;\n  }\n\n}\n\nexport { MutationCache };\n//# sourceMappingURL=mutationCache.mjs.map\n", "function infiniteQueryBehavior() {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        let newPageParams = oldPageParams;\n        let cancelled = false;\n\n        const addSignalProperty = object => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              var _context$signal;\n\n              if ((_context$signal = context.signal) != null && _context$signal.aborted) {\n                cancelled = true;\n              } else {\n                var _context$signal2;\n\n                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener('abort', () => {\n                  cancelled = true;\n                });\n              }\n\n              return context.signal;\n            }\n          });\n        }; // Get query function\n\n\n        const queryFn = context.options.queryFn || (() => Promise.reject(\"Missing queryFn for queryKey '\" + context.options.queryHash + \"'\"));\n\n        const buildNewPages = (pages, param, page, previous) => {\n          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];\n          return previous ? [page, ...pages] : [...pages, page];\n        }; // Create function to fetch a page\n\n\n        const fetchPage = (pages, manual, param, previous) => {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const queryFnResult = queryFn(queryFnContext);\n          const promise = Promise.resolve(queryFnResult).then(page => buildNewPages(pages, param, page, previous));\n          return promise;\n        };\n\n        let promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param);\n        } // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param, true);\n        } // Refetch pages\n        else {\n          newPageParams = [];\n          const manual = typeof context.options.getNextPageParam === 'undefined';\n          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n              if (shouldFetchNextPage) {\n                const param = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n                return fetchPage(pages, manual, param);\n              }\n\n              return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n            });\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams\n        }));\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n\n  return;\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n\n  return;\n}\n\nexport { getNextPageParam, getPreviousPageParam, hasNextPage, hasPreviousPage, infiniteQueryBehavior };\n//# sourceMappingURL=infiniteQueryBehavior.mjs.map\n", "import { parseFilter<PERSON>rgs, parseQueryArgs, functionalUpdate, noop, hashQ<PERSON>y<PERSON><PERSON>, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils.mjs';\nimport { QueryCache } from './queryCache.mjs';\nimport { MutationCache } from './mutationCache.mjs';\nimport { focusManager } from './focusManager.mjs';\nimport { onlineManager } from './onlineManager.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior.mjs';\nimport { defaultLogger } from './logger.mjs';\n\n// CLASS\nclass QueryClient {\n  constructor(config = {}) {\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.logger = config.logger || defaultLogger;\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n    this.mountCount = 0;\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\"Passing a custom logger has been deprecated and will be removed in the next major version.\");\n    }\n  }\n\n  mount() {\n    this.mountCount++;\n    if (this.mountCount !== 1) return;\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations();\n        this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations();\n        this.queryCache.onOnline();\n      }\n    });\n  }\n\n  unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    this.mountCount--;\n    if (this.mountCount !== 0) return;\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    this.unsubscribeFocus = undefined;\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n    this.unsubscribeOnline = undefined;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    filters.fetchStatus = 'fetching';\n    return this.queryCache.findAll(filters).length;\n  }\n\n  isMutating(filters) {\n    return this.mutationCache.findAll({ ...filters,\n      fetching: true\n    }).length;\n  }\n\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    const cachedData = this.getQueryData(parsedOptions.queryKey);\n    return cachedData ? Promise.resolve(cachedData) : this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n\n  setQueryData(queryKey, updater, options) {\n    const query = this.queryCache.find(queryKey);\n    const prevData = query == null ? void 0 : query.state.data;\n    const data = functionalUpdate(updater, prevData);\n\n    if (typeof data === 'undefined') {\n      return undefined;\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(data, { ...options,\n      manual: true\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData(queryKeyOrFilters, updater, options) {\n    return notifyManager.batch(() => this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n\n  getQueryState(queryKey,\n  /**\n   * @deprecated This filters will be removed in the next major version.\n   */\n  filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    const queryCache = this.queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    const queryCache = this.queryCache;\n    const refetchFilters = {\n      type: 'active',\n      ...filters\n    };\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(arg1, arg2, arg3) {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3);\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions)));\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    return notifyManager.batch(() => {\n      var _ref, _filters$refetchType;\n\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve();\n      }\n\n      const refetchFilters = { ...filters,\n        type: (_ref = (_filters$refetchType = filters.refetchType) != null ? _filters$refetchType : filters.type) != null ? _ref : 'active'\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).filter(query => !query.isDisabled()).map(query => {\n      var _options$cancelRefetc;\n\n      return query.fetch(undefined, { ...options,\n        cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n        meta: {\n          refetchPage: filters.refetchPage\n        }\n      });\n    }));\n    let promise = Promise.all(promises).then(noop);\n\n    if (!(options != null && options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  }\n\n  resumePausedMutations() {\n    return this.mutationCache.resumePausedMutations();\n  }\n\n  getQueryCache() {\n    return this.queryCache;\n  }\n\n  getMutationCache() {\n    return this.mutationCache;\n  }\n\n  getLogger() {\n    return this.logger;\n  }\n\n  getDefaultOptions() {\n    return this.defaultOptions;\n  }\n\n  setDefaultOptions(options) {\n    this.defaultOptions = options;\n  }\n\n  setQueryDefaults(queryKey, options) {\n    const result = this.queryDefaults.find(x => hashQueryKey(queryKey) === hashQueryKey(x.queryKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getQueryDefaults(queryKey) {\n    if (!queryKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.queryDefaults.find(x => partialMatchKey(queryKey, x.queryKey)); // Additional checks and error in dev mode\n\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter(x => partialMatchKey(queryKey, x.queryKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several query defaults match with key '\" + JSON.stringify(queryKey) + \"'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  setMutationDefaults(mutationKey, options) {\n    const result = this.mutationDefaults.find(x => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getMutationDefaults(mutationKey) {\n    if (!mutationKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.mutationDefaults.find(x => partialMatchKey(mutationKey, x.mutationKey)); // Additional checks and error in dev mode\n\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter(x => partialMatchKey(mutationKey, x.mutationKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several mutation defaults match with key '\" + JSON.stringify(mutationKey) + \"'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  defaultQueryOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    const defaultedOptions = { ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options == null ? void 0 : options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    } // dependent default values\n\n\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== 'always';\n    }\n\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense;\n    }\n\n    return defaultedOptions;\n  }\n\n  defaultMutationOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    return { ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options == null ? void 0 : options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n\n  clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  }\n\n}\n\nexport { QueryClient };\n//# sourceMappingURL=queryClient.mjs.map\n", "import { shallowEqualObjects, noop, isServer, isValidTimeout, timeUntilStale, replaceData } from './utils.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { focusManager } from './focusManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\nimport { canFetch, isCancelledError } from './retryer.mjs';\n\nclass QueryObserver extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.options = options;\n    this.trackedProps = new Set();\n    this.selectError = null;\n    this.bindMethods();\n    this.setOptions(options);\n  }\n\n  bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n    this.currentQuery.removeObserver(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryOptions(options);\n\n    if (process.env.NODE_ENV !== 'production' && typeof (options == null ? void 0 : options.isDataEqual) !== 'undefined') {\n      this.client.getLogger().error(\"The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option\");\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this\n      });\n    }\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    const mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n\n  getOptimisticResult(options) {\n    const query = this.client.getQueryCache().build(this.client, options);\n    const result = this.createResult(query, options);\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result;\n      this.currentResultOptions = this.options;\n      this.currentResultState = this.currentQuery.state;\n    }\n\n    return result;\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  trackResult(result) {\n    const trackedResult = {};\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n\n  getCurrentQuery() {\n    return this.currentQuery;\n  }\n\n  remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  }\n\n  refetch({\n    refetchPage,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        refetchPage\n      }\n    });\n  }\n\n  fetchOptimistic(options) {\n    const defaultedOptions = this.client.defaultQueryOptions(options);\n    const query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n\n  fetch(fetchOptions) {\n    var _fetchOptions$cancelR;\n\n    return this.executeFetch({ ...fetchOptions,\n      cancelRefetch: (_fetchOptions$cancelR = fetchOptions.cancelRefetch) != null ? _fetchOptions$cancelR : true\n    }).then(() => {\n      this.updateResult();\n      return this.currentResult;\n    });\n  }\n\n  executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    let promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions != null && fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  }\n\n  updateStaleTimeout() {\n    this.clearStaleTimeout();\n\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n\n    const time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    const timeout = time + 1;\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n\n  computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  }\n\n  updateRefetchInterval(nextInterval) {\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  }\n\n  updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  }\n\n  clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  }\n\n  clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  }\n\n  createResult(query, options) {\n    const prevQuery = this.currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.currentResult;\n    const prevResultState = this.currentResultState;\n    const prevResultOptions = this.currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    const prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    const {\n      state\n    } = query;\n    let {\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      fetchStatus,\n      status\n    } = state;\n    let isPreviousData = false;\n    let isPlaceholderData = false;\n    let data; // Optimistically set result in fetching state if needed\n\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode) ? 'fetching' : 'paused';\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle';\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdatedAt && prevQueryResult != null && prevQueryResult.isSuccess && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n        data = this.selectResult;\n      } else {\n        try {\n          this.selectFn = options.select;\n          data = options.select(state.data);\n          data = replaceData(prevResult == null ? void 0 : prevResult.data, data, options);\n          this.selectResult = data;\n          this.selectError = null;\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError);\n          }\n\n          this.selectError = selectError;\n        }\n      }\n    } // Use query data\n    else {\n      data = state.data;\n    } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && status === 'loading') {\n      let placeholderData; // Memoize placeholder data\n\n      if (prevResult != null && prevResult.isPlaceholderData && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.selectError = null;\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError);\n            }\n\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = replaceData(prevResult == null ? void 0 : prevResult.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    const isFetching = fetchStatus === 'fetching';\n    const isLoading = status === 'loading';\n    const isError = status === 'error';\n    const result = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  }\n\n  updateResult(notifyOptions) {\n    const prevResult = this.currentResult;\n    const nextResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify and update result if something has changed\n\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n\n    this.currentResult = nextResult; // Determine which callbacks to trigger\n\n    const defaultNotifyOptions = {\n      cache: true\n    };\n\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === 'function' ? notifyOnChangeProps() : notifyOnChangeProps;\n\n      if (notifyOnChangePropsValue === 'all' || !notifyOnChangePropsValue && !this.trackedProps.size) {\n        return true;\n      }\n\n      const includedProps = new Set(notifyOnChangePropsValue != null ? notifyOnChangePropsValue : this.trackedProps);\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error');\n      }\n\n      return Object.keys(this.currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify({ ...defaultNotifyOptions,\n      ...notifyOptions\n    });\n  }\n\n  updateQuery() {\n    const query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    const prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n\n  onQueryUpdate(action) {\n    const notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  }\n\n  notify(notifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        var _this$options$onSucce, _this$options, _this$options$onSettl, _this$options2;\n\n        (_this$options$onSucce = (_this$options = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options, this.currentResult.data);\n        (_this$options$onSettl = (_this$options2 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options2, this.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        var _this$options$onError, _this$options3, _this$options$onSettl2, _this$options4;\n\n        (_this$options$onError = (_this$options3 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options3, this.currentResult.error);\n        (_this$options$onSettl2 = (_this$options4 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options4, undefined, this.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  }\n\n}\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n} // this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\n\n\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult, options) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false;\n  } // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n\n\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData;\n  } // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n\n\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  } // basically, just keep previous properties if nothing changed\n\n\n  return false;\n}\n\nexport { QueryObserver };\n//# sourceMappingURL=queryObserver.mjs.map\n", "/**\n * @license React\n * use-sync-external-store-shim.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useState = React.useState,\n  useEffect = React.useEffect,\n  useLayoutEffect = React.useLayoutEffect,\n  useDebugValue = React.useDebugValue;\nfunction useSyncExternalStore$2(subscribe, getSnapshot) {\n  var value = getSnapshot(),\n    _useState = useState({ inst: { value: value, getSnapshot: getSnapshot } }),\n    inst = _useState[0].inst,\n    forceUpdate = _useState[1];\n  useLayoutEffect(\n    function () {\n      inst.value = value;\n      inst.getSnapshot = getSnapshot;\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n    },\n    [subscribe, value, getSnapshot]\n  );\n  useEffect(\n    function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      return subscribe(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      });\n    },\n    [subscribe]\n  );\n  useDebugValue(value);\n  return value;\n}\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  inst = inst.value;\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(inst, nextValue);\n  } catch (error) {\n    return !0;\n  }\n}\nfunction useSyncExternalStore$1(subscribe, getSnapshot) {\n  return getSnapshot();\n}\nvar shim =\n  \"undefined\" === typeof window ||\n  \"undefined\" === typeof window.document ||\n  \"undefined\" === typeof window.document.createElement\n    ? useSyncExternalStore$1\n    : useSyncExternalStore$2;\nexports.useSyncExternalStore =\n  void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "'use client';\nimport { useSyncExternalStore as useSyncExternalStore$1 } from 'use-sync-external-store/shim/index.js';\n\nconst useSyncExternalStore = useSyncExternalStore$1;\n\nexport { useSyncExternalStore };\n//# sourceMappingURL=useSyncExternalStore.mjs.map\n", "'use client';\nimport * as React from 'react';\n\nconst defaultContext = /*#__PURE__*/React.createContext(undefined);\nconst QueryClientSharingContext = /*#__PURE__*/React.createContext(false); // If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(context, contextSharing) {\n  if (context) {\n    return context;\n  }\n\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nconst useQueryClient = ({\n  context\n} = {}) => {\n  const queryClient = React.useContext(getQueryClientContext(context, React.useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nconst QueryClientProvider = ({\n  client,\n  children,\n  context,\n  contextSharing = false\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n\n  if (process.env.NODE_ENV !== 'production' && contextSharing) {\n    client.getLogger().error(\"The contextSharing option has been deprecated and will be removed in the next major version\");\n  }\n\n  const Context = getQueryClientContext(context, contextSharing);\n  return /*#__PURE__*/React.createElement(QueryClientSharingContext.Provider, {\n    value: !context && contextSharing\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: client\n  }, children));\n};\n\nexport { QueryClientProvider, defaultContext, useQueryClient };\n//# sourceMappingURL=QueryClientProvider.mjs.map\n", "'use client';\nimport * as React from 'react';\n\nconst IsRestoringContext = /*#__PURE__*/React.createContext(false);\nconst useIsRestoring = () => React.useContext(IsRestoringContext);\nconst IsRestoringProvider = IsRestoringContext.Provider;\n\nexport { IsRestoringProvider, useIsRestoring };\n//# sourceMappingURL=isRestoring.mjs.map\n", "'use client';\nimport * as React from 'react';\n\nfunction createValue() {\n  let isReset = false;\n  return {\n    clearReset: () => {\n      isReset = false;\n    },\n    reset: () => {\n      isReset = true;\n    },\n    isReset: () => {\n      return isReset;\n    }\n  };\n}\n\nconst QueryErrorResetBoundaryContext = /*#__PURE__*/React.createContext(createValue()); // HOOK\n\nconst useQueryErrorResetBoundary = () => React.useContext(QueryErrorResetBoundaryContext); // COMPONENT\n\nconst QueryErrorResetBoundary = ({\n  children\n}) => {\n  const [value] = React.useState(() => createValue());\n  return /*#__PURE__*/React.createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};\n\nexport { QueryErrorResetBoundary, useQueryErrorResetBoundary };\n//# sourceMappingURL=QueryErrorResetBoundary.mjs.map\n", "function shouldThrowError(_useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params);\n  }\n\n  return !!_useErrorBoundary;\n}\n\nexport { shouldThrowError };\n//# sourceMappingURL=utils.mjs.map\n", "'use client';\nimport * as React from 'react';\nimport { shouldThrowError } from './utils.mjs';\n\nconst ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n  if (options.suspense || options.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false;\n    }\n  }\n};\nconst useClearResetErrorBoundary = errorResetBoundary => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n};\nconst getHasError = ({\n  result,\n  errorResetBoundary,\n  useErrorBoundary,\n  query\n}) => {\n  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(useErrorBoundary, [result.error, query]);\n};\n\nexport { ensurePreventErrorBoundaryRetry, getHasError, useClearResetErrorBoundary };\n//# sourceMappingURL=errorBoundaryUtils.mjs.map\n", "const ensureStaleTime = defaultedOptions => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    }\n  }\n};\nconst willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nconst shouldSuspend = (defaultedOptions, result, isRestoring) => (defaultedOptions == null ? void 0 : defaultedOptions.suspense) && willFetch(result, isRestoring);\nconst fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).then(({\n  data\n}) => {\n  defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n  defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n}).catch(error => {\n  errorResetBoundary.clearReset();\n  defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n  defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n});\n\nexport { ensureStaleTime, fetchOptimistic, shouldSuspend, willFetch };\n//# sourceMappingURL=suspense.mjs.map\n", "'use client';\nimport * as React from 'react';\nimport { notifyManager } from '@tanstack/query-core';\nimport { useSyncExternalStore } from './useSyncExternalStore.mjs';\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary.mjs';\nimport { useQueryClient } from './QueryClientProvider.mjs';\nimport { useIsRestoring } from './isRestoring.mjs';\nimport { ensurePreventErrorBoundaryRetry, useClearResetErrorBoundary, getHasError } from './errorBoundaryUtils.mjs';\nimport { ensureStaleTime, shouldSuspend, fetchOptimistic } from './suspense.mjs';\n\nfunction useBaseQuery(options, Observer) {\n  const queryClient = useQueryClient({\n    context: options.context\n  });\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const defaultedOptions = queryClient.defaultQueryOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions._optimisticResults = isRestoring ? 'isRestoring' : 'optimistic'; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  ensureStaleTime(defaultedOptions);\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);\n  useClearResetErrorBoundary(errorResetBoundary);\n  const [observer] = React.useState(() => new Observer(queryClient, defaultedOptions));\n  const result = observer.getOptimisticResult(defaultedOptions);\n  useSyncExternalStore(React.useCallback(onStoreChange => {\n    const unsubscribe = isRestoring ? () => undefined : observer.subscribe(notifyManager.batchCalls(onStoreChange)); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return unsubscribe;\n  }, [observer, isRestoring]), () => observer.getCurrentResult(), () => observer.getCurrentResult());\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (shouldSuspend(defaultedOptions, result, isRestoring)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary);\n  } // Handle error boundary\n\n\n  if (getHasError({\n    result,\n    errorResetBoundary,\n    useErrorBoundary: defaultedOptions.useErrorBoundary,\n    query: observer.getCurrentQuery()\n  })) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n\nexport { useBaseQuery };\n//# sourceMappingURL=useBaseQuery.mjs.map\n", "'use client';\nimport { parseQueryArgs, QueryObserver } from '@tanstack/query-core';\nimport { useBaseQuery } from './useBaseQuery.mjs';\n\nfunction useQuery(arg1, arg2, arg3) {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(parsedOptions, QueryObserver);\n}\n\nexport { useQuery };\n//# sourceMappingURL=useQuery.mjs.map\n"], "names": ["Subscribable", "listener", "identity", "isServer", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "timeUntilStale", "updatedAt", "staleTime", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "parseFilter<PERSON><PERSON>s", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "hashQueryKeyByOptions", "partialMatchKey", "isActive", "matchMutation", "mutation", "fetching", "<PERSON><PERSON><PERSON>", "hashQuery<PERSON>ey", "options", "_", "val", "isPlainObject", "result", "key", "a", "b", "partialDeepEqual", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aSize", "bItems", "bSize", "copy", "equalItems", "i", "shallowEqualObjects", "o", "hasObjectPrototype", "ctor", "prot", "sleep", "timeout", "resolve", "scheduleMicrotask", "callback", "getAbortController", "replaceData", "prevData", "data", "FocusManager", "onFocus", "_this$cleanup", "setup", "_this$cleanup2", "focused", "focusManager", "onlineEvents", "OnlineManager", "onOnline", "event", "online", "onlineManager", "defaultRetryDelay", "failureCount", "canFetch", "networkMode", "CancelledError", "isCancelledError", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "promiseResolve", "promiseReject", "promise", "outerResolve", "outerReject", "cancel", "cancelOptions", "reject", "cancelRetry", "continueRetry", "shouldP<PERSON>e", "pause", "continueResolve", "canContinue", "run", "promiseOrValue", "error", "_config$retry", "_config$retryDelay", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "defaultLogger", "createNotifyManager", "queue", "transactions", "notifyFn", "batchNotifyFn", "batch", "flush", "schedule", "batchCalls", "args", "originalQueue", "fn", "notify<PERSON><PERSON>ger", "Removable", "newCacheTime", "Query", "getDefaultState", "newData", "state", "setStateOptions", "_this$retryer", "observer", "_this$retryer2", "x", "_this$retryer3", "fetchOptions", "_this$options$behavio", "_context$fetchOptions", "_this$retryer4", "abortController", "queryFnContext", "addSignalProperty", "object", "fetchFn", "context", "_context$fetchOptions2", "onError", "_this$cache$config$on", "_this$cache$config", "_this$cache$config$on2", "_this$cache$config2", "_this$cache$config$on3", "_this$cache$config3", "_this$cache$config$on4", "_this$cache$config4", "action", "reducer", "_action$meta", "_action$dataUpdatedAt", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "client", "_options$queryHash", "queryHash", "queryInMap", "Mutation", "_this$retryer$continu", "executeMutation", "_this$options$retry", "restored", "_this$mutationCache$c3", "_this$mutationCache$c4", "_this$options$onSucce", "_this$options2", "_this$mutationCache$c5", "_this$mutationCache$c6", "_this$options$onSettl", "_this$options3", "_this$mutationCache$c", "_this$mutationCache$c2", "_this$options$onMutat", "_this$options", "_this$mutationCache$c7", "_this$mutationCache$c8", "_this$options$onError", "_this$options4", "_this$mutationCache$c9", "_this$mutationCache$c10", "_this$options$onSettl2", "_this$options5", "MutationCache", "_this$resuming", "pausedMutations", "infiniteQueryBehavior", "_context$fetchOptions3", "_context$fetchOptions4", "_context$state$data", "_context$state$data2", "refetchPage", "fetchMore", "pageParam", "isFetchingNextPage", "isFetchingPreviousPage", "oldPages", "oldPageParams", "newPageParams", "cancelled", "_context$signal", "_context$signal2", "queryFn", "buildNewPages", "pages", "param", "page", "previous", "fetchPage", "manual", "queryFnResult", "getNextPageParam", "getPreviousPageParam", "QueryClient", "_this$unsubscribeFocu", "_this$unsubscribeOnli", "_this$queryCache$find", "parsedOptions", "cachedData", "query<PERSON>eyOrFilters", "defaultedOptions", "_this$queryCache$find2", "queryCache", "refetchFilters", "promises", "_ref", "_filters$refetchType", "_options$cancelRefetc", "firstMatchingDefaults", "QueryObserver", "shouldFetchOnMount", "shouldFetchOn", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "mounted", "shouldFetchOptionally", "nextRefetchInterval", "shouldAssignObserverCurrentProperties", "trackedResult", "_fetchOptions$cancelR", "_this$options$refetch", "nextInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "prevQueryResult", "dataUpdatedAt", "errorUpdatedAt", "status", "isPreviousData", "isPlaceholderData", "fetchOnMount", "fetchOptionally", "selectError", "placeholderData", "isFetching", "isLoading", "isError", "isStale", "nextResult", "defaultNotifyOptions", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "<PERSON><PERSON><PERSON>", "shouldLoadOnMount", "field", "optimisticResult", "React", "require$$0", "is", "y", "objectIs", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "useSyncExternalStore$2", "subscribe", "getSnapshot", "_useState", "inst", "forceUpdate", "checkIfSnapshotChanged", "latestGetSnapshot", "nextValue", "useSyncExternalStore$1", "shim", "useSyncExternalStoreShim_production", "shimModule", "useSyncExternalStore", "defaultContext", "React.createContext", "QueryClientSharingContext", "getQueryClientContext", "contextSharing", "useQueryClient", "queryClient", "React.useContext", "QueryClientProvider", "children", "React.useEffect", "Context", "React.createElement", "IsRestoringContext", "useIsRestoring", "createValue", "isReset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "shouldThrowError", "_useErrorBoundary", "params", "ensurePreventErrorBoundaryRetry", "errorResetBoundary", "useClearResetErrorBoundary", "getHasError", "useErrorBoundary", "ensureStaleTime", "<PERSON><PERSON><PERSON><PERSON>", "isRestoring", "shouldSuspend", "fetchOptimistic", "useBaseQuery", "Observer", "React.useState", "React.useCallback", "onStoreChange", "unsubscribe", "useQuery"], "mappings": "yCAAA,MAAMA,CAAa,CACjB,aAAc,CACZ,KAAK,UAAY,IAAI,IACrB,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,CAC1C,CAED,UAAUC,EAAU,CAClB,MAAMC,EAAW,CACf,SAAAD,CACN,EACI,YAAK,UAAU,IAAIC,CAAQ,EAC3B,KAAK,YAAW,EACT,IAAM,CACX,KAAK,UAAU,OAAOA,CAAQ,EAC9B,KAAK,cAAa,CACxB,CACG,CAED,cAAe,CACb,OAAO,KAAK,UAAU,KAAO,CAC9B,CAED,aAAc,CACb,CAED,eAAgB,CACf,CAEH,CC1BA,MAAMC,EAAW,OAAO,OAAW,KAAe,SAAU,OAC5D,SAASC,GAAO,CAEhB,CACA,SAASC,GAAiBC,EAASC,EAAO,CACxC,OAAO,OAAOD,GAAY,WAAaA,EAAQC,CAAK,EAAID,CAC1D,CACA,SAASE,EAAeC,EAAO,CAC7B,OAAO,OAAOA,GAAU,UAAYA,GAAS,GAAKA,IAAU,GAC9D,CASA,SAASC,GAAeC,EAAWC,EAAW,CAC5C,OAAO,KAAK,IAAID,GAAaC,GAAa,GAAK,KAAK,MAAO,CAAC,CAC9D,CACA,SAASC,EAAeC,EAAMC,EAAMC,EAAM,CACxC,OAAKC,EAAWH,CAAI,EAIhB,OAAOC,GAAS,WACX,CAAE,GAAGC,EACV,SAAUF,EACV,QAASC,CACf,EAGS,CAAE,GAAGA,EACV,SAAUD,CACd,EAZWA,CAaX,CAwBA,SAASI,EAAgBJ,EAAMC,EAAMC,EAAM,CACzC,OAAOC,EAAWH,CAAI,EAAI,CAAC,CAAE,GAAGC,EAC9B,SAAUD,CACX,EAAEE,CAAI,EAAI,CAACF,GAAQ,CAAE,EAAEC,CAAI,CAC9B,CAMA,SAASI,EAAWC,EAASC,EAAO,CAClC,KAAM,CACJ,KAAAC,EAAO,MACP,MAAAC,EACA,YAAAC,EACA,UAAAC,EACA,SAAAC,EACA,MAAAC,CACD,EAAGP,EAEJ,GAAIH,EAAWS,CAAQ,GACrB,GAAIH,GACF,GAAIF,EAAM,YAAcO,EAAsBF,EAAUL,EAAM,OAAO,EACnE,MAAO,WAEA,CAACQ,EAAgBR,EAAM,SAAUK,CAAQ,EAClD,MAAO,GAIX,GAAIJ,IAAS,MAAO,CAClB,MAAMQ,EAAWT,EAAM,WAMvB,GAJIC,IAAS,UAAY,CAACQ,GAItBR,IAAS,YAAcQ,EACzB,MAAO,EAEV,CAUD,MARI,SAAOH,GAAU,WAAaN,EAAM,QAAO,IAAOM,GAIlD,OAAOH,EAAgB,KAAeA,IAAgBH,EAAM,MAAM,aAIlEI,GAAa,CAACA,EAAUJ,CAAK,EAKnC,CACA,SAASU,EAAcX,EAASY,EAAU,CACxC,KAAM,CACJ,MAAAT,EACA,SAAAU,EACA,UAAAR,EACA,YAAAS,CACD,EAAGd,EAEJ,GAAIH,EAAWiB,CAAW,EAAG,CAC3B,GAAI,CAACF,EAAS,QAAQ,YACpB,MAAO,GAGT,GAAIT,GACF,GAAIY,EAAaH,EAAS,QAAQ,WAAW,IAAMG,EAAaD,CAAW,EACzE,MAAO,WAEA,CAACL,EAAgBG,EAAS,QAAQ,YAAaE,CAAW,EACnE,MAAO,EAEV,CAMD,MAJI,SAAOD,GAAa,WAAaD,EAAS,MAAM,SAAW,YAAcC,GAIzER,GAAa,CAACA,EAAUO,CAAQ,EAKtC,CACA,SAASJ,EAAsBF,EAAUU,EAAS,CAEhD,QADgBA,GAAW,KAAO,OAASA,EAAQ,iBAAmBD,GACxDT,CAAQ,CACxB,CAMA,SAASS,EAAaT,EAAU,CAC9B,OAAO,KAAK,UAAUA,EAAU,CAACW,EAAGC,IAAQC,EAAcD,CAAG,EAAI,OAAO,KAAKA,CAAG,EAAE,KAAM,EAAC,OAAO,CAACE,EAAQC,KACvGD,EAAOC,CAAG,EAAIH,EAAIG,CAAG,EACdD,GACN,CAAE,CAAA,EAAIF,CAAG,CACd,CAKA,SAAST,EAAgBa,EAAGC,EAAG,CAC7B,OAAOC,GAAiBF,EAAGC,CAAC,CAC9B,CAKA,SAASC,GAAiBF,EAAGC,EAAG,CAC9B,OAAID,IAAMC,EACD,GAGL,OAAOD,GAAM,OAAOC,EACf,GAGLD,GAAKC,GAAK,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,CAAC,OAAO,KAAKA,CAAC,EAAE,KAAKF,GAAO,CAACG,GAAiBF,EAAED,CAAG,EAAGE,EAAEF,CAAG,CAAC,CAAC,EAG/D,EACT,CAOA,SAASI,GAAiBH,EAAGC,EAAG,CAC9B,GAAID,IAAMC,EACR,OAAOD,EAGT,MAAMI,EAAQC,GAAaL,CAAC,GAAKK,GAAaJ,CAAC,EAE/C,GAAIG,GAASP,EAAcG,CAAC,GAAKH,EAAcI,CAAC,EAAG,CACjD,MAAMK,EAAQF,EAAQJ,EAAE,OAAS,OAAO,KAAKA,CAAC,EAAE,OAC1CO,EAASH,EAAQH,EAAI,OAAO,KAAKA,CAAC,EAClCO,EAAQD,EAAO,OACfE,EAAOL,EAAQ,CAAE,EAAG,GAC1B,IAAIM,EAAa,EAEjB,QAASC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,MAAMZ,EAAMK,EAAQO,EAAIJ,EAAOI,CAAC,EAChCF,EAAKV,CAAG,EAAII,GAAiBH,EAAED,CAAG,EAAGE,EAAEF,CAAG,CAAC,EAEvCU,EAAKV,CAAG,IAAMC,EAAED,CAAG,GACrBW,GAEH,CAED,OAAOJ,IAAUE,GAASE,IAAeJ,EAAQN,EAAIS,CACtD,CAED,OAAOR,CACT,CAKA,SAASW,EAAoBZ,EAAGC,EAAG,CACjC,GAAID,GAAK,CAACC,GAAKA,GAAK,CAACD,EACnB,MAAO,GAGT,UAAWD,KAAOC,EAChB,GAAIA,EAAED,CAAG,IAAME,EAAEF,CAAG,EAClB,MAAO,GAIX,MAAO,EACT,CACA,SAASM,GAAatC,EAAO,CAC3B,OAAO,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,OAAO,KAAKA,CAAK,EAAE,MACrE,CAEA,SAAS8B,EAAcgB,EAAG,CACxB,GAAI,CAACC,GAAmBD,CAAC,EACvB,MAAO,GAIT,MAAME,EAAOF,EAAE,YAEf,GAAI,OAAOE,EAAS,IAClB,MAAO,GAIT,MAAMC,EAAOD,EAAK,UAOlB,MALI,GAACD,GAAmBE,CAAI,GAKxB,CAACA,EAAK,eAAe,eAAe,EAM1C,CAEA,SAASF,GAAmBD,EAAG,CAC7B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,IAAM,iBAC/C,CAEA,SAAStC,EAAWR,EAAO,CACzB,OAAO,MAAM,QAAQA,CAAK,CAC5B,CAIA,SAASkD,GAAMC,EAAS,CACtB,OAAO,IAAI,QAAQC,GAAW,CAC5B,WAAWA,EAASD,CAAO,CAC/B,CAAG,CACH,CAMA,SAASE,GAAkBC,EAAU,CACnCJ,GAAM,CAAC,EAAE,KAAKI,CAAQ,CACxB,CACA,SAASC,IAAqB,CAC5B,GAAI,OAAO,iBAAoB,WAC7B,OAAO,IAAI,eAIf,CACA,SAASC,EAAYC,EAAUC,EAAM/B,EAAS,CAE5C,OAAIA,EAAQ,aAAe,MAAQA,EAAQ,YAAY8B,EAAUC,CAAI,EAC5DD,EACE,OAAO9B,EAAQ,mBAAsB,WACvCA,EAAQ,kBAAkB8B,EAAUC,CAAI,EACtC/B,EAAQ,oBAAsB,GAEhCS,GAAiBqB,EAAUC,CAAI,EAGjCA,CACT,CCzTA,MAAMC,WAAqBpE,CAAa,CACtC,aAAc,CACZ,QAEA,KAAK,MAAQqE,GAAW,CAGtB,GAAI,CAAClE,GAAY,OAAO,iBAAkB,CACxC,MAAMF,EAAW,IAAMoE,IAGvB,cAAO,iBAAiB,mBAAoBpE,EAAU,EAAK,EAC3D,OAAO,iBAAiB,QAASA,EAAU,EAAK,EACzC,IAAM,CAEX,OAAO,oBAAoB,mBAAoBA,CAAQ,EACvD,OAAO,oBAAoB,QAASA,CAAQ,CACtD,CACO,CAGP,CACG,CAED,aAAc,CACP,KAAK,SACR,KAAK,iBAAiB,KAAK,KAAK,CAEnC,CAED,eAAgB,CACd,GAAI,CAAC,KAAK,eAAgB,CACxB,IAAIqE,GAEHA,EAAgB,KAAK,UAAY,MAAgBA,EAAc,KAAK,IAAI,EACzE,KAAK,QAAU,MAChB,CACF,CAED,iBAAiBC,EAAO,CACtB,IAAIC,EAEJ,KAAK,MAAQD,GACZC,EAAiB,KAAK,UAAY,MAAgBA,EAAe,KAAK,IAAI,EAC3E,KAAK,QAAUD,EAAME,GAAW,CAC1B,OAAOA,GAAY,UACrB,KAAK,WAAWA,CAAO,EAEvB,KAAK,QAAO,CAEpB,CAAK,CACF,CAED,WAAWA,EAAS,CACF,KAAK,UAAYA,IAG/B,KAAK,QAAUA,EACf,KAAK,QAAO,EAEf,CAED,SAAU,CACR,KAAK,UAAU,QAAQ,CAAC,CACtB,SAAAxE,CACN,IAAU,CACJA,GACN,CAAK,CACF,CAED,WAAY,CACV,OAAI,OAAO,KAAK,SAAY,UACnB,KAAK,QAIV,OAAO,SAAa,IACf,GAGF,CAAC,OAAW,UAAW,WAAW,EAAE,SAAS,SAAS,eAAe,CAC7E,CAEH,CACA,MAAMyE,EAAe,IAAIN,GCpFnBO,GAAe,CAAC,SAAU,SAAS,EACzC,MAAMC,WAAsB5E,CAAa,CACvC,aAAc,CACZ,QAEA,KAAK,MAAQ6E,GAAY,CAGvB,GAAI,CAAC1E,GAAY,OAAO,iBAAkB,CACxC,MAAMF,EAAW,IAAM4E,IAGvB,OAAAF,GAAa,QAAQG,GAAS,CAC5B,OAAO,iBAAiBA,EAAO7E,EAAU,EAAK,CACxD,CAAS,EACM,IAAM,CAEX0E,GAAa,QAAQG,GAAS,CAC5B,OAAO,oBAAoBA,EAAO7E,CAAQ,CACtD,CAAW,CACX,CACO,CAGP,CACG,CAED,aAAc,CACP,KAAK,SACR,KAAK,iBAAiB,KAAK,KAAK,CAEnC,CAED,eAAgB,CACd,GAAI,CAAC,KAAK,eAAgB,CACxB,IAAIqE,GAEHA,EAAgB,KAAK,UAAY,MAAgBA,EAAc,KAAK,IAAI,EACzE,KAAK,QAAU,MAChB,CACF,CAED,iBAAiBC,EAAO,CACtB,IAAIC,EAEJ,KAAK,MAAQD,GACZC,EAAiB,KAAK,UAAY,MAAgBA,EAAe,KAAK,IAAI,EAC3E,KAAK,QAAUD,EAAMQ,GAAU,CACzB,OAAOA,GAAW,UACpB,KAAK,UAAUA,CAAM,EAErB,KAAK,SAAQ,CAErB,CAAK,CACF,CAED,UAAUA,EAAQ,CACA,KAAK,SAAWA,IAG9B,KAAK,OAASA,EACd,KAAK,SAAQ,EAEhB,CAED,UAAW,CACT,KAAK,UAAU,QAAQ,CAAC,CACtB,SAAA9E,CACN,IAAU,CACJA,GACN,CAAK,CACF,CAED,UAAW,CACT,OAAI,OAAO,KAAK,QAAW,UAClB,KAAK,OAGV,OAAO,UAAc,KAAe,OAAO,UAAU,OAAW,IAC3D,GAGF,UAAU,MAClB,CAEH,CACA,MAAM+E,EAAgB,IAAIJ,GCrF1B,SAASK,GAAkBC,EAAc,CACvC,OAAO,KAAK,IAAI,IAAO,GAAKA,EAAc,GAAK,CACjD,CAEA,SAASC,EAASC,EAAa,CAC7B,OAAQA,GAAoC,YAAc,SAAWJ,EAAc,SAAU,EAAG,EAClG,CACA,MAAMK,EAAe,CACnB,YAAYjD,EAAS,CACnB,KAAK,OAASA,GAAW,KAAO,OAASA,EAAQ,OACjD,KAAK,OAASA,GAAW,KAAO,OAASA,EAAQ,MAClD,CAEH,CACA,SAASkD,EAAiB7E,EAAO,CAC/B,OAAOA,aAAiB4E,EAC1B,CACA,SAASE,GAAcC,EAAQ,CAC7B,IAAIC,EAAmB,GACnBP,EAAe,EACfQ,EAAa,GACbC,EACAC,EACAC,EACJ,MAAMC,EAAU,IAAI,QAAQ,CAACC,EAAcC,IAAgB,CACzDJ,EAAiBG,EACjBF,EAAgBG,CACpB,CAAG,EAEKC,EAASC,GAAiB,CACzBR,IACHS,EAAO,IAAId,GAAea,CAAa,CAAC,EACxCV,EAAO,OAAS,MAAgBA,EAAO,QAE7C,EAEQY,EAAc,IAAM,CACxBX,EAAmB,EACvB,EAEQY,EAAgB,IAAM,CAC1BZ,EAAmB,EACvB,EAEQa,EAAc,IAAM,CAAC5B,EAAa,UAAW,GAAIc,EAAO,cAAgB,UAAY,CAACR,EAAc,SAAQ,EAE3GnB,EAAUpD,GAAS,CAClBiF,IACHA,EAAa,GACbF,EAAO,WAAa,MAAgBA,EAAO,UAAU/E,CAAK,EAC1DkF,GAAc,MAAgBA,EAAU,EACxCC,EAAenF,CAAK,EAE1B,EAEQ0F,EAAS1F,GAAS,CACjBiF,IACHA,EAAa,GACbF,EAAO,SAAW,MAAgBA,EAAO,QAAQ/E,CAAK,EACtDkF,GAAc,MAAgBA,EAAU,EACxCE,EAAcpF,CAAK,EAEzB,EAEQ8F,EAAQ,IACL,IAAI,QAAQC,GAAmB,CACpCb,EAAalF,GAAS,CACpB,MAAMgG,EAAcf,GAAc,CAACY,IAEnC,OAAIG,GACFD,EAAgB/F,CAAK,EAGhBgG,CACf,EAEMjB,EAAO,SAAW,MAAgBA,EAAO,SAC/C,CAAK,EAAE,KAAK,IAAM,CACZG,EAAa,OAERD,GACHF,EAAO,YAAc,MAAgBA,EAAO,YAEpD,CAAK,EAIGkB,EAAM,IAAM,CAEhB,GAAIhB,EACF,OAGF,IAAIiB,EAEJ,GAAI,CACFA,EAAiBnB,EAAO,IACzB,OAAQoB,EAAO,CACdD,EAAiB,QAAQ,OAAOC,CAAK,CACtC,CAED,QAAQ,QAAQD,CAAc,EAAE,KAAK9C,CAAO,EAAE,MAAM+C,GAAS,CAC3D,IAAIC,EAAeC,EAGnB,GAAIpB,EACF,OAIF,MAAMqB,GAASF,EAAgBrB,EAAO,QAAU,KAAOqB,EAAgB,EACjEG,GAAcF,EAAqBtB,EAAO,aAAe,KAAOsB,EAAqB7B,GACrFgC,EAAQ,OAAOD,GAAe,WAAaA,EAAW9B,EAAc0B,CAAK,EAAII,EAC7EE,EAAcH,IAAU,IAAQ,OAAOA,GAAU,UAAY7B,EAAe6B,GAAS,OAAOA,GAAU,YAAcA,EAAM7B,EAAc0B,CAAK,EAEnJ,GAAInB,GAAoB,CAACyB,EAAa,CAEpCf,EAAOS,CAAK,EACZ,MACD,CAED1B,IAEAM,EAAO,QAAU,MAAgBA,EAAO,OAAON,EAAc0B,CAAK,EAElEjD,GAAMsD,CAAK,EACV,KAAK,IAAM,CACV,GAAIX,EAAW,EACb,OAAOC,EAAK,CAItB,CAAO,EAAE,KAAK,IAAM,CACRd,EACFU,EAAOS,CAAK,EAEZF,GAEV,CAAO,CACP,CAAK,CACL,EAGE,OAAIvB,EAASK,EAAO,WAAW,EAC7BkB,IAEAH,EAAO,EAAC,KAAKG,CAAG,EAGX,CACL,QAAAZ,EACA,OAAAG,EACA,SAAU,KACYN,GAAc,KAAO,OAASA,EAAU,GACvCG,EAAU,QAAQ,QAAO,EAEhD,YAAAM,EACA,cAAAC,CACJ,CACA,CCnKA,MAAMc,EAAgB,QCEtB,SAASC,IAAsB,CAC7B,IAAIC,EAAQ,CAAA,EACRC,EAAe,EAEfC,EAAWxD,GAAY,CACzBA,GACJ,EAEMyD,EAAgBzD,GAAY,CAC9BA,GACJ,EAEE,MAAM0D,EAAQ1D,GAAY,CACxB,IAAIvB,EACJ8E,IAEA,GAAI,CACF9E,EAASuB,EAAQ,CACvB,QAAc,CACRuD,IAEKA,GACHI,GAEH,CAED,OAAOlF,CACX,EAEQmF,EAAW5D,GAAY,CACvBuD,EACFD,EAAM,KAAKtD,CAAQ,EAEnBD,GAAkB,IAAM,CACtByD,EAASxD,CAAQ,CACzB,CAAO,CAEP,EAMQ6D,EAAa7D,GACV,IAAI8D,IAAS,CAClBF,EAAS,IAAM,CACb5D,EAAS,GAAG8D,CAAI,CACxB,CAAO,CACP,EAGQH,EAAQ,IAAM,CAClB,MAAMI,EAAgBT,EACtBA,EAAQ,CAAA,EAEJS,EAAc,QAChBhE,GAAkB,IAAM,CACtB0D,EAAc,IAAM,CAClBM,EAAc,QAAQ/D,GAAY,CAChCwD,EAASxD,CAAQ,CAC7B,CAAW,CACX,CAAS,CACT,CAAO,CAEP,EAoBE,MAAO,CACL,MAAA0D,EACA,WAAAG,EACA,SAAAD,EACA,kBAjBwBI,GAAM,CAC9BR,EAAWQ,CACf,EAgBI,uBAT6BA,GAAM,CACnCP,EAAgBO,CACpB,CAQA,CACA,CAEA,MAAMC,EAAgBZ,GAAqB,EC7F3C,MAAMa,EAAU,CACd,SAAU,CACR,KAAK,eAAc,CACpB,CAED,YAAa,CACX,KAAK,eAAc,EAEfzH,EAAe,KAAK,SAAS,IAC/B,KAAK,UAAY,WAAW,IAAM,CAChC,KAAK,eAAc,CAC3B,EAAS,KAAK,SAAS,EAEpB,CAED,gBAAgB0H,EAAc,CAE5B,KAAK,UAAY,KAAK,IAAI,KAAK,WAAa,EAAGA,IAAsC/H,EAAW,IAAW,EAAI,GAAK,IAAI,CACzH,CAED,gBAAiB,CACX,KAAK,YACP,aAAa,KAAK,SAAS,EAC3B,KAAK,UAAY,OAEpB,CAEH,CCtBA,MAAMgI,WAAcF,EAAU,CAC5B,YAAYzC,EAAQ,CAClB,QACA,KAAK,oBAAsB,GAC3B,KAAK,eAAiBA,EAAO,eAC7B,KAAK,WAAWA,EAAO,OAAO,EAC9B,KAAK,UAAY,GACjB,KAAK,MAAQA,EAAO,MACpB,KAAK,OAASA,EAAO,QAAU2B,EAC/B,KAAK,SAAW3B,EAAO,SACvB,KAAK,UAAYA,EAAO,UACxB,KAAK,aAAeA,EAAO,OAAS4C,GAAgB,KAAK,OAAO,EAChE,KAAK,MAAQ,KAAK,aAClB,KAAK,WAAU,CAChB,CAED,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACrB,CAED,WAAWhG,EAAS,CAClB,KAAK,QAAU,CAAE,GAAG,KAAK,eACvB,GAAGA,CACT,EACI,KAAK,gBAAgB,KAAK,QAAQ,SAAS,CAC5C,CAED,gBAAiB,CACX,CAAC,KAAK,UAAU,QAAU,KAAK,MAAM,cAAgB,QACvD,KAAK,MAAM,OAAO,IAAI,CAEzB,CAED,QAAQiG,EAASjG,EAAS,CACxB,MAAM+B,EAAOF,EAAY,KAAK,MAAM,KAAMoE,EAAS,KAAK,OAAO,EAE/D,YAAK,SAAS,CACZ,KAAAlE,EACA,KAAM,UACN,cAAe/B,GAAW,KAAO,OAASA,EAAQ,UAClD,OAAQA,GAAW,KAAO,OAASA,EAAQ,MACjD,CAAK,EACM+B,CACR,CAED,SAASmE,EAAOC,EAAiB,CAC/B,KAAK,SAAS,CACZ,KAAM,WACN,MAAAD,EACA,gBAAAC,CACN,CAAK,CACF,CAED,OAAOnG,EAAS,CACd,IAAIoG,EAEJ,MAAM1C,EAAU,KAAK,QACrB,OAAC0C,EAAgB,KAAK,UAAY,MAAgBA,EAAc,OAAOpG,CAAO,EACvE0D,EAAUA,EAAQ,KAAK1F,CAAI,EAAE,MAAMA,CAAI,EAAI,QAAQ,SAC3D,CAED,SAAU,CACR,MAAM,QAAO,EACb,KAAK,OAAO,CACV,OAAQ,EACd,CAAK,CACF,CAED,OAAQ,CACN,KAAK,QAAO,EACZ,KAAK,SAAS,KAAK,YAAY,CAChC,CAED,UAAW,CACT,OAAO,KAAK,UAAU,KAAKqI,GAAYA,EAAS,QAAQ,UAAY,EAAK,CAC1E,CAED,YAAa,CACX,OAAO,KAAK,kBAAmB,EAAG,GAAK,CAAC,KAAK,UAC9C,CAED,SAAU,CACR,OAAO,KAAK,MAAM,eAAiB,CAAC,KAAK,MAAM,eAAiB,KAAK,UAAU,KAAKA,GAAYA,EAAS,iBAAgB,EAAG,OAAO,CACpI,CAED,cAAc7H,EAAY,EAAG,CAC3B,OAAO,KAAK,MAAM,eAAiB,CAAC,KAAK,MAAM,eAAiB,CAACF,GAAe,KAAK,MAAM,cAAeE,CAAS,CACpH,CAED,SAAU,CACR,IAAI8H,EAEJ,MAAMD,EAAW,KAAK,UAAU,KAAKE,GAAKA,EAAE,yBAAwB,CAAE,EAElEF,GACFA,EAAS,QAAQ,CACf,cAAe,EACvB,CAAO,GAIFC,EAAiB,KAAK,UAAY,MAAgBA,EAAe,UACnE,CAED,UAAW,CACT,IAAIE,EAEJ,MAAMH,EAAW,KAAK,UAAU,KAAKE,GAAKA,EAAE,uBAAsB,CAAE,EAEhEF,GACFA,EAAS,QAAQ,CACf,cAAe,EACvB,CAAO,GAIFG,EAAiB,KAAK,UAAY,MAAgBA,EAAe,UACnE,CAED,YAAYH,EAAU,CACf,KAAK,UAAU,SAASA,CAAQ,IACnC,KAAK,UAAU,KAAKA,CAAQ,EAE5B,KAAK,eAAc,EACnB,KAAK,MAAM,OAAO,CAChB,KAAM,gBACN,MAAO,KACP,SAAAA,CACR,CAAO,EAEJ,CAED,eAAeA,EAAU,CACnB,KAAK,UAAU,SAASA,CAAQ,IAClC,KAAK,UAAY,KAAK,UAAU,OAAOE,GAAKA,IAAMF,CAAQ,EAErD,KAAK,UAAU,SAGd,KAAK,UACH,KAAK,oBACP,KAAK,QAAQ,OAAO,CAClB,OAAQ,EACtB,CAAa,EAED,KAAK,QAAQ,eAIjB,KAAK,WAAU,GAGjB,KAAK,MAAM,OAAO,CAChB,KAAM,kBACN,MAAO,KACP,SAAAA,CACR,CAAO,EAEJ,CAED,mBAAoB,CAClB,OAAO,KAAK,UAAU,MACvB,CAED,YAAa,CACN,KAAK,MAAM,eACd,KAAK,SAAS,CACZ,KAAM,YACd,CAAO,CAEJ,CAED,MAAMrG,EAASyG,EAAc,CAC3B,IAAIC,EAAuBC,EAE3B,GAAI,KAAK,MAAM,cAAgB,QAC7B,GAAI,KAAK,MAAM,eAAiBF,GAAgB,MAAQA,EAAa,cAEnE,KAAK,OAAO,CACV,OAAQ,EAClB,CAAS,UACQ,KAAK,QAAS,CACvB,IAAIG,EAGJ,OAACA,EAAiB,KAAK,UAAY,MAAgBA,EAAe,gBAE3D,KAAK,OACb,EAUH,GANI5G,GACF,KAAK,WAAWA,CAAO,EAKrB,CAAC,KAAK,QAAQ,QAAS,CACzB,MAAMqG,EAAW,KAAK,UAAU,KAAKE,GAAKA,EAAE,QAAQ,OAAO,EAEvDF,GACF,KAAK,WAAWA,EAAS,OAAO,CAEnC,CAQD,MAAMQ,EAAkBjF,KAElBkF,EAAiB,CACrB,SAAU,KAAK,SACf,UAAW,OACX,KAAM,KAAK,IACjB,EAIUC,EAAoBC,GAAU,CAClC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,IAAM,CACT,GAAIH,EACF,YAAK,oBAAsB,GACpBA,EAAgB,MAI1B,CACT,CAAO,CACP,EAEIE,EAAkBD,CAAc,EAEhC,MAAMG,EAAU,IACT,KAAK,QAAQ,SAIlB,KAAK,oBAAsB,GACpB,KAAK,QAAQ,QAAQH,CAAc,GAJjC,QAAQ,OAAO,iCAAmC,KAAK,QAAQ,UAAY,GAAG,EAQnFI,EAAU,CACd,aAAAT,EACA,QAAS,KAAK,QACd,SAAU,KAAK,SACf,MAAO,KAAK,MACZ,QAAAQ,CACN,EAMI,GALAF,EAAkBG,CAAO,GACxBR,EAAwB,KAAK,QAAQ,WAAa,MAAgBA,EAAsB,QAAQQ,CAAO,EAExG,KAAK,YAAc,KAAK,MAEpB,KAAK,MAAM,cAAgB,QAAU,KAAK,MAAM,cAAgBP,EAAwBO,EAAQ,eAAiB,KAAO,OAASP,EAAsB,MAAO,CAChK,IAAIQ,EAEJ,KAAK,SAAS,CACZ,KAAM,QACN,MAAOA,EAAyBD,EAAQ,eAAiB,KAAO,OAASC,EAAuB,IACxG,CAAO,CACF,CAED,MAAMC,EAAU5C,GAAS,CASvB,GAPMtB,EAAiBsB,CAAK,GAAKA,EAAM,QACrC,KAAK,SAAS,CACZ,KAAM,QACN,MAAOA,CACjB,CAAS,EAGC,CAACtB,EAAiBsB,CAAK,EAAG,CAC5B,IAAI6C,EAAuBC,EAAoBC,EAAwBC,GAGtEH,GAAyBC,EAAqB,KAAK,MAAM,QAAQ,UAAY,MAAgBD,EAAsB,KAAKC,EAAoB9C,EAAO,IAAI,GACvJ+C,GAA0BC,EAAsB,KAAK,MAAM,QAAQ,YAAc,MAAgBD,EAAuB,KAAKC,EAAqB,KAAK,MAAM,KAAMhD,EAAO,IAAI,CAKhL,CAEI,KAAK,sBAER,KAAK,WAAU,EAGjB,KAAK,qBAAuB,EAClC,EAGI,YAAK,QAAUrB,GAAc,CAC3B,GAAI+D,EAAQ,QACZ,MAAOL,GAAmB,KAAO,OAASA,EAAgB,MAAM,KAAKA,CAAe,EACpF,UAAW9E,GAAQ,CACjB,IAAI0F,EAAwBC,EAAqBC,EAAwBC,EAEzE,GAAI,OAAO7F,EAAS,IAAa,CAK/BqF,EAAQ,IAAI,MAAM,KAAK,UAAY,oBAAoB,CAAC,EACxD,MACD,CAED,KAAK,QAAQrF,CAAI,GAEhB0F,GAA0BC,EAAsB,KAAK,MAAM,QAAQ,YAAc,MAAgBD,EAAuB,KAAKC,EAAqB3F,EAAM,IAAI,GAC5J4F,GAA0BC,EAAsB,KAAK,MAAM,QAAQ,YAAc,MAAgBD,EAAuB,KAAKC,EAAqB7F,EAAM,KAAK,MAAM,MAAO,IAAI,EAE1K,KAAK,sBAER,KAAK,WAAU,EAGjB,KAAK,qBAAuB,EAC7B,EACD,QAAAqF,EACA,OAAQ,CAACtE,EAAc0B,IAAU,CAC/B,KAAK,SAAS,CACZ,KAAM,SACN,aAAA1B,EACA,MAAA0B,CACV,CAAS,CACF,EACD,QAAS,IAAM,CACb,KAAK,SAAS,CACZ,KAAM,OAChB,CAAS,CACF,EACD,WAAY,IAAM,CAChB,KAAK,SAAS,CACZ,KAAM,UAChB,CAAS,CACF,EACD,MAAO0C,EAAQ,QAAQ,MACvB,WAAYA,EAAQ,QAAQ,WAC5B,YAAaA,EAAQ,QAAQ,WACnC,CAAK,EACD,KAAK,QAAU,KAAK,QAAQ,QACrB,KAAK,OACb,CAED,SAASW,EAAQ,CACf,MAAMC,EAAU5B,GAAS,CACvB,IAAI6B,EAAcC,EAElB,OAAQH,EAAO,KAAI,CACjB,IAAK,SACH,MAAO,CAAE,GAAG3B,EACV,kBAAmB2B,EAAO,aAC1B,mBAAoBA,EAAO,KACvC,EAEQ,IAAK,QACH,MAAO,CAAE,GAAG3B,EACV,YAAa,QACzB,EAEQ,IAAK,WACH,MAAO,CAAE,GAAGA,EACV,YAAa,UACzB,EAEQ,IAAK,QACH,MAAO,CAAE,GAAGA,EACV,kBAAmB,EACnB,mBAAoB,KACpB,WAAY6B,EAAeF,EAAO,OAAS,KAAOE,EAAe,KACjE,YAAahF,EAAS,KAAK,QAAQ,WAAW,EAAI,WAAa,SAC/D,GAAI,CAACmD,EAAM,eAAiB,CAC1B,MAAO,KACP,OAAQ,SACtB,CACA,EAEQ,IAAK,UACH,MAAO,CAAE,GAAGA,EACV,KAAM2B,EAAO,KACb,gBAAiB3B,EAAM,gBAAkB,EACzC,eAAgB8B,EAAwBH,EAAO,gBAAkB,KAAOG,EAAwB,KAAK,IAAK,EAC1G,MAAO,KACP,cAAe,GACf,OAAQ,UACR,GAAI,CAACH,EAAO,QAAU,CACpB,YAAa,OACb,kBAAmB,EACnB,mBAAoB,IAClC,CACA,EAEQ,IAAK,QACH,MAAMrD,EAAQqD,EAAO,MAErB,OAAI3E,EAAiBsB,CAAK,GAAKA,EAAM,QAAU,KAAK,YAC3C,CAAE,GAAG,KAAK,YACf,YAAa,MAC3B,EAGiB,CAAE,GAAG0B,EACV,MAAO1B,EACP,iBAAkB0B,EAAM,iBAAmB,EAC3C,eAAgB,KAAK,IAAK,EAC1B,kBAAmBA,EAAM,kBAAoB,EAC7C,mBAAoB1B,EACpB,YAAa,OACb,OAAQ,OACpB,EAEQ,IAAK,aACH,MAAO,CAAE,GAAG0B,EACV,cAAe,EAC3B,EAEQ,IAAK,WACH,MAAO,CAAE,GAAGA,EACV,GAAG2B,EAAO,KACtB,CACO,CACP,EAEI,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/BlC,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAQS,GAAY,CACjCA,EAAS,cAAcwB,CAAM,CACrC,CAAO,EACD,KAAK,MAAM,OAAO,CAChB,MAAO,KACP,KAAM,UACN,OAAAA,CACR,CAAO,CACP,CAAK,CACF,CAEH,CAEA,SAAS7B,GAAgBhG,EAAS,CAChC,MAAM+B,EAAO,OAAO/B,EAAQ,aAAgB,WAAaA,EAAQ,YAAW,EAAKA,EAAQ,YACnFiI,EAAU,OAAOlG,EAAS,IAC1BmG,EAAuBD,EAAU,OAAOjI,EAAQ,sBAAyB,WAAaA,EAAQ,qBAAsB,EAAGA,EAAQ,qBAAuB,EAC5J,MAAO,CACL,KAAA+B,EACA,gBAAiB,EACjB,cAAekG,EAAUC,GAAsD,KAAK,IAAG,EAAK,EAC5F,MAAO,KACP,iBAAkB,EAClB,eAAgB,EAChB,kBAAmB,EACnB,mBAAoB,KACpB,UAAW,KACX,cAAe,GACf,OAAQD,EAAU,UAAY,UAC9B,YAAa,MACjB,CACA,CCjdA,MAAME,WAAmBvK,CAAa,CACpC,YAAYwF,EAAQ,CAClB,QACA,KAAK,OAASA,GAAU,GACxB,KAAK,QAAU,GACf,KAAK,WAAa,EACnB,CAED,MAAMgF,EAAQpI,EAASkG,EAAO,CAC5B,IAAImC,EAEJ,MAAM/I,EAAWU,EAAQ,SACnBsI,GAAaD,EAAqBrI,EAAQ,YAAc,KAAOqI,EAAqB7I,EAAsBF,EAAUU,CAAO,EACjI,IAAIf,EAAQ,KAAK,IAAIqJ,CAAS,EAE9B,OAAKrJ,IACHA,EAAQ,IAAI8G,GAAM,CAChB,MAAO,KACP,OAAQqC,EAAO,UAAW,EAC1B,SAAA9I,EACA,UAAAgJ,EACA,QAASF,EAAO,oBAAoBpI,CAAO,EAC3C,MAAAkG,EACA,eAAgBkC,EAAO,iBAAiB9I,CAAQ,CACxD,CAAO,EACD,KAAK,IAAIL,CAAK,GAGTA,CACR,CAED,IAAIA,EAAO,CACJ,KAAK,WAAWA,EAAM,SAAS,IAClC,KAAK,WAAWA,EAAM,SAAS,EAAIA,EACnC,KAAK,QAAQ,KAAKA,CAAK,EACvB,KAAK,OAAO,CACV,KAAM,QACN,MAAAA,CACR,CAAO,EAEJ,CAED,OAAOA,EAAO,CACZ,MAAMsJ,EAAa,KAAK,WAAWtJ,EAAM,SAAS,EAE9CsJ,IACFtJ,EAAM,QAAO,EACb,KAAK,QAAU,KAAK,QAAQ,OAAOsH,GAAKA,IAAMtH,CAAK,EAE/CsJ,IAAetJ,GACjB,OAAO,KAAK,WAAWA,EAAM,SAAS,EAGxC,KAAK,OAAO,CACV,KAAM,UACN,MAAAA,CACR,CAAO,EAEJ,CAED,OAAQ,CACN2G,EAAc,MAAM,IAAM,CACxB,KAAK,QAAQ,QAAQ3G,GAAS,CAC5B,KAAK,OAAOA,CAAK,CACzB,CAAO,CACP,CAAK,CACF,CAED,IAAIqJ,EAAW,CACb,OAAO,KAAK,WAAWA,CAAS,CACjC,CAED,QAAS,CACP,OAAO,KAAK,OACb,CAKD,KAAK5J,EAAMC,EAAM,CACf,KAAM,CAACK,CAAO,EAAIF,EAAgBJ,EAAMC,CAAI,EAE5C,OAAI,OAAOK,EAAQ,MAAU,MAC3BA,EAAQ,MAAQ,IAGX,KAAK,QAAQ,KAAKC,GAASF,EAAWC,EAASC,CAAK,CAAC,CAC7D,CAKD,QAAQP,EAAMC,EAAM,CAClB,KAAM,CAACK,CAAO,EAAIF,EAAgBJ,EAAMC,CAAI,EAC5C,OAAO,OAAO,KAAKK,CAAO,EAAE,OAAS,EAAI,KAAK,QAAQ,OAAOC,GAASF,EAAWC,EAASC,CAAK,CAAC,EAAI,KAAK,OAC1G,CAED,OAAOyD,EAAO,CACZkD,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAQ,CAAC,CACtB,SAAA/H,CACR,IAAY,CACJA,EAAS6E,CAAK,CACtB,CAAO,CACP,CAAK,CACF,CAED,SAAU,CACRkD,EAAc,MAAM,IAAM,CACxB,KAAK,QAAQ,QAAQ3G,GAAS,CAC5BA,EAAM,QAAO,CACrB,CAAO,CACP,CAAK,CACF,CAED,UAAW,CACT2G,EAAc,MAAM,IAAM,CACxB,KAAK,QAAQ,QAAQ3G,GAAS,CAC5BA,EAAM,SAAQ,CACtB,CAAO,CACP,CAAK,CACF,CAEH,CC3HA,MAAMuJ,WAAiB3C,EAAU,CAC/B,YAAYzC,EAAQ,CAClB,QACA,KAAK,eAAiBA,EAAO,eAC7B,KAAK,WAAaA,EAAO,WACzB,KAAK,cAAgBA,EAAO,cAC5B,KAAK,OAASA,EAAO,QAAU2B,EAC/B,KAAK,UAAY,GACjB,KAAK,MAAQ3B,EAAO,OAAS4C,GAAe,EAC5C,KAAK,WAAW5C,EAAO,OAAO,EAC9B,KAAK,WAAU,CAChB,CAED,WAAWpD,EAAS,CAClB,KAAK,QAAU,CAAE,GAAG,KAAK,eACvB,GAAGA,CACT,EACI,KAAK,gBAAgB,KAAK,QAAQ,SAAS,CAC5C,CAED,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACrB,CAED,SAASkG,EAAO,CACd,KAAK,SAAS,CACZ,KAAM,WACN,MAAAA,CACN,CAAK,CACF,CAED,YAAYG,EAAU,CACf,KAAK,UAAU,SAASA,CAAQ,IACnC,KAAK,UAAU,KAAKA,CAAQ,EAE5B,KAAK,eAAc,EACnB,KAAK,cAAc,OAAO,CACxB,KAAM,gBACN,SAAU,KACV,SAAAA,CACR,CAAO,EAEJ,CAED,eAAeA,EAAU,CACvB,KAAK,UAAY,KAAK,UAAU,OAAOE,GAAKA,IAAMF,CAAQ,EAC1D,KAAK,WAAU,EACf,KAAK,cAAc,OAAO,CACxB,KAAM,kBACN,SAAU,KACV,SAAAA,CACN,CAAK,CACF,CAED,gBAAiB,CACV,KAAK,UAAU,SACd,KAAK,MAAM,SAAW,UACxB,KAAK,WAAU,EAEf,KAAK,cAAc,OAAO,IAAI,EAGnC,CAED,UAAW,CACT,IAAIoC,EAAuBrC,EAE3B,OAAQqC,GAAyBrC,EAAgB,KAAK,UAAY,KAAO,OAASA,EAAc,SAAQ,IAAO,KAAOqC,EAAwB,KAAK,SACpJ,CAED,MAAM,SAAU,CACd,MAAMC,EAAkB,IAAM,CAC5B,IAAIC,EAEJ,YAAK,QAAUxF,GAAc,CAC3B,GAAI,IACG,KAAK,QAAQ,WAIX,KAAK,QAAQ,WAAW,KAAK,MAAM,SAAS,EAH1C,QAAQ,OAAO,qBAAqB,EAK/C,OAAQ,CAACL,EAAc0B,IAAU,CAC/B,KAAK,SAAS,CACZ,KAAM,SACN,aAAA1B,EACA,MAAA0B,CACZ,CAAW,CACF,EACD,QAAS,IAAM,CACb,KAAK,SAAS,CACZ,KAAM,OAClB,CAAW,CACF,EACD,WAAY,IAAM,CAChB,KAAK,SAAS,CACZ,KAAM,UAClB,CAAW,CACF,EACD,OAAQmE,EAAsB,KAAK,QAAQ,QAAU,KAAOA,EAAsB,EAClF,WAAY,KAAK,QAAQ,WACzB,YAAa,KAAK,QAAQ,WAClC,CAAO,EACM,KAAK,QAAQ,OAC1B,EAEUC,EAAW,KAAK,MAAM,SAAW,UAEvC,GAAI,CACF,IAAIC,EAAwBC,EAAwBC,EAAuBC,EAAgBC,EAAwBC,EAAwBC,EAAuBC,EAElK,GAAI,CAACR,EAAU,CACb,IAAIS,EAAuBC,EAAwBC,EAAuBC,EAE1E,KAAK,SAAS,CACZ,KAAM,UACN,UAAW,KAAK,QAAQ,SAClC,CAAS,EAED,OAAQH,GAAyBC,EAAyB,KAAK,cAAc,QAAQ,WAAa,KAAO,OAASD,EAAsB,KAAKC,EAAwB,KAAK,MAAM,UAAW,IAAI,GAC/L,MAAMpC,EAAU,OAAQqC,GAAyBC,EAAgB,KAAK,SAAS,WAAa,KAAO,OAASD,EAAsB,KAAKC,EAAe,KAAK,MAAM,SAAS,GAEtKtC,IAAY,KAAK,MAAM,SACzB,KAAK,SAAS,CACZ,KAAM,UACN,QAAAA,EACA,UAAW,KAAK,MAAM,SAClC,CAAW,CAEJ,CAED,MAAMnF,EAAO,MAAM2G,IAEnB,cAAQG,GAA0BC,EAAyB,KAAK,cAAc,QAAQ,YAAc,KAAO,OAASD,EAAuB,KAAKC,EAAwB/G,EAAM,KAAK,MAAM,UAAW,KAAK,MAAM,QAAS,IAAI,GAC5N,OAAQgH,GAAyBC,EAAiB,KAAK,SAAS,YAAc,KAAO,OAASD,EAAsB,KAAKC,EAAgBjH,EAAM,KAAK,MAAM,UAAW,KAAK,MAAM,OAAO,GAEvL,OAAQkH,GAA0BC,EAAyB,KAAK,cAAc,QAAQ,YAAc,KAAO,OAASD,EAAuB,KAAKC,EAAwBnH,EAAM,KAAM,KAAK,MAAM,UAAW,KAAK,MAAM,QAAS,IAAI,GAClO,OAAQoH,GAAyBC,EAAiB,KAAK,SAAS,YAAc,KAAO,OAASD,EAAsB,KAAKC,EAAgBrH,EAAM,KAAM,KAAK,MAAM,UAAW,KAAK,MAAM,OAAO,GAC7L,KAAK,SAAS,CACZ,KAAM,UACN,KAAAA,CACR,CAAO,EACMA,CACR,OAAQyC,EAAO,CACd,GAAI,CACF,IAAIiF,EAAwBC,EAAwBC,EAAuBC,EAAgBC,EAAwBC,EAAyBC,EAAwBC,EAGpK,aAAQP,GAA0BC,EAAyB,KAAK,cAAc,QAAQ,UAAY,KAAO,OAASD,EAAuB,KAAKC,EAAwBlF,EAAO,KAAK,MAAM,UAAW,KAAK,MAAM,QAAS,IAAI,GAM3N,OAAQmF,GAAyBC,EAAiB,KAAK,SAAS,UAAY,KAAO,OAASD,EAAsB,KAAKC,EAAgBpF,EAAO,KAAK,MAAM,UAAW,KAAK,MAAM,OAAO,GAEtL,OAAQqF,GAA0BC,EAA0B,KAAK,cAAc,QAAQ,YAAc,KAAO,OAASD,EAAuB,KAAKC,EAAyB,OAAWtF,EAAO,KAAK,MAAM,UAAW,KAAK,MAAM,QAAS,IAAI,GAC1O,OAAQuF,GAA0BC,EAAiB,KAAK,SAAS,YAAc,KAAO,OAASD,EAAuB,KAAKC,EAAgB,OAAWxF,EAAO,KAAK,MAAM,UAAW,KAAK,MAAM,OAAO,GAC/LA,CACd,QAAgB,CACR,KAAK,SAAS,CACZ,KAAM,QACN,MAAOA,CACjB,CAAS,CACF,CACF,CACF,CAED,SAASqD,EAAQ,CACf,MAAMC,EAAU5B,GAAS,CACvB,OAAQ2B,EAAO,KAAI,CACjB,IAAK,SACH,MAAO,CAAE,GAAG3B,EACV,aAAc2B,EAAO,aACrB,cAAeA,EAAO,KAClC,EAEQ,IAAK,QACH,MAAO,CAAE,GAAG3B,EACV,SAAU,EACtB,EAEQ,IAAK,WACH,MAAO,CAAE,GAAGA,EACV,SAAU,EACtB,EAEQ,IAAK,UACH,MAAO,CAAE,GAAGA,EACV,QAAS2B,EAAO,QAChB,KAAM,OACN,aAAc,EACd,cAAe,KACf,MAAO,KACP,SAAU,CAAC9E,EAAS,KAAK,QAAQ,WAAW,EAC5C,OAAQ,UACR,UAAW8E,EAAO,SAC9B,EAEQ,IAAK,UACH,MAAO,CAAE,GAAG3B,EACV,KAAM2B,EAAO,KACb,aAAc,EACd,cAAe,KACf,MAAO,KACP,OAAQ,UACR,SAAU,EACtB,EAEQ,IAAK,QACH,MAAO,CAAE,GAAG3B,EACV,KAAM,OACN,MAAO2B,EAAO,MACd,aAAc3B,EAAM,aAAe,EACnC,cAAe2B,EAAO,MACtB,SAAU,GACV,OAAQ,OACpB,EAEQ,IAAK,WACH,MAAO,CAAE,GAAG3B,EACV,GAAG2B,EAAO,KACtB,CACO,CACP,EAEI,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/BlC,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAQS,GAAY,CACjCA,EAAS,iBAAiBwB,CAAM,CACxC,CAAO,EACD,KAAK,cAAc,OAAO,CACxB,SAAU,KACV,KAAM,UACN,OAAAA,CACR,CAAO,CACP,CAAK,CACF,CAEH,CACA,SAAS7B,IAAkB,CACzB,MAAO,CACL,QAAS,OACT,KAAM,OACN,MAAO,KACP,aAAc,EACd,cAAe,KACf,SAAU,GACV,OAAQ,OACR,UAAW,MACf,CACA,CC3PA,MAAMiE,WAAsBrM,CAAa,CACvC,YAAYwF,EAAQ,CAClB,QACA,KAAK,OAASA,GAAU,GACxB,KAAK,UAAY,GACjB,KAAK,WAAa,CACnB,CAED,MAAMgF,EAAQpI,EAASkG,EAAO,CAC5B,MAAMtG,EAAW,IAAI4I,GAAS,CAC5B,cAAe,KACf,OAAQJ,EAAO,UAAW,EAC1B,WAAY,EAAE,KAAK,WACnB,QAASA,EAAO,uBAAuBpI,CAAO,EAC9C,MAAAkG,EACA,eAAgBlG,EAAQ,YAAcoI,EAAO,oBAAoBpI,EAAQ,WAAW,EAAI,MAC9F,CAAK,EACD,YAAK,IAAIJ,CAAQ,EACVA,CACR,CAED,IAAIA,EAAU,CACZ,KAAK,UAAU,KAAKA,CAAQ,EAC5B,KAAK,OAAO,CACV,KAAM,QACN,SAAAA,CACN,CAAK,CACF,CAED,OAAOA,EAAU,CACf,KAAK,UAAY,KAAK,UAAU,OAAO2G,GAAKA,IAAM3G,CAAQ,EAC1D,KAAK,OAAO,CACV,KAAM,UACN,SAAAA,CACN,CAAK,CACF,CAED,OAAQ,CACNgG,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAQhG,GAAY,CACjC,KAAK,OAAOA,CAAQ,CAC5B,CAAO,CACP,CAAK,CACF,CAED,QAAS,CACP,OAAO,KAAK,SACb,CAED,KAAKZ,EAAS,CACZ,OAAI,OAAOA,EAAQ,MAAU,MAC3BA,EAAQ,MAAQ,IAGX,KAAK,UAAU,KAAKY,GAAYD,EAAcX,EAASY,CAAQ,CAAC,CACxE,CAED,QAAQZ,EAAS,CACf,OAAO,KAAK,UAAU,OAAOY,GAAYD,EAAcX,EAASY,CAAQ,CAAC,CAC1E,CAED,OAAO8C,EAAO,CACZkD,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAQ,CAAC,CACtB,SAAA/H,CACR,IAAY,CACJA,EAAS6E,CAAK,CACtB,CAAO,CACP,CAAK,CACF,CAED,uBAAwB,CACtB,IAAIwH,EAEJ,YAAK,WAAaA,EAAiB,KAAK,WAAa,KAAOA,EAAiB,QAAQ,QAAS,GAAE,KAAK,IAAM,CACzG,MAAMC,EAAkB,KAAK,UAAU,OAAO5D,GAAKA,EAAE,MAAM,QAAQ,EACnE,OAAOX,EAAc,MAAM,IAAMuE,EAAgB,OAAO,CAACzG,EAAS9D,IAAa8D,EAAQ,KAAK,IAAM9D,EAAS,SAAU,EAAC,MAAM5B,CAAI,CAAC,EAAG,QAAQ,QAAS,CAAA,CAAC,CAC5J,CAAK,EAAE,KAAK,IAAM,CACZ,KAAK,SAAW,MACtB,CAAK,EACM,KAAK,QACb,CAEH,CCzFA,SAASoM,IAAwB,CAC/B,MAAO,CACL,QAASlD,GAAW,CAClBA,EAAQ,QAAU,IAAM,CACtB,IAAIP,EAAuBQ,EAAwBkD,EAAwBC,EAAwBC,EAAqBC,EAExH,MAAMC,GAAe9D,EAAwBO,EAAQ,eAAiB,OAAiBC,EAAyBR,EAAsB,OAAS,KAAlE,OAAkFQ,EAAuB,YAChLuD,GAAaL,EAAyBnD,EAAQ,eAAiB,OAAiBoD,EAAyBD,EAAuB,OAAS,KAAnE,OAAmFC,EAAuB,UAChLK,EAAYD,GAAa,KAAO,OAASA,EAAU,UACnDE,GAAsBF,GAAa,KAAO,OAASA,EAAU,aAAe,UAC5EG,GAA0BH,GAAa,KAAO,OAASA,EAAU,aAAe,WAChFI,IAAaP,EAAsBrD,EAAQ,MAAM,OAAS,KAAO,OAASqD,EAAoB,QAAU,CAAA,EACxGQ,IAAkBP,EAAuBtD,EAAQ,MAAM,OAAS,KAAO,OAASsD,EAAqB,aAAe,CAAA,EAC1H,IAAIQ,EAAgBD,EAChBE,EAAY,GAEhB,MAAMlE,EAAoBC,GAAU,CAClC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,IAAM,CACT,IAAIkE,EAEJ,IAAKA,EAAkBhE,EAAQ,SAAW,MAAQgE,EAAgB,QAChED,EAAY,OACP,CACL,IAAIE,GAEHA,EAAmBjE,EAAQ,SAAW,MAAgBiE,EAAiB,iBAAiB,QAAS,IAAM,CACtGF,EAAY,EAC9B,CAAiB,CACF,CAED,OAAO/D,EAAQ,MAChB,CACb,CAAW,CACX,EAGckE,EAAUlE,EAAQ,QAAQ,UAAY,IAAM,QAAQ,OAAO,iCAAmCA,EAAQ,QAAQ,UAAY,GAAG,GAE7HmE,EAAgB,CAACC,EAAOC,EAAOC,EAAMC,KACzCT,EAAgBS,EAAW,CAACF,EAAO,GAAGP,CAAa,EAAI,CAAC,GAAGA,EAAeO,CAAK,EACxEE,EAAW,CAACD,EAAM,GAAGF,CAAK,EAAI,CAAC,GAAGA,EAAOE,CAAI,GAIhDE,EAAY,CAACJ,EAAOK,EAAQJ,EAAOE,IAAa,CACpD,GAAIR,EACF,OAAO,QAAQ,OAAO,WAAW,EAGnC,GAAI,OAAOM,EAAU,KAAe,CAACI,GAAUL,EAAM,OACnD,OAAO,QAAQ,QAAQA,CAAK,EAG9B,MAAMxE,EAAiB,CACrB,SAAUI,EAAQ,SAClB,UAAWqE,EACX,KAAMrE,EAAQ,QAAQ,IAClC,EACUH,EAAkBD,CAAc,EAChC,MAAM8E,EAAgBR,EAAQtE,CAAc,EAE5C,OADgB,QAAQ,QAAQ8E,CAAa,EAAE,KAAKJ,IAAQH,EAAcC,EAAOC,EAAOC,GAAMC,CAAQ,CAAC,CAEjH,EAEQ,IAAI/H,EAEJ,GAAI,CAACoH,EAAS,OACZpH,EAAUgI,EAAU,CAAA,CAAE,UAEfd,EAAoB,CAC3B,MAAMe,EAAS,OAAOhB,EAAc,IAC9BY,EAAQI,EAAShB,EAAYkB,GAAiB3E,EAAQ,QAAS4D,CAAQ,EAC7EpH,EAAUgI,EAAUZ,EAAUa,EAAQJ,CAAK,CAC5C,SACQV,EAAwB,CAC/B,MAAMc,EAAS,OAAOhB,EAAc,IAC9BY,EAAQI,EAAShB,EAAYmB,GAAqB5E,EAAQ,QAAS4D,CAAQ,EACjFpH,EAAUgI,EAAUZ,EAAUa,EAAQJ,EAAO,EAAI,CAClD,KACI,CACHP,EAAgB,CAAA,EAChB,MAAMW,EAAS,OAAOzE,EAAQ,QAAQ,iBAAqB,IAG3DxD,GAF6B+G,GAAeK,EAAS,CAAC,EAAIL,EAAYK,EAAS,CAAC,EAAG,EAAGA,CAAQ,EAAI,IAEjEY,EAAU,CAAA,EAAIC,EAAQZ,EAAc,CAAC,CAAC,EAAI,QAAQ,QAAQM,EAAc,CAAE,EAAEN,EAAc,CAAC,EAAGD,EAAS,CAAC,CAAC,CAAC,EAE3I,QAAS7J,EAAI,EAAGA,EAAI6J,EAAS,OAAQ7J,IACnCyC,EAAUA,EAAQ,KAAK4H,GAAS,CAG9B,GAF4Bb,GAAeK,EAAS7J,CAAC,EAAIwJ,EAAYK,EAAS7J,CAAC,EAAGA,EAAG6J,CAAQ,EAAI,GAExE,CACvB,MAAMS,EAAQI,EAASZ,EAAc9J,CAAC,EAAI4K,GAAiB3E,EAAQ,QAASoE,CAAK,EACjF,OAAOI,EAAUJ,EAAOK,EAAQJ,CAAK,CACtC,CAED,OAAO,QAAQ,QAAQF,EAAcC,EAAOP,EAAc9J,CAAC,EAAG6J,EAAS7J,CAAC,CAAC,CAAC,CACxF,CAAa,CAEJ,CAMD,OAJqByC,EAAQ,KAAK4H,IAAU,CAC1C,MAAAA,EACA,WAAYN,CACb,EAAC,CAEV,CACK,CACL,CACA,CACA,SAASa,GAAiB7L,EAASsL,EAAO,CACxC,OAAOtL,EAAQ,kBAAoB,KAAO,OAASA,EAAQ,iBAAiBsL,EAAMA,EAAM,OAAS,CAAC,EAAGA,CAAK,CAC5G,CACA,SAASQ,GAAqB9L,EAASsL,EAAO,CAC5C,OAAOtL,EAAQ,sBAAwB,KAAO,OAASA,EAAQ,qBAAqBsL,EAAM,CAAC,EAAGA,CAAK,CACrG,CC1GA,MAAMS,EAAY,CAChB,YAAY3I,EAAS,GAAI,CACvB,KAAK,WAAaA,EAAO,YAAc,IAAI+E,GAC3C,KAAK,cAAgB/E,EAAO,eAAiB,IAAI6G,GACjD,KAAK,OAAS7G,EAAO,QAAU2B,EAC/B,KAAK,eAAiB3B,EAAO,gBAAkB,CAAA,EAC/C,KAAK,cAAgB,GACrB,KAAK,iBAAmB,GACxB,KAAK,WAAa,CAKnB,CAED,OAAQ,CACN,KAAK,aACD,KAAK,aAAe,IACxB,KAAK,iBAAmBd,EAAa,UAAU,IAAM,CAC/CA,EAAa,cACf,KAAK,sBAAqB,EAC1B,KAAK,WAAW,UAExB,CAAK,EACD,KAAK,kBAAoBM,EAAc,UAAU,IAAM,CACjDA,EAAc,aAChB,KAAK,sBAAqB,EAC1B,KAAK,WAAW,WAExB,CAAK,EACF,CAED,SAAU,CACR,IAAIoJ,EAAuBC,EAE3B,KAAK,aACD,KAAK,aAAe,KACvBD,EAAwB,KAAK,mBAAqB,MAAgBA,EAAsB,KAAK,IAAI,EAClG,KAAK,iBAAmB,QACvBC,EAAwB,KAAK,oBAAsB,MAAgBA,EAAsB,KAAK,IAAI,EACnG,KAAK,kBAAoB,OAC1B,CAKD,WAAWvN,EAAMC,EAAM,CACrB,KAAM,CAACK,CAAO,EAAIF,EAAgBJ,EAAMC,CAAI,EAC5C,OAAAK,EAAQ,YAAc,WACf,KAAK,WAAW,QAAQA,CAAO,EAAE,MACzC,CAED,WAAWA,EAAS,CAClB,OAAO,KAAK,cAAc,QAAQ,CAAE,GAAGA,EACrC,SAAU,EACX,CAAA,EAAE,MACJ,CAKD,aAAaM,EAAUN,EAAS,CAC9B,IAAIkN,EAEJ,OAAQA,EAAwB,KAAK,WAAW,KAAK5M,EAAUN,CAAO,IAAM,KAAO,OAASkN,EAAsB,MAAM,IACzH,CAKD,gBAAgBxN,EAAMC,EAAMC,EAAM,CAChC,MAAMuN,EAAgB1N,EAAeC,EAAMC,EAAMC,CAAI,EAC/CwN,EAAa,KAAK,aAAaD,EAAc,QAAQ,EAC3D,OAAOC,EAAa,QAAQ,QAAQA,CAAU,EAAI,KAAK,WAAWD,CAAa,CAChF,CAKD,eAAeE,EAAmB,CAChC,OAAO,KAAK,cAAe,EAAC,QAAQA,CAAiB,EAAE,IAAI,CAAC,CAC1D,SAAA/M,EACA,MAAA4G,CACN,IAAU,CACJ,MAAMnE,EAAOmE,EAAM,KACnB,MAAO,CAAC5G,EAAUyC,CAAI,CAC5B,CAAK,CACF,CAED,aAAazC,EAAUpB,EAAS8B,EAAS,CACvC,MAAMf,EAAQ,KAAK,WAAW,KAAKK,CAAQ,EACrCwC,EAAW7C,GAAS,KAAO,OAASA,EAAM,MAAM,KAChD8C,EAAO9D,GAAiBC,EAAS4D,CAAQ,EAE/C,GAAI,OAAOC,EAAS,IAClB,OAGF,MAAMoK,EAAgB1N,EAAea,CAAQ,EACvCgN,EAAmB,KAAK,oBAAoBH,CAAa,EAC/D,OAAO,KAAK,WAAW,MAAM,KAAMG,CAAgB,EAAE,QAAQvK,EAAM,CAAE,GAAG/B,EACtE,OAAQ,EACd,CAAK,CACF,CAKD,eAAeqM,EAAmBnO,EAAS8B,EAAS,CAClD,OAAO4F,EAAc,MAAM,IAAM,KAAK,cAAe,EAAC,QAAQyG,CAAiB,EAAE,IAAI,CAAC,CACpF,SAAA/M,CACN,IAAU,CAACA,EAAU,KAAK,aAAaA,EAAUpB,EAAS8B,CAAO,CAAC,CAAC,CAAC,CACjE,CAED,cAAcV,EAIdN,EAAS,CACP,IAAIuN,EAEJ,OAAQA,EAAyB,KAAK,WAAW,KAAKjN,EAAUN,CAAO,IAAM,KAAO,OAASuN,EAAuB,KACrH,CAKD,cAAc7N,EAAMC,EAAM,CACxB,KAAM,CAACK,CAAO,EAAIF,EAAgBJ,EAAMC,CAAI,EACtC6N,EAAa,KAAK,WACxB5G,EAAc,MAAM,IAAM,CACxB4G,EAAW,QAAQxN,CAAO,EAAE,QAAQC,GAAS,CAC3CuN,EAAW,OAAOvN,CAAK,CAC/B,CAAO,CACP,CAAK,CACF,CAKD,aAAaP,EAAMC,EAAMC,EAAM,CAC7B,KAAM,CAACI,EAASgB,CAAO,EAAIlB,EAAgBJ,EAAMC,EAAMC,CAAI,EACrD4N,EAAa,KAAK,WAClBC,EAAiB,CACrB,KAAM,SACN,GAAGzN,CACT,EACI,OAAO4G,EAAc,MAAM,KACzB4G,EAAW,QAAQxN,CAAO,EAAE,QAAQC,GAAS,CAC3CA,EAAM,MAAK,CACnB,CAAO,EACM,KAAK,eAAewN,EAAgBzM,CAAO,EACnD,CACF,CAKD,cAActB,EAAMC,EAAMC,EAAM,CAC9B,KAAM,CAACI,EAAS8E,EAAgB,CAAE,CAAA,EAAIhF,EAAgBJ,EAAMC,EAAMC,CAAI,EAElE,OAAOkF,EAAc,OAAW,MAClCA,EAAc,OAAS,IAGzB,MAAM4I,EAAW9G,EAAc,MAAM,IAAM,KAAK,WAAW,QAAQ5G,CAAO,EAAE,IAAIC,GAASA,EAAM,OAAO6E,CAAa,CAAC,CAAC,EACrH,OAAO,QAAQ,IAAI4I,CAAQ,EAAE,KAAK1O,CAAI,EAAE,MAAMA,CAAI,CACnD,CAKD,kBAAkBU,EAAMC,EAAMC,EAAM,CAClC,KAAM,CAACI,EAASgB,CAAO,EAAIlB,EAAgBJ,EAAMC,EAAMC,CAAI,EAC3D,OAAOgH,EAAc,MAAM,IAAM,CAC/B,IAAI+G,EAAMC,EAMV,GAJA,KAAK,WAAW,QAAQ5N,CAAO,EAAE,QAAQC,GAAS,CAChDA,EAAM,WAAU,CACxB,CAAO,EAEGD,EAAQ,cAAgB,OAC1B,OAAO,QAAQ,UAGjB,MAAMyN,EAAiB,CAAE,GAAGzN,EAC1B,MAAO2N,GAAQC,EAAuB5N,EAAQ,cAAgB,KAAO4N,EAAuB5N,EAAQ,OAAS,KAAO2N,EAAO,QACnI,EACM,OAAO,KAAK,eAAeF,EAAgBzM,CAAO,CACxD,CAAK,CACF,CAKD,eAAetB,EAAMC,EAAMC,EAAM,CAC/B,KAAM,CAACI,EAASgB,CAAO,EAAIlB,EAAgBJ,EAAMC,EAAMC,CAAI,EACrD8N,EAAW9G,EAAc,MAAM,IAAM,KAAK,WAAW,QAAQ5G,CAAO,EAAE,OAAOC,GAAS,CAACA,EAAM,WAAU,CAAE,EAAE,IAAIA,GAAS,CAC5H,IAAI4N,EAEJ,OAAO5N,EAAM,MAAM,OAAW,CAAE,GAAGe,EACjC,eAAgB6M,EAAwB7M,GAAW,KAAO,OAASA,EAAQ,gBAAkB,KAAO6M,EAAwB,GAC5H,KAAM,CACJ,YAAa7N,EAAQ,WACtB,CACT,CAAO,CACF,CAAA,CAAC,EACF,IAAI0E,EAAU,QAAQ,IAAIgJ,CAAQ,EAAE,KAAK1O,CAAI,EAE7C,OAAMgC,GAAW,MAAQA,EAAQ,eAC/B0D,EAAUA,EAAQ,MAAM1F,CAAI,GAGvB0F,CACR,CAKD,WAAWhF,EAAMC,EAAMC,EAAM,CAC3B,MAAMuN,EAAgB1N,EAAeC,EAAMC,EAAMC,CAAI,EAC/C0N,EAAmB,KAAK,oBAAoBH,CAAa,EAE3D,OAAOG,EAAiB,MAAU,MACpCA,EAAiB,MAAQ,IAG3B,MAAMrN,EAAQ,KAAK,WAAW,MAAM,KAAMqN,CAAgB,EAC1D,OAAOrN,EAAM,cAAcqN,EAAiB,SAAS,EAAIrN,EAAM,MAAMqN,CAAgB,EAAI,QAAQ,QAAQrN,EAAM,MAAM,IAAI,CAC1H,CAKD,cAAcP,EAAMC,EAAMC,EAAM,CAC9B,OAAO,KAAK,WAAWF,EAAMC,EAAMC,CAAI,EAAE,KAAKZ,CAAI,EAAE,MAAMA,CAAI,CAC/D,CAKD,mBAAmBU,EAAMC,EAAMC,EAAM,CACnC,MAAMuN,EAAgB1N,EAAeC,EAAMC,EAAMC,CAAI,EACrD,OAAAuN,EAAc,SAAW/B,KAClB,KAAK,WAAW+B,CAAa,CACrC,CAKD,sBAAsBzN,EAAMC,EAAMC,EAAM,CACtC,OAAO,KAAK,mBAAmBF,EAAMC,EAAMC,CAAI,EAAE,KAAKZ,CAAI,EAAE,MAAMA,CAAI,CACvE,CAED,uBAAwB,CACtB,OAAO,KAAK,cAAc,uBAC3B,CAED,eAAgB,CACd,OAAO,KAAK,UACb,CAED,kBAAmB,CACjB,OAAO,KAAK,aACb,CAED,WAAY,CACV,OAAO,KAAK,MACb,CAED,mBAAoB,CAClB,OAAO,KAAK,cACb,CAED,kBAAkBgC,EAAS,CACzB,KAAK,eAAiBA,CACvB,CAED,iBAAiBV,EAAUU,EAAS,CAClC,MAAMI,EAAS,KAAK,cAAc,KAAKmG,GAAKxG,EAAaT,CAAQ,IAAMS,EAAawG,EAAE,QAAQ,CAAC,EAE3FnG,EACFA,EAAO,eAAiBJ,EAExB,KAAK,cAAc,KAAK,CACtB,SAAAV,EACA,eAAgBU,CACxB,CAAO,CAEJ,CAED,iBAAiBV,EAAU,CACzB,GAAI,CAACA,EACH,OAIF,MAAMwN,EAAwB,KAAK,cAAc,KAAKvG,GAAK9G,EAAgBH,EAAUiH,EAAE,QAAQ,CAAC,EAWhG,OAAOuG,GAAyB,KAAO,OAASA,EAAsB,cACvE,CAED,oBAAoBhN,EAAaE,EAAS,CACxC,MAAMI,EAAS,KAAK,iBAAiB,KAAKmG,GAAKxG,EAAaD,CAAW,IAAMC,EAAawG,EAAE,WAAW,CAAC,EAEpGnG,EACFA,EAAO,eAAiBJ,EAExB,KAAK,iBAAiB,KAAK,CACzB,YAAAF,EACA,eAAgBE,CACxB,CAAO,CAEJ,CAED,oBAAoBF,EAAa,CAC/B,GAAI,CAACA,EACH,OAIF,MAAMgN,EAAwB,KAAK,iBAAiB,KAAKvG,GAAK9G,EAAgBK,EAAayG,EAAE,WAAW,CAAC,EAWzG,OAAOuG,GAAyB,KAAO,OAASA,EAAsB,cACvE,CAED,oBAAoB9M,EAAS,CAC3B,GAAIA,GAAW,MAAQA,EAAQ,WAC7B,OAAOA,EAGT,MAAMsM,EAAmB,CAAE,GAAG,KAAK,eAAe,QAChD,GAAG,KAAK,iBAAiBtM,GAAW,KAAO,OAASA,EAAQ,QAAQ,EACpE,GAAGA,EACH,WAAY,EAClB,EAEI,MAAI,CAACsM,EAAiB,WAAaA,EAAiB,WAClDA,EAAiB,UAAY9M,EAAsB8M,EAAiB,SAAUA,CAAgB,GAI5F,OAAOA,EAAiB,mBAAuB,MACjDA,EAAiB,mBAAqBA,EAAiB,cAAgB,UAGrE,OAAOA,EAAiB,iBAAqB,MAC/CA,EAAiB,iBAAmB,CAAC,CAACA,EAAiB,UAGlDA,CACR,CAED,uBAAuBtM,EAAS,CAC9B,OAAIA,GAAW,MAAQA,EAAQ,WACtBA,EAGF,CAAE,GAAG,KAAK,eAAe,UAC9B,GAAG,KAAK,oBAAoBA,GAAW,KAAO,OAASA,EAAQ,WAAW,EAC1E,GAAGA,EACH,WAAY,EAClB,CACG,CAED,OAAQ,CACN,KAAK,WAAW,QAChB,KAAK,cAAc,OACpB,CAEH,CCxYA,MAAM+M,WAAsBnP,CAAa,CACvC,YAAYwK,EAAQpI,EAAS,CAC3B,QACA,KAAK,OAASoI,EACd,KAAK,QAAUpI,EACf,KAAK,aAAe,IAAI,IACxB,KAAK,YAAc,KACnB,KAAK,YAAW,EAChB,KAAK,WAAWA,CAAO,CACxB,CAED,aAAc,CACZ,KAAK,OAAS,KAAK,OAAO,KAAK,IAAI,EACnC,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,CACtC,CAED,aAAc,CACR,KAAK,UAAU,OAAS,IAC1B,KAAK,aAAa,YAAY,IAAI,EAE9BgN,GAAmB,KAAK,aAAc,KAAK,OAAO,GACpD,KAAK,aAAY,EAGnB,KAAK,aAAY,EAEpB,CAED,eAAgB,CACT,KAAK,gBACR,KAAK,QAAO,CAEf,CAED,wBAAyB,CACvB,OAAOC,EAAc,KAAK,aAAc,KAAK,QAAS,KAAK,QAAQ,kBAAkB,CACtF,CAED,0BAA2B,CACzB,OAAOA,EAAc,KAAK,aAAc,KAAK,QAAS,KAAK,QAAQ,oBAAoB,CACxF,CAED,SAAU,CACR,KAAK,UAAY,IAAI,IACrB,KAAK,kBAAiB,EACtB,KAAK,qBAAoB,EACzB,KAAK,aAAa,eAAe,IAAI,CACtC,CAED,WAAWjN,EAASkN,EAAe,CACjC,MAAMC,EAAc,KAAK,QACnBC,EAAY,KAAK,aAevB,GAdA,KAAK,QAAU,KAAK,OAAO,oBAAoBpN,CAAO,EAMjDkB,EAAoBiM,EAAa,KAAK,OAAO,GAChD,KAAK,OAAO,cAAe,EAAC,OAAO,CACjC,KAAM,yBACN,MAAO,KAAK,aACZ,SAAU,IAClB,CAAO,EAGC,OAAO,KAAK,QAAQ,QAAY,KAAe,OAAO,KAAK,QAAQ,SAAY,UACjF,MAAM,IAAI,MAAM,kCAAkC,EAI/C,KAAK,QAAQ,WAChB,KAAK,QAAQ,SAAWA,EAAY,UAGtC,KAAK,YAAW,EAChB,MAAME,EAAU,KAAK,eAEjBA,GAAWC,GAAsB,KAAK,aAAcF,EAAW,KAAK,QAASD,CAAW,GAC1F,KAAK,aAAY,EAInB,KAAK,aAAaD,CAAa,EAE3BG,IAAY,KAAK,eAAiBD,GAAa,KAAK,QAAQ,UAAYD,EAAY,SAAW,KAAK,QAAQ,YAAcA,EAAY,YACxI,KAAK,mBAAkB,EAGzB,MAAMI,EAAsB,KAAK,yBAE7BF,IAAY,KAAK,eAAiBD,GAAa,KAAK,QAAQ,UAAYD,EAAY,SAAWI,IAAwB,KAAK,yBAC9H,KAAK,sBAAsBA,CAAmB,CAEjD,CAED,oBAAoBvN,EAAS,CAC3B,MAAMf,EAAQ,KAAK,OAAO,cAAa,EAAG,MAAM,KAAK,OAAQe,CAAO,EAC9DI,EAAS,KAAK,aAAanB,EAAOe,CAAO,EAE/C,OAAIwN,GAAsC,KAAMpN,EAAQJ,CAAO,IAgB7D,KAAK,cAAgBI,EACrB,KAAK,qBAAuB,KAAK,QACjC,KAAK,mBAAqB,KAAK,aAAa,OAGvCA,CACR,CAED,kBAAmB,CACjB,OAAO,KAAK,aACb,CAED,YAAYA,EAAQ,CAClB,MAAMqN,EAAgB,CAAA,EACtB,cAAO,KAAKrN,CAAM,EAAE,QAAQC,GAAO,CACjC,OAAO,eAAeoN,EAAepN,EAAK,CACxC,aAAc,GACd,WAAY,GACZ,IAAK,KACH,KAAK,aAAa,IAAIA,CAAG,EAClBD,EAAOC,CAAG,EAE3B,CAAO,CACP,CAAK,EACMoN,CACR,CAED,iBAAkB,CAChB,OAAO,KAAK,YACb,CAED,QAAS,CACP,KAAK,OAAO,cAAa,EAAG,OAAO,KAAK,YAAY,CACrD,CAED,QAAQ,CACN,YAAAhD,EACA,GAAGzK,CACJ,EAAG,GAAI,CACN,OAAO,KAAK,MAAM,CAAE,GAAGA,EACrB,KAAM,CACJ,YAAAyK,CACD,CACP,CAAK,CACF,CAED,gBAAgBzK,EAAS,CACvB,MAAMsM,EAAmB,KAAK,OAAO,oBAAoBtM,CAAO,EAC1Df,EAAQ,KAAK,OAAO,cAAa,EAAG,MAAM,KAAK,OAAQqN,CAAgB,EAC7E,OAAArN,EAAM,qBAAuB,GACtBA,EAAM,MAAO,EAAC,KAAK,IAAM,KAAK,aAAaA,EAAOqN,CAAgB,CAAC,CAC3E,CAED,MAAM7F,EAAc,CAClB,IAAIiH,EAEJ,OAAO,KAAK,aAAa,CAAE,GAAGjH,EAC5B,eAAgBiH,EAAwBjH,EAAa,gBAAkB,KAAOiH,EAAwB,EAC5G,CAAK,EAAE,KAAK,KACN,KAAK,aAAY,EACV,KAAK,cACb,CACF,CAED,aAAajH,EAAc,CAEzB,KAAK,YAAW,EAEhB,IAAI/C,EAAU,KAAK,aAAa,MAAM,KAAK,QAAS+C,CAAY,EAEhE,OAAMA,GAAgB,MAAQA,EAAa,eACzC/C,EAAUA,EAAQ,MAAM1F,CAAI,GAGvB0F,CACR,CAED,oBAAqB,CAGnB,GAFA,KAAK,kBAAiB,EAElB3F,GAAY,KAAK,cAAc,SAAW,CAACK,EAAe,KAAK,QAAQ,SAAS,EAClF,OAMF,MAAMoD,EAHOlD,GAAe,KAAK,cAAc,cAAe,KAAK,QAAQ,SAAS,EAG7D,EACvB,KAAK,eAAiB,WAAW,IAAM,CAChC,KAAK,cAAc,SACtB,KAAK,aAAY,CAEpB,EAAEkD,CAAO,CACX,CAED,wBAAyB,CACvB,IAAImM,EAEJ,OAAO,OAAO,KAAK,QAAQ,iBAAoB,WAAa,KAAK,QAAQ,gBAAgB,KAAK,cAAc,KAAM,KAAK,YAAY,GAAKA,EAAwB,KAAK,QAAQ,kBAAoB,KAAOA,EAAwB,EACjO,CAED,sBAAsBC,EAAc,CAClC,KAAK,qBAAoB,EACzB,KAAK,uBAAyBA,EAE1B,EAAA7P,GAAY,KAAK,QAAQ,UAAY,IAAS,CAACK,EAAe,KAAK,sBAAsB,GAAK,KAAK,yBAA2B,KAIlI,KAAK,kBAAoB,YAAY,IAAM,EACrC,KAAK,QAAQ,6BAA+BkE,EAAa,UAAS,IACpE,KAAK,aAAY,CAEzB,EAAO,KAAK,sBAAsB,EAC/B,CAED,cAAe,CACb,KAAK,mBAAkB,EACvB,KAAK,sBAAsB,KAAK,uBAAwB,CAAA,CACzD,CAED,mBAAoB,CACd,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,OAEzB,CAED,sBAAuB,CACjB,KAAK,oBACP,cAAc,KAAK,iBAAiB,EACpC,KAAK,kBAAoB,OAE5B,CAED,aAAarD,EAAOe,EAAS,CAC3B,MAAMoN,EAAY,KAAK,aACjBD,EAAc,KAAK,QACnBU,EAAa,KAAK,cAClBC,EAAkB,KAAK,mBACvBC,EAAoB,KAAK,qBACzBC,EAAc/O,IAAUmO,EACxBa,EAAoBD,EAAc/O,EAAM,MAAQ,KAAK,yBACrDiP,EAAkBF,EAAc,KAAK,cAAgB,KAAK,oBAC1D,CACJ,MAAA9H,CACD,EAAGjH,EACJ,GAAI,CACF,cAAAkP,EACA,MAAA3J,EACA,eAAA4J,EACA,YAAAhP,EACA,OAAAiP,CACD,EAAGnI,EACAoI,EAAiB,GACjBC,EAAoB,GACpBxM,EAEJ,GAAI/B,EAAQ,mBAAoB,CAC9B,MAAMqN,EAAU,KAAK,eACfmB,EAAe,CAACnB,GAAWL,GAAmB/N,EAAOe,CAAO,EAC5DyO,EAAkBpB,GAAWC,GAAsBrO,EAAOmO,EAAWpN,EAASmN,CAAW,GAE3FqB,GAAgBC,KAClBrP,EAAc2D,EAAS9D,EAAM,QAAQ,WAAW,EAAI,WAAa,SAE5DkP,IACHE,EAAS,YAITrO,EAAQ,qBAAuB,gBACjCZ,EAAc,OAEjB,CAGD,GAAIY,EAAQ,kBAAoB,CAACkG,EAAM,eAAiBgI,GAAmB,MAAQA,EAAgB,WAAaG,IAAW,QACzHtM,EAAOmM,EAAgB,KACvBC,EAAgBD,EAAgB,cAChCG,EAASH,EAAgB,OACzBI,EAAiB,WAEVtO,EAAQ,QAAU,OAAOkG,EAAM,KAAS,IAE/C,GAAI2H,GAAc3H,EAAM,QAAU4H,GAAmB,KAAO,OAASA,EAAgB,OAAS9N,EAAQ,SAAW,KAAK,SACpH+B,EAAO,KAAK,iBAEZ,IAAI,CACF,KAAK,SAAW/B,EAAQ,OACxB+B,EAAO/B,EAAQ,OAAOkG,EAAM,IAAI,EAChCnE,EAAOF,EAAYgM,GAAc,KAAO,OAASA,EAAW,KAAM9L,EAAM/B,CAAO,EAC/E,KAAK,aAAe+B,EACpB,KAAK,YAAc,IACpB,OAAQ2M,EAAa,CAKpB,KAAK,YAAcA,CACpB,MAIH3M,EAAOmE,EAAM,KAIf,GAAI,OAAOlG,EAAQ,gBAAoB,KAAe,OAAO+B,EAAS,KAAesM,IAAW,UAAW,CACzG,IAAIM,EAEJ,GAAId,GAAc,MAAQA,EAAW,mBAAqB7N,EAAQ,mBAAqB+N,GAAqB,KAAO,OAASA,EAAkB,iBAC5IY,EAAkBd,EAAW,aAE7Bc,EAAkB,OAAO3O,EAAQ,iBAAoB,WAAaA,EAAQ,gBAAe,EAAKA,EAAQ,gBAElGA,EAAQ,QAAU,OAAO2O,EAAoB,IAC/C,GAAI,CACFA,EAAkB3O,EAAQ,OAAO2O,CAAe,EAChD,KAAK,YAAc,IACpB,OAAQD,EAAa,CAKpB,KAAK,YAAcA,CACpB,CAID,OAAOC,EAAoB,MAC7BN,EAAS,UACTtM,EAAOF,EAAYgM,GAAc,KAAO,OAASA,EAAW,KAAMc,EAAiB3O,CAAO,EAC1FuO,EAAoB,GAEvB,CAEG,KAAK,cACP/J,EAAQ,KAAK,YACbzC,EAAO,KAAK,aACZqM,EAAiB,KAAK,MACtBC,EAAS,SAGX,MAAMO,EAAaxP,IAAgB,WAC7ByP,EAAYR,IAAW,UACvBS,EAAUT,IAAW,QA4B3B,MA3Be,CACb,OAAAA,EACA,YAAAjP,EACA,UAAAyP,EACA,UAAWR,IAAW,UACtB,QAAAS,EACA,iBAAkBD,GAAaD,EAC/B,KAAA7M,EACA,cAAAoM,EACA,MAAA3J,EACA,eAAA4J,EACA,aAAclI,EAAM,kBACpB,cAAeA,EAAM,mBACrB,iBAAkBA,EAAM,iBACxB,UAAWA,EAAM,gBAAkB,GAAKA,EAAM,iBAAmB,EACjE,oBAAqBA,EAAM,gBAAkB+H,EAAkB,iBAAmB/H,EAAM,iBAAmB+H,EAAkB,iBAC7H,WAAAW,EACA,aAAcA,GAAc,CAACC,EAC7B,eAAgBC,GAAW5I,EAAM,gBAAkB,EACnD,SAAU9G,IAAgB,SAC1B,kBAAAmP,EACA,eAAAD,EACA,eAAgBQ,GAAW5I,EAAM,gBAAkB,EACnD,QAAS6I,EAAQ9P,EAAOe,CAAO,EAC/B,QAAS,KAAK,QACd,OAAQ,KAAK,MACnB,CAEG,CAED,aAAakN,EAAe,CAC1B,MAAMW,EAAa,KAAK,cAClBmB,EAAa,KAAK,aAAa,KAAK,aAAc,KAAK,OAAO,EAIpE,GAHA,KAAK,mBAAqB,KAAK,aAAa,MAC5C,KAAK,qBAAuB,KAAK,QAE7B9N,EAAoB8N,EAAYnB,CAAU,EAC5C,OAGF,KAAK,cAAgBmB,EAErB,MAAMC,EAAuB,CAC3B,MAAO,EACb,EAEUC,EAAwB,IAAM,CAClC,GAAI,CAACrB,EACH,MAAO,GAGT,KAAM,CACJ,oBAAAsB,CACR,EAAU,KAAK,QACHC,EAA2B,OAAOD,GAAwB,WAAaA,EAAmB,EAAKA,EAErG,GAAIC,IAA6B,OAAS,CAACA,GAA4B,CAAC,KAAK,aAAa,KACxF,MAAO,GAGT,MAAMC,EAAgB,IAAI,IAAID,GAA8D,KAAK,YAAY,EAE7G,OAAI,KAAK,QAAQ,kBACfC,EAAc,IAAI,OAAO,EAGpB,OAAO,KAAK,KAAK,aAAa,EAAE,KAAKhP,GAAO,CACjD,MAAMiP,EAAWjP,EAEjB,OADgB,KAAK,cAAciP,CAAQ,IAAMzB,EAAWyB,CAAQ,GAClDD,EAAc,IAAIC,CAAQ,CACpD,CAAO,CACP,GAESpC,GAAiB,KAAO,OAASA,EAAc,aAAe,IAASgC,MAC1ED,EAAqB,UAAY,IAGnC,KAAK,OAAO,CAAE,GAAGA,EACf,GAAG/B,CACT,CAAK,CACF,CAED,aAAc,CACZ,MAAMjO,EAAQ,KAAK,OAAO,cAAe,EAAC,MAAM,KAAK,OAAQ,KAAK,OAAO,EAEzE,GAAIA,IAAU,KAAK,aACjB,OAGF,MAAMmO,EAAY,KAAK,aACvB,KAAK,aAAenO,EACpB,KAAK,yBAA2BA,EAAM,MACtC,KAAK,oBAAsB,KAAK,cAE5B,KAAK,iBACPmO,GAAa,MAAgBA,EAAU,eAAe,IAAI,EAC1DnO,EAAM,YAAY,IAAI,EAEzB,CAED,cAAc4I,EAAQ,CACpB,MAAMqF,EAAgB,CAAA,EAElBrF,EAAO,OAAS,UAClBqF,EAAc,UAAY,CAACrF,EAAO,OACzBA,EAAO,OAAS,SAAW,CAAC3E,EAAiB2E,EAAO,KAAK,IAClEqF,EAAc,QAAU,IAG1B,KAAK,aAAaA,CAAa,EAE3B,KAAK,gBACP,KAAK,aAAY,CAEpB,CAED,OAAOA,EAAe,CACpBtH,EAAc,MAAM,IAAM,CAExB,GAAIsH,EAAc,UAAW,CAC3B,IAAInE,EAAuBS,EAAeL,EAAuBH,GAEhED,GAAyBS,EAAgB,KAAK,SAAS,YAAc,MAAgBT,EAAsB,KAAKS,EAAe,KAAK,cAAc,IAAI,GACtJL,GAAyBH,EAAiB,KAAK,SAAS,YAAc,MAAgBG,EAAsB,KAAKH,EAAgB,KAAK,cAAc,KAAM,IAAI,CACvK,SAAiBkE,EAAc,QAAS,CAChC,IAAIvD,EAAuBP,EAAgBW,EAAwBH,GAElED,GAAyBP,EAAiB,KAAK,SAAS,UAAY,MAAgBO,EAAsB,KAAKP,EAAgB,KAAK,cAAc,KAAK,GACvJW,GAA0BH,EAAiB,KAAK,SAAS,YAAc,MAAgBG,EAAuB,KAAKH,EAAgB,OAAW,KAAK,cAAc,KAAK,CACxK,CAGGsD,EAAc,WAChB,KAAK,UAAU,QAAQ,CAAC,CACtB,SAAArP,CACV,IAAc,CACJA,EAAS,KAAK,aAAa,CACrC,CAAS,EAICqP,EAAc,OAChB,KAAK,OAAO,cAAe,EAAC,OAAO,CACjC,MAAO,KAAK,aACZ,KAAM,wBAChB,CAAS,CAET,CAAK,CACF,CAEH,CAEA,SAASqC,GAAkBtQ,EAAOe,EAAS,CACzC,OAAOA,EAAQ,UAAY,IAAS,CAACf,EAAM,MAAM,eAAiB,EAAEA,EAAM,MAAM,SAAW,SAAWe,EAAQ,eAAiB,GACjI,CAEA,SAASgN,GAAmB/N,EAAOe,EAAS,CAC1C,OAAOuP,GAAkBtQ,EAAOe,CAAO,GAAKf,EAAM,MAAM,cAAgB,GAAKgO,EAAchO,EAAOe,EAASA,EAAQ,cAAc,CACnI,CAEA,SAASiN,EAAchO,EAAOe,EAASwP,EAAO,CAC5C,GAAIxP,EAAQ,UAAY,GAAO,CAC7B,MAAM3B,EAAQ,OAAOmR,GAAU,WAAaA,EAAMvQ,CAAK,EAAIuQ,EAC3D,OAAOnR,IAAU,UAAYA,IAAU,IAAS0Q,EAAQ9P,EAAOe,CAAO,CACvE,CAED,MAAO,EACT,CAEA,SAASsN,GAAsBrO,EAAOmO,EAAWpN,EAASmN,EAAa,CACrE,OAAOnN,EAAQ,UAAY,KAAUf,IAAUmO,GAAaD,EAAY,UAAY,MAAW,CAACnN,EAAQ,UAAYf,EAAM,MAAM,SAAW,UAAY8P,EAAQ9P,EAAOe,CAAO,CAC/K,CAEA,SAAS+O,EAAQ9P,EAAOe,EAAS,CAC/B,OAAOf,EAAM,cAAce,EAAQ,SAAS,CAC9C,CAIA,SAASwN,GAAsCnH,EAAUoJ,EAAkBzP,EAAS,CAOlF,OAAIA,EAAQ,iBACH,GAKLA,EAAQ,kBAAoB,OAIvByP,EAAiB,kBAKrB,CAAAvO,EAAoBmF,EAAS,iBAAkB,EAAEoJ,CAAgB,CAMxE;;;;;;;;GCrjBA,IAAIC,EAAQC,EACZ,SAASC,GAAGrJ,EAAGsJ,EAAG,CAChB,OAAQtJ,IAAMsJ,IAAYtJ,IAAN,GAAW,EAAIA,IAAM,EAAIsJ,IAAQtJ,IAAMA,GAAKsJ,IAAMA,CACxE,CACA,IAAIC,GAA0B,OAAO,OAAO,IAA7B,WAAkC,OAAO,GAAKF,GAC3DG,GAAWL,EAAM,SACjBM,GAAYN,EAAM,UAClBO,GAAkBP,EAAM,gBACxBQ,GAAgBR,EAAM,cACxB,SAASS,GAAuBC,EAAWC,EAAa,CACtD,IAAIhS,EAAQgS,EAAa,EACvBC,EAAYP,GAAS,CAAE,KAAM,CAAE,MAAO1R,EAAO,YAAagS,CAAW,EAAI,EACzEE,EAAOD,EAAU,CAAC,EAAE,KACpBE,EAAcF,EAAU,CAAC,EAC3B,OAAAL,GACE,UAAY,CACVM,EAAK,MAAQlS,EACbkS,EAAK,YAAcF,EACnBI,EAAuBF,CAAI,GAAKC,EAAY,CAAE,KAAMD,CAAI,CAAE,CAC3D,EACD,CAACH,EAAW/R,EAAOgS,CAAW,CAClC,EACEL,GACE,UAAY,CACV,OAAAS,EAAuBF,CAAI,GAAKC,EAAY,CAAE,KAAMD,CAAI,CAAE,EACnDH,EAAU,UAAY,CAC3BK,EAAuBF,CAAI,GAAKC,EAAY,CAAE,KAAMD,CAAI,CAAE,CAClE,CAAO,CACF,EACD,CAACH,CAAS,CACd,EACEF,GAAc7R,CAAK,EACZA,CACT,CACA,SAASoS,EAAuBF,EAAM,CACpC,IAAIG,EAAoBH,EAAK,YAC7BA,EAAOA,EAAK,MACZ,GAAI,CACF,IAAII,EAAYD,IAChB,MAAO,CAACZ,GAASS,EAAMI,CAAS,CACjC,MAAe,CACd,MAAO,EACR,CACH,CACA,SAASC,GAAuBR,EAAWC,EAAa,CACtD,OAAOA,EAAW,CACpB,CACA,IAAIQ,GACc,OAAO,OAAvB,KACgB,OAAO,OAAO,SAA9B,KACgB,OAAO,OAAO,SAAS,cAAvC,IACID,GACAT,GACsBW,GAAA,qBACfpB,EAAM,uBAAjB,OAAwCA,EAAM,qBAAuBmB,GC9DrEE,GAAA,QAAiBpB,qBCAnB,MAAMqB,GAAuBJ,GAAsB,qBCA7CK,GAA8BC,EAAAA,cAAoB,MAAS,EAC3DC,GAAyCD,EAAAA,cAAoB,EAAK,EAQxE,SAASE,GAAsBlK,EAASmK,EAAgB,CACtD,OAAInK,IAIAmK,GAAkB,OAAO,OAAW,KACjC,OAAO,0BACV,OAAO,wBAA0BJ,IAG5B,OAAO,yBAGTA,GACT,CAEA,MAAMK,GAAiB,CAAC,CACtB,QAAApK,CACF,EAAI,KAAO,CACT,MAAMqK,EAAcC,EAAAA,WAAiBJ,GAAsBlK,EAASsK,aAAiBL,EAAyB,CAAC,CAAC,EAEhH,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,wDAAwD,EAG1E,OAAOA,CACT,EACME,GAAsB,CAAC,CAC3B,OAAArJ,EACA,SAAAsJ,EACA,QAAAxK,EACA,eAAAmK,EAAiB,EACnB,IAAM,CACJM,EAAAA,UAAgB,KACdvJ,EAAO,MAAK,EACL,IAAM,CACXA,EAAO,QAAO,CACpB,GACK,CAACA,CAAM,CAAC,EAMX,MAAMwJ,EAAUR,GAAsBlK,EAASmK,CAAc,EAC7D,OAAoBQ,EAAmB,cAACV,GAA0B,SAAU,CAC1E,MAAO,CAACjK,GAAWmK,CACvB,EAAkBQ,EAAmB,cAACD,EAAQ,SAAU,CACpD,MAAOxJ,CACX,EAAKsJ,CAAQ,CAAC,CACd,EC3DMI,GAAkCZ,EAAAA,cAAoB,EAAK,EAC3Da,GAAiB,IAAMP,aAAiBM,EAAkB,EACpCA,GAAmB,SCF/C,SAASE,IAAc,CACrB,IAAIC,EAAU,GACd,MAAO,CACL,WAAY,IAAM,CAChBA,EAAU,EACX,EACD,MAAO,IAAM,CACXA,EAAU,EACX,EACD,QAAS,IACAA,CAEb,CACA,CAEA,MAAMC,GAA8ChB,EAAAA,cAAoBc,GAAW,CAAE,EAE/EG,GAA6B,IAAMX,aAAiBU,EAA8B,ECpBxF,SAASE,GAAiBC,EAAmBC,EAAQ,CAEnD,OAAI,OAAOD,GAAsB,WACxBA,EAAkB,GAAGC,CAAM,EAG7B,CAAC,CAACD,CACX,CCHA,MAAME,GAAkC,CAACvS,EAASwS,IAAuB,EACnExS,EAAQ,UAAYA,EAAQ,oBAEzBwS,EAAmB,YACtBxS,EAAQ,aAAe,IAG7B,EACMyS,GAA6BD,GAAsB,CACvDb,EAAAA,UAAgB,IAAM,CACpBa,EAAmB,WAAU,CACjC,EAAK,CAACA,CAAkB,CAAC,CACzB,EACME,GAAc,CAAC,CACnB,OAAAtS,EACA,mBAAAoS,EACA,iBAAAG,EACA,MAAA1T,CACF,IACSmB,EAAO,SAAW,CAACoS,EAAmB,QAAO,GAAM,CAACpS,EAAO,YAAcgS,GAAiBO,EAAkB,CAACvS,EAAO,MAAOnB,CAAK,CAAC,ECvBpI2T,GAAkBtG,GAAoB,CACtCA,EAAiB,UAGf,OAAOA,EAAiB,WAAc,WACxCA,EAAiB,UAAY,IAGnC,EACMuG,GAAY,CAACzS,EAAQ0S,IAAgB1S,EAAO,WAAaA,EAAO,YAAc,CAAC0S,EAC/EC,GAAgB,CAACzG,EAAkBlM,EAAQ0S,KAAiBxG,GAAoB,KAAO,OAASA,EAAiB,WAAauG,GAAUzS,EAAQ0S,CAAW,EAC3JE,GAAkB,CAAC1G,EAAkBjG,EAAUmM,IAAuBnM,EAAS,gBAAgBiG,CAAgB,EAAE,KAAK,CAAC,CAC3H,KAAAvK,CACF,IAAM,CACJuK,EAAiB,WAAa,MAAgBA,EAAiB,UAAUvK,CAAI,EAC7EuK,EAAiB,WAAa,MAAgBA,EAAiB,UAAUvK,EAAM,IAAI,CACrF,CAAC,EAAE,MAAMyC,GAAS,CAChBgO,EAAmB,WAAU,EAC7BlG,EAAiB,SAAW,MAAgBA,EAAiB,QAAQ9H,CAAK,EAC1E8H,EAAiB,WAAa,MAAgBA,EAAiB,UAAU,OAAW9H,CAAK,CAC3F,CAAC,ECVD,SAASyO,GAAajT,EAASkT,EAAU,CACvC,MAAM3B,EAAcD,GAAe,CACjC,QAAStR,EAAQ,OACrB,CAAG,EACK8S,EAAcf,KACdS,EAAqBL,KACrB7F,EAAmBiF,EAAY,oBAAoBvR,CAAO,EAEhEsM,EAAiB,mBAAqBwG,EAAc,cAAgB,aAEhExG,EAAiB,UACnBA,EAAiB,QAAU1G,EAAc,WAAW0G,EAAiB,OAAO,GAG1EA,EAAiB,YACnBA,EAAiB,UAAY1G,EAAc,WAAW0G,EAAiB,SAAS,GAG9EA,EAAiB,YACnBA,EAAiB,UAAY1G,EAAc,WAAW0G,EAAiB,SAAS,GAGlFsG,GAAgBtG,CAAgB,EAChCiG,GAAgCjG,EAAkBkG,CAAkB,EACpEC,GAA2BD,CAAkB,EAC7C,KAAM,CAACnM,CAAQ,EAAI8M,WAAe,IAAM,IAAID,EAAS3B,EAAajF,CAAgB,CAAC,EAC7ElM,EAASiG,EAAS,oBAAoBiG,CAAgB,EAgB5D,GAfA0E,GAAqBoC,EAAiB,YAACC,GAAiB,CACtD,MAAMC,EAAcR,EAAc,IAAA,GAAkBzM,EAAS,UAAUT,EAAc,WAAWyN,CAAa,CAAC,EAG9G,OAAAhN,EAAS,aAAY,EACdiN,CACR,EAAE,CAACjN,EAAUyM,CAAW,CAAC,EAAG,IAAMzM,EAAS,iBAAgB,EAAI,IAAMA,EAAS,iBAAgB,CAAE,EACjGsL,EAAAA,UAAgB,IAAM,CAGpBtL,EAAS,WAAWiG,EAAkB,CACpC,UAAW,EACjB,CAAK,CACL,EAAK,CAACA,EAAkBjG,CAAQ,CAAC,EAE3B0M,GAAczG,EAAkBlM,EAAQ0S,CAAW,EACrD,MAAME,GAAgB1G,EAAkBjG,EAAUmM,CAAkB,EAItE,GAAIE,GAAY,CACd,OAAAtS,EACA,mBAAAoS,EACA,iBAAkBlG,EAAiB,iBACnC,MAAOjG,EAAS,gBAAiB,CACrC,CAAG,EACC,MAAMjG,EAAO,MAIf,OAAQkM,EAAiB,oBAAqDlM,EAA/BiG,EAAS,YAAYjG,CAAM,CAC5E,CChEA,SAASmT,GAAS7U,EAAMC,EAAMC,EAAM,CAClC,MAAMuN,EAAgB1N,EAAeC,EAAMC,EAAMC,CAAI,EACrD,OAAOqU,GAAa9G,EAAeY,EAAa,CAClD", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]}