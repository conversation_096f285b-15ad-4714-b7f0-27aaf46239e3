import React, { useState } from 'react'
import Dashboard from '@/app/dashboard'
import DatabaseList from '@/app/databases'
import SearchConsole from '@/app/search'
import SettingsPage from '@/app/settings'
import InputPage from '@/app/input'
import AppLayout from '@/components/layout/AppLayout'

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<'dashboard' | 'databases' | 'input' | 'search' | 'settings'>('dashboard')

  const handlePageChange = (page: string) => {
    setCurrentPage(page as 'dashboard' | 'databases' | 'input' | 'search' | 'settings')
  }

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />
      case 'databases':
        return <DatabaseList />
      case 'input':
        return <InputPage />
      case 'search':
        return <SearchConsole />
      case 'settings':
        return <SettingsPage />
      default:
        return <Dashboard />
    }
  }

  return (
    <AppLayout currentPage={currentPage} onPageChange={handlePageChange}>
      {renderCurrentPage()}
    </AppLayout>
  )
}

export default App
