import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { useQuery } from '@tanstack/react-query'
import { Activity, Database, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react'
import { fetchHealthCheck } from '@/services/health'

const Dashboard: React.FC = () => {
  const { data: healthData, isLoading, refetch } = useQuery({
    queryKey: ['health'],
    queryFn: fetchHealthCheck,
    refetchInterval: 30000, // 30초마다 자동 새로고침
  })

  const healthyCount = healthData?.databases?.filter(db => db.status === 'healthy').length || 0
  const totalCount = healthData?.databases?.length || 0
  const unhealthyCount = totalCount - healthyCount

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge variant="success" className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          정상
        </Badge>
      case 'unhealthy':
        return <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          비정상
        </Badge>
      default:
        return <Badge variant="outline">알 수 없음</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">대시보드</h1>
          <p className="text-gray-600 mt-2">
            Vector Database Manager 시스템 전체 현황을 모니터링합니다.
          </p>
        </div>
        <Button 
          onClick={() => refetch()} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          새로고침
        </Button>
      </div>

      {/* 상태 카드들 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">전체 데이터베이스</CardTitle>
            <Database className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
            <p className="text-xs text-gray-500">
              총 연결된 데이터베이스 수
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">정상 상태</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{healthyCount}</div>
            <p className="text-xs text-gray-500">
              정상 작동 중인 데이터베이스
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">문제 상태</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{unhealthyCount}</div>
            <p className="text-xs text-gray-500">
              문제가 있는 데이터베이스
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">시스템 상태</CardTitle>
            <Activity className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0}%
            </div>
            <Progress value={totalCount > 0 ? (healthyCount / totalCount) * 100 : 0} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* 데이터베이스 상태 테이블 */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>데이터베이스 상태</CardTitle>
            <CardDescription>
              각 데이터베이스의 실시간 상태를 확인합니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {healthData?.databases?.map((db) => (
                <div key={db.name} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {db.type === 'vector' ? '🔍' : db.type === 'search' ? '🔎' : '📄'}
                    </div>
                    <div>
                      <div className="font-medium">{db.name}</div>
                      <div className="text-sm text-gray-500 capitalize">{db.type}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(db.status)}
                    {db.response_time && (
                      <span className="text-xs text-gray-500">
                        {db.response_time.toFixed(0)}ms
                      </span>
                    )}
                  </div>
                </div>
              ))}
              {(!healthData?.databases || healthData.databases.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  연결된 데이터베이스가 없습니다.
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>최근 활동</CardTitle>
            <CardDescription>
              시스템의 최근 활동 내역입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <Activity className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="font-medium">시스템 시작됨</div>
                  <div className="text-sm text-gray-500">
                    Vector DB Manager가 시작되었습니다.
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <Database className="h-8 w-8 text-green-600" />
                <div>
                  <div className="font-medium">데이터베이스 연결 확인</div>
                  <div className="text-sm text-gray-500">
                    모든 데이터베이스 연결 상태를 확인했습니다.
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Dashboard
