import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useQuery } from '@tanstack/react-query'
import { Database, Activity, Settings, RefreshCw } from 'lucide-react'
import { fetchHealthCheck } from '@/services/health'

const DatabaseList: React.FC = () => {
  const { data: healthData, isLoading, refetch } = useQuery({
    queryKey: ['health'],
    queryFn: fetchHealthCheck,
    refetchInterval: 30000,
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge variant="success">정상</Badge>
      case 'unhealthy':
        return <Badge variant="destructive">비정상</Badge>
      default:
        return <Badge variant="outline">알 수 없음</Badge>
    }
  }

  const getDatabaseTypeInfo = (type: string) => {
    switch (type) {
      case 'vector':
        return { icon: '🔍', label: '벡터 DB', color: 'text-purple-600' }
      case 'search':
        return { icon: '🔎', label: '검색 엔진', color: 'text-blue-600' }
      case 'document':
        return { icon: '📄', label: '문서 DB', color: 'text-green-600' }
      default:
        return { icon: '💾', label: '데이터베이스', color: 'text-gray-600' }
    }
  }

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">데이터베이스 관리</h1>
          <p className="text-gray-600 mt-2">
            연결된 모든 데이터베이스의 상태를 관리하고 모니터링합니다.
          </p>
        </div>
        <Button 
          onClick={() => refetch()} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          새로고침
        </Button>
      </div>

      {/* 데이터베이스 그리드 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {healthData?.databases?.map((db) => {
          const typeInfo = getDatabaseTypeInfo(db.type)
          return (
            <Card key={db.name} className="hover:shadow-md transition-all duration-200 border-gray-100">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{typeInfo.icon}</div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">{db.name}</CardTitle>
                      <CardDescription className={typeInfo.color}>
                        {typeInfo.label}
                      </CardDescription>
                    </div>
                  </div>
                  {getStatusBadge(db.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">URL:</span>
                    <code className="text-xs bg-gray-50 text-gray-700 px-2 py-1 rounded">
                      {db.url}
                    </code>
                  </div>
                  {db.response_time && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">응답 시간:</span>
                      <span className="font-medium text-gray-900">{db.response_time.toFixed(0)}ms</span>
                    </div>
                  )}
                  {db.error && (
                    <div className="text-sm">
                      <span className="text-gray-500">오류:</span>
                      <p className="text-red-600 text-xs mt-1 p-2 bg-red-50 rounded border-0">
                        {db.error}
                      </p>
                    </div>
                  )}
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Activity className="h-4 w-4 mr-2" />
                    상태 확인
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    설정
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {(!healthData?.databases || healthData.databases.length === 0) && (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Database className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">연결된 데이터베이스가 없습니다</h3>
              <p className="text-gray-500 mb-4">
                설정에서 데이터베이스를 추가하여 시작하세요.
              </p>
              <Button>
                <Settings className="h-4 w-4 mr-2" />
                설정으로 이동
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 상세 정보 카드 */}
      {healthData?.databases && healthData.databases.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>데이터베이스 통계</CardTitle>
              <CardDescription>
                타입별 데이터베이스 분포
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {['vector', 'search', 'document'].map(type => {
                  const count = healthData.databases?.filter(db => db.type === type).length || 0
                  const typeInfo = getDatabaseTypeInfo(type)
                  return count > 0 ? (
                    <div key={type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{typeInfo.icon}</span>
                        <span className={`font-medium ${typeInfo.color}`}>{typeInfo.label}</span>
                      </div>
                      <Badge variant="outline">{count}개</Badge>
                    </div>
                  ) : null
                })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>연결 상태</CardTitle>
              <CardDescription>
                데이터베이스별 연결 상태 요약
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">정상</span>
                  <div className="flex items-center space-x-2">
                    <Badge variant="success">
                      {healthData.databases?.filter(db => db.status === 'healthy').length || 0}개
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">비정상</span>
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive">
                      {healthData.databases?.filter(db => db.status === 'unhealthy').length || 0}개
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">평균 응답시간</span>
                  <span className="font-medium">
                    {healthData.databases?.length ? 
                      Math.round(
                        healthData.databases
                          .filter(db => db.response_time)
                          .reduce((sum, db) => sum + (db.response_time || 0), 0) / 
                        healthData.databases.filter(db => db.response_time).length
                      ) + 'ms' : 'N/A'
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default DatabaseList
