import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Send,
  Copy,
  Trash2,
  AlertCircle,
  CheckCircle2,
  Loader2,
  Brain,
  Wifi,
  WifiOff
} from 'lucide-react'
import { ollamaAPI, ollamaWebSocket, type OllamaExtractResponse } from '@/services/ollama'

interface TextInputData {
  text: string
  model: string
  extract_summary: boolean
}



interface ConnectionStatus {
  ollama_connected: boolean
  websocket_connected: boolean
  last_check: string
}

const InputPage: React.FC = () => {
  const [formData, setFormData] = useState<TextInputData>({
    text: '',
    model: 'gemma3:1b',
    extract_summary: true
  })

  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<OllamaExtractResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    ollama_connected: false,
    websocket_connected: false,
    last_check: ''
  })
  const [availableModels, setAvailableModels] = useState<string[]>([])
  const [queueStatus, setQueueStatus] = useState<any>(null)

  // 컴포넌트 마운트 시 초기화
  useEffect(() => {
    initializeConnection()

    // 컴포넌트 언마운트 시 WebSocket 연결 해제
    return () => {
      ollamaWebSocket.disconnect()
    }
  }, [])

  const initializeConnection = async () => {
    try {
      // Ollama 헬스 체크
      const health = await ollamaAPI.healthCheck()
      setConnectionStatus(prev => ({
        ...prev,
        ollama_connected: health.data.overall_healthy,
        last_check: new Date().toISOString()
      }))

      // 사용 가능한 모델 목록 조회
      const models = await ollamaAPI.listModels()
      setAvailableModels(models.data.models)

      // 큐 상태 조회
      const queue = await ollamaAPI.getQueueStatus()
      setQueueStatus(queue.data)

      // WebSocket 연결
      ollamaWebSocket.on('connected', () => {
        setConnectionStatus(prev => ({ ...prev, websocket_connected: true }))
      })

      ollamaWebSocket.on('disconnected', () => {
        setConnectionStatus(prev => ({ ...prev, websocket_connected: false }))
      })

      ollamaWebSocket.on('ollama_update', (data: any) => {
        if (data.data?.queue) {
          setQueueStatus(data.data.queue)
        }
      })

      await ollamaWebSocket.connect()
    } catch (error) {
      console.error('Failed to initialize connection:', error)
      setError('Ollama 서비스 연결에 실패했습니다.')
    }
  }

  const handleTextChange = (value: string) => {
    setFormData(prev => ({ ...prev, text: value }))
    setError(null)
  }

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      if (text) {
        handleTextChange(text)
      }
    } catch (err) {
      setError('클립보드 접근 권한이 필요합니다.')
    }
  }

  const handleClear = () => {
    setFormData({
      text: '',
      model: 'gemma3:1b',
      extract_summary: true
    })
    setResult(null)
    setError(null)
  }

  const handleSubmit = async () => {
    if (!formData.text.trim()) {
      setError('텍스트를 입력해주세요.')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Ollama API를 통해 메타데이터 추출
      const result = await ollamaAPI.extractMetadata({
        content: formData.text,
        model: formData.model,
        extract_summary: formData.extract_summary
      })

      if (result.success) {
        setResult(result)
        setFormData(prev => ({ ...prev, text: '' })) // 성공 시 텍스트만 초기화
      } else {
        setError(result.message || '메타데이터 추출에 실패했습니다.')
      }
    } catch (err) {
      setError('API 요청 중 오류가 발생했습니다.')
      console.error('Submit error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const getConnectionStatusIcon = () => {
    if (connectionStatus.ollama_connected && connectionStatus.websocket_connected) {
      return <Wifi className="h-4 w-4 text-green-600" />
    } else if (connectionStatus.ollama_connected) {
      return <Wifi className="h-4 w-4 text-yellow-600" />
    } else {
      return <WifiOff className="h-4 w-4 text-red-600" />
    }
  }

  const getConnectionStatusText = () => {
    if (connectionStatus.ollama_connected && connectionStatus.websocket_connected) {
      return '연결됨'
    } else if (connectionStatus.ollama_connected) {
      return 'HTTP만 연결됨'
    } else {
      return '연결 안됨'
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">텍스트 메타데이터 추출</h1>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getConnectionStatusIcon()}
            <span className="text-sm text-gray-600">{getConnectionStatusText()}</span>
          </div>
          <Badge variant="outline" className="text-sm">
            Ollama 메타데이터 추출
          </Badge>
        </div>
      </div>

      {/* 상태 정보 */}
      {queueStatus && (
        <Card className="p-4 bg-blue-50">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-blue-600" />
              <span className="font-medium">큐 상태:</span>
            </div>
            <span>대기: {queueStatus.pending}</span>
            <span>처리중: {queueStatus.processing}</span>
            <span>완료: {queueStatus.completed}</span>
            {queueStatus.failed > 0 && <span className="text-red-600">실패: {queueStatus.failed}</span>}
          </div>
        </Card>
      )}

      {/* 입력 폼 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              텍스트 내용 *
            </label>
            <div className="relative">
              <textarea
                value={formData.text}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleTextChange(e.target.value)}
                placeholder="메타데이터를 추출할 텍스트를 여기에 입력하거나 붙여넣으세요..."
                className="w-full min-h-[200px] resize-y px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="absolute top-2 right-2 flex gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handlePaste}
                  className="h-8 w-8 p-0"
                  title="클립보드에서 붙여넣기"
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClear}
                  className="h-8 w-8 p-0"
                  title="모두 지우기"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {formData.text && (
              <p className="text-xs text-gray-500 mt-1">
                {formData.text.split('\n').length}줄, {formData.text.length}자
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                사용할 모델
              </label>
              <select
                value={formData.model}
                onChange={(e) => setFormData(prev => ({ ...prev, model: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {availableModels.length > 0 ? (
                  availableModels.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))
                ) : (
                  <option value="gemma3:1b">gemma3:1b (기본값)</option>
                )}
              </select>
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                요약 포함
              </label>
              <Switch
                checked={formData.extract_summary}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, extract_summary: checked }))}
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Button
              onClick={handleSubmit}
              disabled={isLoading || !formData.text.trim()}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
              {isLoading ? '처리 중...' : '메타데이터 추출'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
            >
              초기화
            </Button>
          </div>
        </div>
      </Card>

      {/* 결과 표시 */}
      {result && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Brain className="h-5 w-5" />
            추출된 메타데이터
          </h2>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600">처리 상태</label>
                <div className="flex items-center gap-2">
                  <Badge variant={result.success ? "default" : "destructive"}>
                    {result.success ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                    {result.success ? '성공' : '실패'}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">처리 시간</label>
                <p className="text-sm text-gray-900">{new Date(result.timestamp).toLocaleString()}</p>
              </div>
            </div>

            {result.success && result.data && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-600">제목</label>
                  <p className="text-lg font-medium text-gray-900">{result.data.title}</p>
                </div>

                {result.data.category && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600">카테고리</label>
                    <Badge variant="outline">{result.data.category}</Badge>
                  </div>
                )}

                {result.data.keywords && result.data.keywords.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600">키워드</label>
                    <div className="flex flex-wrap gap-2">
                      {result.data.keywords.map((keyword, index) => (
                        <Badge key={index} variant="secondary">{keyword}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                {result.data.summary && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600">요약</label>
                    <p className="text-sm text-gray-700 bg-blue-50 rounded-md p-3">{result.data.summary}</p>
                  </div>
                )}

                {result.data.metadata && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600">추가 메타데이터</label>
                    <pre className="text-xs text-gray-600 bg-gray-50 rounded-md p-3 overflow-auto">
                      {JSON.stringify(result.data.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </>
            )}

            {!result.success && result.message && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                  <p className="text-sm text-red-800">{result.message}</p>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 문서 생성 기능은 향후 구현 예정 */}
      {/*
      <Card className="p-6 bg-gray-50">
        <h3 className="text-lg font-medium text-gray-900 mb-2">문서 생성 (향후 구현)</h3>
        <p className="text-sm text-gray-600">
          추출된 메타데이터를 바탕으로 Notion 문서를 생성하는 기능은 향후 구현될 예정입니다.
        </p>
      </Card>
      */}
    </div>
  )
}

export default InputPage
