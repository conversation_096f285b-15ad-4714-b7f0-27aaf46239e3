import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Send, 
  Copy, 
  Trash2, 
  FileText, 
  Save,
  AlertCircle,
  CheckCircle2,
  Loader2
} from 'lucide-react'

interface TextInputData {
  text: string
  title?: string
  category?: string
  keywords?: string[]
  auto_process: boolean
  convert_to_markdown: boolean
  source_description?: string
}

interface ProcessingResult {
  id: string
  title: string
  category: string
  keywords: string[]
  content: string
  summary: string
  metadata: {
    source: string
    created_at: string
    processing_status: 'pending' | 'processing' | 'completed' | 'failed'
  }
  text_processing_info: {
    detected_type: string
    was_converted: boolean
    source_description: string
    basic_metadata: {
      estimated_title: string
      has_code_blocks: boolean
      has_links: boolean
      has_images: boolean
      line_count: number
      word_count: number
    }
  }
}

const InputPage: React.FC = () => {
  const [formData, setFormData] = useState<TextInputData>({
    text: '',
    title: '',
    category: '',
    keywords: [],
    auto_process: true,
    convert_to_markdown: true,
    source_description: 'LLM 응답'
  })
  
  const [keywordInput, setKeywordInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<ProcessingResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleTextChange = (value: string) => {
    setFormData(prev => ({ ...prev, text: value }))
    setError(null)
  }

  const handleKeywordAdd = () => {
    if (keywordInput.trim() && !formData.keywords?.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...(prev.keywords || []), keywordInput.trim()]
      }))
      setKeywordInput('')
    }
  }

  const handleKeywordRemove = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords?.filter(k => k !== keyword) || []
    }))
  }

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      if (text) {
        handleTextChange(text)
      }
    } catch (err) {
      setError('클립보드 접근 권한이 필요합니다.')
    }
  }

  const handleClear = () => {
    setFormData({
      text: '',
      title: '',
      category: '',
      keywords: [],
      auto_process: true,
      convert_to_markdown: true,
      source_description: 'LLM 응답'
    })
    setKeywordInput('')
    setResult(null)
    setError(null)
  }

  const handleSubmit = async () => {
    if (!formData.text.trim()) {
      setError('텍스트를 입력해주세요.')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/v1/notion/create-from-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: formData.text,
          title: formData.title || undefined,
          category: formData.category || undefined,
          keywords: formData.keywords?.length ? formData.keywords : undefined,
          auto_process: formData.auto_process,
          convert_to_markdown: formData.convert_to_markdown,
          source_description: formData.source_description || undefined
        })
      })

      const data = await response.json()

      if (data.success) {
        setResult(data.data)
        setFormData(prev => ({ ...prev, text: '' })) // 성공 시 텍스트만 초기화
      } else {
        setError(data.error || '문서 생성에 실패했습니다.')
      }
    } catch (err) {
      setError('API 요청 중 오류가 발생했습니다.')
      console.error('Submit error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'processing': return 'bg-yellow-100 text-yellow-800'
      case 'pending': return 'bg-blue-100 text-blue-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="h-4 w-4" />
      case 'processing': return <Loader2 className="h-4 w-4 animate-spin" />
      case 'failed': return <AlertCircle className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">텍스트 입력</h1>
        <Badge variant="outline" className="text-sm">
          LLM 응답 처리
        </Badge>
      </div>

      {/* 입력 폼 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              텍스트 내용 *
            </label>
            <div className="relative">
              <Textarea
                value={formData.text}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleTextChange(e.target.value)}
                placeholder="LLM 응답이나 다른 텍스트를 여기에 붙여넣으세요..."
                className="min-h-[200px] resize-y"
              />
              <div className="absolute top-2 right-2 flex gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handlePaste}
                  className="h-8 w-8 p-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClear}
                  className="h-8 w-8 p-0"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {formData.text && (
              <p className="text-xs text-gray-500 mt-1">
                {formData.text.split('\n').length}줄, {formData.text.length}자
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                제목 (선택사항)
              </label>
              <Input
                value={formData.title}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="문서 제목 (미입력시 자동 추출)"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                카테고리 (선택사항)
              </label>
              <Input
                value={formData.category}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                placeholder="카테고리 (미입력시 자동 분류)"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              키워드 (선택사항)
            </label>
            <div className="flex gap-2 mb-2">
              <Input
                value={keywordInput}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setKeywordInput(e.target.value)}
                placeholder="키워드 입력"
                onKeyPress={(e) => e.key === 'Enter' && handleKeywordAdd()}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleKeywordAdd}
                disabled={!keywordInput.trim()}
              >
                추가
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.keywords?.map((keyword, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => handleKeywordRemove(keyword)}
                >
                  {keyword} ×
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              출처 설명
            </label>
            <Input
              value={formData.source_description}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, source_description: e.target.value }))}
              placeholder="예: ChatGPT 응답, Claude 응답, 개인 메모"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                자동 메타데이터 추출
              </label>
              <Switch
                checked={formData.auto_process}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, auto_process: checked }))}
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                마크다운 변환
              </label>
              <Switch
                checked={formData.convert_to_markdown}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, convert_to_markdown: checked }))}
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Button
              onClick={handleSubmit}
              disabled={isLoading || !formData.text.trim()}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
              {isLoading ? '처리 중...' : '문서 생성'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
            >
              초기화
            </Button>
          </div>
        </div>
      </Card>

      {/* 결과 표시 */}
      {result && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Save className="h-5 w-5" />
            생성된 문서
          </h2>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600">문서 ID</label>
                <p className="text-sm text-gray-900 font-mono">{result.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">처리 상태</label>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(result.metadata.processing_status)}>
                    {getStatusIcon(result.metadata.processing_status)}
                    {result.metadata.processing_status}
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-600">제목</label>
              <p className="text-lg font-medium text-gray-900">{result.title}</p>
            </div>

            {result.category && (
              <div>
                <label className="block text-sm font-medium text-gray-600">카테고리</label>
                <Badge variant="outline">{result.category}</Badge>
              </div>
            )}

            {result.keywords.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-600">키워드</label>
                <div className="flex flex-wrap gap-2">
                  {result.keywords.map((keyword, index) => (
                    <Badge key={index} variant="secondary">{keyword}</Badge>
                  ))}
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-600">텍스트 처리 정보</label>
              <div className="bg-gray-50 rounded-md p-3 text-sm">
                <div className="grid grid-cols-2 gap-2">
                  <span>감지된 타입: <strong>{result.text_processing_info.detected_type}</strong></span>
                  <span>변환됨: <strong>{result.text_processing_info.was_converted ? 'Yes' : 'No'}</strong></span>
                  <span>줄 수: <strong>{result.text_processing_info.basic_metadata.line_count}</strong></span>
                  <span>단어 수: <strong>{result.text_processing_info.basic_metadata.word_count}</strong></span>
                </div>
              </div>
            </div>

            {result.summary && (
              <div>
                <label className="block text-sm font-medium text-gray-600">요약</label>
                <p className="text-sm text-gray-700 bg-blue-50 rounded-md p-3">{result.summary}</p>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}

export default InputPage
