import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, Database, Clock, Filter } from 'lucide-react'

const SearchConsole: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [vectorInput, setVectorInput] = useState('')

  const handleTextSearch = async () => {
    if (!searchQuery.trim()) return
    
    setIsSearching(true)
    try {
      // TODO: API 호출 구현
      setTimeout(() => {
        setSearchResults([
          {
            id: 1,
            database: 'Elasticsearch',
            content: '검색 결과 예시 내용입니다.',
            score: 0.95,
            metadata: { type: 'document' }
          },
          {
            id: 2,
            database: 'Meilisearch',
            content: '또 다른 검색 결과입니다.',
            score: 0.87,
            metadata: { type: 'article' }
          }
        ])
        setIsSearching(false)
      }, 1000)
    } catch (error) {
      setIsSearching(false)
    }
  }

  const handleVectorSearch = async () => {
    if (!vectorInput.trim()) return
    
    setIsSearching(true)
    try {
      // TODO: 벡터 검색 API 호출 구현
      setTimeout(() => {
        setSearchResults([
          {
            id: 1,
            database: 'Weaviate',
            content: '벡터 유사도 검색 결과입니다.',
            score: 0.92,
            similarity: 0.92,
            metadata: { type: 'vector' }
          },
          {
            id: 2,
            database: 'Qdrant',
            content: '유사한 벡터 데이터를 찾았습니다.',
            score: 0.88,
            similarity: 0.88,
            metadata: { type: 'embedding' }
          }
        ])
        setIsSearching(false)
      }, 1000)
    } catch (error) {
      setIsSearching(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">검색 콘솔</h1>
        <p className="text-gray-600 mt-2">
          통합 검색 및 벡터 유사도 검색을 수행할 수 있습니다.
        </p>
      </div>

      {/* 검색 탭 */}
      <Tabs defaultValue="text" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="text" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            텍스트 검색
          </TabsTrigger>
          <TabsTrigger value="vector" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            벡터 검색
          </TabsTrigger>
        </TabsList>

        {/* 텍스트 검색 */}
        <TabsContent value="text" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                텍스트 검색
              </CardTitle>
              <CardDescription>
                연결된 모든 검색 엔진에서 텍스트를 검색합니다.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="검색할 텍스트를 입력하세요..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleTextSearch()}
                  className="flex-1"
                />
                <Button 
                  onClick={handleTextSearch}
                  disabled={isSearching || !searchQuery.trim()}
                >
                  {isSearching ? '검색 중...' : '검색'}
                </Button>
              </div>
              
              <div className="flex gap-2 text-sm text-gray-500">
                <Filter className="h-4 w-4" />
                <span>검색 옵션: 전체 데이터베이스, 결과 수 제한 10개</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 벡터 검색 */}
        <TabsContent value="vector" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                벡터 유사도 검색
              </CardTitle>
              <CardDescription>
                벡터 데이터베이스에서 유사한 벡터를 검색합니다.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">벡터 입력 (쉼표로 구분)</label>
                <Input
                  placeholder="예: 0.1, 0.2, 0.3, 0.4, 0.5..."
                  value={vectorInput}
                  onChange={(e) => setVectorInput(e.target.value)}
                  className="font-mono text-sm"
                />
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={handleVectorSearch}
                  disabled={isSearching || !vectorInput.trim()}
                  className="flex-1"
                >
                  {isSearching ? '검색 중...' : '벡터 검색'}
                </Button>
              </div>
              
              <div className="flex gap-2 text-sm text-gray-500">
                <Filter className="h-4 w-4" />
                <span>유사도 임계값: 0.8, 최대 결과: 10개</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 검색 결과 */}
      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>검색 결과</span>
              <Badge variant="outline">{searchResults.length}개 결과</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.map((result) => (
                <div key={result.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{result.database}</Badge>
                      {result.metadata.type && (
                        <Badge variant="secondary">{result.metadata.type}</Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      {result.similarity && (
                        <span>유사도: {(result.similarity * 100).toFixed(1)}%</span>
                      )}
                      <span>점수: {result.score.toFixed(2)}</span>
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <p>{result.content}</p>
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>방금 전</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 검색 기록 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>최근 검색</CardTitle>
            <CardDescription>
              최근에 수행한 검색 쿼리들입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 hover:bg-muted rounded">
                <span className="text-sm">machine learning</span>
                <span className="text-xs text-gray-500">5분 전</span>
              </div>
              <div className="flex items-center justify-between p-2 hover:bg-muted rounded">
                <span className="text-sm">vector database</span>
                <span className="text-xs text-gray-500">1시간 전</span>
              </div>
              <div className="flex items-center justify-between p-2 hover:bg-muted rounded">
                <span className="text-sm">neural networks</span>
                <span className="text-xs text-gray-500">2시간 전</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>검색 통계</CardTitle>
            <CardDescription>
              오늘의 검색 활동 요약입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">총 검색 수</span>
                <span className="font-medium">127</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">텍스트 검색</span>
                <span className="font-medium">89</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">벡터 검색</span>
                <span className="font-medium">38</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">평균 응답시간</span>
                <span className="font-medium">245ms</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SearchConsole
