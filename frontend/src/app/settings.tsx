import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Settings, Database, Bell, Save, TestTube } from 'lucide-react'

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    api: {
      baseUrl: 'http://localhost:7200',
      timeout: 30,
      retries: 3
    },
    databases: {
      weaviate: { url: 'http://localhost:7210', enabled: true },
      qdrant: { url: 'http://localhost:7211', enabled: true },
      chroma: { url: 'http://localhost:7212', enabled: true },
      elasticsearch: { url: 'http://localhost:7213', enabled: true },
      meilisearch: { url: 'http://localhost:7214', enabled: true },
      mongodb: { url: 'mongodb://localhost:7215', enabled: true }
    },
    notifications: {
      healthCheck: true,
      errors: true,
      performance: false
    }
  })

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }))
  }

  const handleDatabaseToggle = (dbName: string, enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      databases: {
        ...prev.databases,
        [dbName]: {
          ...prev.databases[dbName as keyof typeof prev.databases],
          enabled
        }
      }
    }))
  }

  const handleSaveSettings = () => {
    // TODO: API 호출로 설정 저장
    console.log('Saving settings:', settings)
  }

  const testConnection = async (dbName: string) => {
    // TODO: 연결 테스트 API 호출
    console.log('Testing connection for:', dbName)
  }

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">설정</h1>
          <p className="text-gray-600 mt-2">
            시스템 설정 및 데이터베이스 연결을 관리합니다.
          </p>
        </div>
        <Button onClick={handleSaveSettings} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          설정 저장
        </Button>
      </div>

      {/* 설정 탭 */}
      <Tabs defaultValue="databases" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="databases" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            데이터베이스
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            API 설정
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            알림
          </TabsTrigger>
        </TabsList>

        {/* 데이터베이스 설정 */}
        <TabsContent value="databases" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>벡터 데이터베이스</CardTitle>
                <CardDescription>
                  벡터 검색을 지원하는 데이터베이스들의 연결 설정입니다.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {['weaviate', 'qdrant', 'chroma'].map((dbName) => (
                  <div key={dbName} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">🔍</div>
                      <div>
                        <div className="font-medium capitalize">{dbName}</div>
                        <div className="text-sm text-gray-500">
                          {settings.databases[dbName as keyof typeof settings.databases].url}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={settings.databases[dbName as keyof typeof settings.databases].enabled}
                        onCheckedChange={(checked) => handleDatabaseToggle(dbName, checked)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testConnection(dbName)}
                      >
                        <TestTube className="h-4 w-4 mr-2" />
                        테스트
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>검색 엔진</CardTitle>
                <CardDescription>
                  텍스트 검색을 지원하는 검색 엔진들의 연결 설정입니다.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {['elasticsearch', 'meilisearch'].map((dbName) => (
                  <div key={dbName} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">🔎</div>
                      <div>
                        <div className="font-medium capitalize">{dbName}</div>
                        <div className="text-sm text-gray-500">
                          {settings.databases[dbName as keyof typeof settings.databases].url}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={settings.databases[dbName as keyof typeof settings.databases].enabled}
                        onCheckedChange={(checked) => handleDatabaseToggle(dbName, checked)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testConnection(dbName)}
                      >
                        <TestTube className="h-4 w-4 mr-2" />
                        테스트
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>문서 데이터베이스</CardTitle>
                <CardDescription>
                  문서 저장소로 사용되는 데이터베이스들의 연결 설정입니다.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {['mongodb'].map((dbName) => (
                  <div key={dbName} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">📄</div>
                      <div>
                        <div className="font-medium capitalize">{dbName}</div>
                        <div className="text-sm text-gray-500">
                          {settings.databases[dbName as keyof typeof settings.databases].url}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={settings.databases[dbName as keyof typeof settings.databases].enabled}
                        onCheckedChange={(checked) => handleDatabaseToggle(dbName, checked)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testConnection(dbName)}
                      >
                        <TestTube className="h-4 w-4 mr-2" />
                        테스트
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* API 설정 */}
        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API 연결 설정</CardTitle>
              <CardDescription>
                백엔드 API 서버와의 연결 설정을 관리합니다.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Base URL</label>
                <Input
                  value={settings.api.baseUrl}
                  onChange={(e) => handleSettingChange('api', 'baseUrl', e.target.value)}
                  placeholder="http://localhost:7200"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">타임아웃 (초)</label>
                  <Input
                    type="number"
                    value={settings.api.timeout}
                    onChange={(e) => handleSettingChange('api', 'timeout', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">재시도 횟수</label>
                  <Input
                    type="number"
                    value={settings.api.retries}
                    onChange={(e) => handleSettingChange('api', 'retries', parseInt(e.target.value))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>보안 설정</CardTitle>
              <CardDescription>
                API 인증 및 보안 관련 설정입니다.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">API 키 인증</div>
                  <div className="text-sm text-gray-500">
                    API 요청에 인증 키를 포함합니다.
                  </div>
                </div>
                <Switch />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">HTTPS 강제</div>
                  <div className="text-sm text-gray-500">
                    모든 API 요청을 HTTPS로 강제합니다.
                  </div>
                </div>
                <Switch />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 알림 설정 */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>알림 설정</CardTitle>
              <CardDescription>
                시스템 이벤트에 대한 알림을 설정합니다.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">헬스체크 알림</div>
                  <div className="text-sm text-gray-500">
                    데이터베이스 연결 상태 변경 시 알림을 받습니다.
                  </div>
                </div>
                <Switch
                  checked={settings.notifications.healthCheck}
                  onCheckedChange={(checked) => handleSettingChange('notifications', 'healthCheck', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">오류 알림</div>
                  <div className="text-sm text-gray-500">
                    시스템 오류 발생 시 즉시 알림을 받습니다.
                  </div>
                </div>
                <Switch
                  checked={settings.notifications.errors}
                  onCheckedChange={(checked) => handleSettingChange('notifications', 'errors', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">성능 알림</div>
                  <div className="text-sm text-gray-500">
                    응답 시간 등 성능 이슈 발생 시 알림을 받습니다.
                  </div>
                </div>
                <Switch
                  checked={settings.notifications.performance}
                  onCheckedChange={(checked) => handleSettingChange('notifications', 'performance', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SettingsPage
