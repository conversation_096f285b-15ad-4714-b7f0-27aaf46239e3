import React from 'react'
import { <PERSON><PERSON>, <PERSON>, Search, User } from 'lucide-react'

interface HeaderProps {
  onMenuClick: () => void
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white">
      <div className="flex items-center justify-between h-16 px-4">
        {/* Left side - Menu and Logo */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuClick}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(238, 242, 255)'
              e.currentTarget.style.color = 'rgb(67, 56, 202)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = 'rgb(75, 85, 99)'
            }}
            style={{ color: 'rgb(75, 85, 99)' }}
            className="lg:hidden w-10 h-10 flex items-center justify-center rounded-md transition-colors"
          >
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">VDB</span>
            </div>
            <h1 className="text-xl font-semibold text-gray-900 hidden sm:block">
              Vector DB Manager
            </h1>
          </div>
        </div>

        {/* Center - Search (optional) */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="검색..."
              className="w-full pl-10 pr-4 py-2 bg-gray-50 rounded-lg border-0 focus:bg-white focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-all duration-200"
            />
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-3">
          <button 
            className="relative w-10 h-10 flex items-center justify-center rounded-md transition-colors"
            style={{ color: 'rgb(75, 85, 99)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(238, 242, 255)'
              e.currentTarget.style.color = 'rgb(67, 56, 202)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = 'rgb(75, 85, 99)'
            }}
          >
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
              3
            </span>
          </button>
          
          <button 
            className="w-10 h-10 flex items-center justify-center rounded-md transition-colors"
            style={{ color: 'rgb(75, 85, 99)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(238, 242, 255)'
              e.currentTarget.style.color = 'rgb(67, 56, 202)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = 'rgb(75, 85, 99)'
            }}
          >
            <User className="h-5 w-5" />
          </button>

          {/* Profile dropdown would go here */}
          <div className="flex items-center space-x-2 ml-2">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full"></div>
            <span className="text-sm font-medium text-gray-700 hidden sm:block">
              Admin
            </span>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
