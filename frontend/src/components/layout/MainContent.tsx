import React from 'react'
import { cn } from '@/lib/utils'

interface MainContentProps {
  children: React.ReactNode
  sidebarCollapsed?: boolean
}

const MainContent: React.FC<MainContentProps> = ({ 
  children, 
  sidebarCollapsed = false 
}) => {
  return (
    <main className={cn(
      "fixed top-16 right-0 bottom-0 transition-all duration-300 ease-in-out bg-gray-50",
      // 사이드바 상태에 따른 left margin 조정
      sidebarCollapsed ? "lg:left-16" : "lg:left-64",
      // 모바일에서는 전체 너비
      "left-0"
    )}>
      {/* 스크롤 가능한 컨텐츠 영역 */}
      <div className="h-full overflow-y-auto">
        <div className="p-6">
          {children}
        </div>
      </div>
    </main>
  )
}

export default MainContent
