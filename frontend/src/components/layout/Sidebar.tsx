import React from 'react'
import { 
  Database, 
  Search, 
  Settings, 
  Activity, 
  X,
  ChevronLeft,
  ChevronRight,
  Edit3
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  currentPage: string
  onPageChange: (page: string) => void
  collapsed?: boolean
  onToggleCollapse?: () => void
}

const Sidebar: React.FC<SidebarProps> = ({ 
  isOpen, 
  onClose, 
  currentPage, 
  onPageChange,
  collapsed = false,
  onToggleCollapse
}) => {
  const navigation = [
    { id: 'dashboard', name: '대시보드', icon: Activity },
    { id: 'databases', name: '데이터베이스', icon: Database },
    { id: 'input', name: '입력', icon: Edit3 },
    { id: 'search', name: '검색 콘솔', icon: Search },
    { id: 'settings', name: '설정', icon: Settings },
  ]

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] bg-white transition-all duration-300 ease-in-out",
        // Mobile
        "lg:translate-x-0",
        isOpen ? "translate-x-0" : "-translate-x-full",
        // Desktop width
        collapsed ? "lg:w-16" : "lg:w-64",
        // Mobile width
        "w-64"
      )}>
        {/* Mobile close button */}
        <div className="absolute top-4 right-4 lg:hidden">
          <button 
            onClick={onClose}
            style={{ color: 'rgb(107, 114, 128)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(238, 242, 255)'
              e.currentTarget.style.color = 'rgb(67, 56, 202)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = 'rgb(107, 114, 128)'
            }}
            className="h-10 w-10 flex items-center justify-center rounded-md transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Desktop collapse toggle */}
        {onToggleCollapse && (
          <div className="absolute -right-3 top-8 hidden lg:block">
            <button
              onClick={onToggleCollapse}
              style={{ 
                color: 'rgb(107, 114, 128)',
                backgroundColor: 'white'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(238, 242, 255)'
                e.currentTarget.style.color = 'rgb(67, 56, 202)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white'
                e.currentTarget.style.color = 'rgb(107, 114, 128)'
              }}
              className="h-6 w-6 rounded-full flex items-center justify-center transition-colors"
            >
              {collapsed ? (
                <ChevronRight className="h-3 w-3" />
              ) : (
                <ChevronLeft className="h-3 w-3" />
              )}
            </button>
          </div>
        )}

        {/* Navigation */}
        <nav className="p-4">
          <div className="space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = currentPage === item.id
              
              return (
                <button
                  key={item.id}
                  onClick={() => onPageChange(item.id)}
                  style={{
                    backgroundColor: isActive ? 'rgb(238, 242, 255)' : 'transparent',
                    color: isActive ? 'rgb(67, 56, 202)' : 'rgb(75, 85, 99)'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'rgb(238, 242, 255)'
                      e.currentTarget.style.color = 'rgb(67, 56, 202)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent'
                      e.currentTarget.style.color = 'rgb(75, 85, 99)'
                    }
                  }}
                  className={cn(
                    "w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 border-0 outline-none",
                    collapsed && "lg:justify-center lg:px-2"
                  )}
                  title={collapsed ? item.name : undefined}
                >
                  <Icon 
                    className={cn(
                      "h-5 w-5 flex-shrink-0 transition-colors duration-200",
                      !collapsed && "mr-3"
                    )}
                    style={{
                      color: isActive ? 'rgb(67, 56, 202)' : 'rgb(107, 114, 128)'
                    }}
                  />
                  {!collapsed && (
                    <span className="truncate">{item.name}</span>
                  )}
                </button>
              )
            })}
          </div>
        </nav>

        {/* Footer info (when not collapsed) */}
        {!collapsed && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-indigo-50 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-indigo-700 font-medium">시스템 정상</span>
              </div>
              <div className="text-xs text-indigo-600 mt-1">
                5/6 데이터베이스 연결됨
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default Sidebar
