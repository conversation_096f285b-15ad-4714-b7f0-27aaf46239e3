import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export function formatNumber(num: number) {
  return new Intl.NumberFormat().format(num)
}

export function formatDuration(ms: number) {
  if (ms < 1000) return `${Math.round(ms)}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

export function getStatusColor(status: string) {
  switch (status) {
    case 'healthy':
      return 'text-green-600'
    case 'unhealthy':
      return 'text-red-600'
    case 'warning':
      return 'text-yellow-600'
    default:
      return 'text-gray-600'
  }
}

export function getDatabaseTypeIcon(type: string) {
  switch (type) {
    case 'vector':
      return '🔍'
    case 'search':
      return '🔎'
    case 'document':
      return '📄'
    default:
      return '💾'
  }
}
