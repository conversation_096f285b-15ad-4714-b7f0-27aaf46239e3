// Ollama API 서비스
// vector-db-manager를 통해 ollama 기능에 접근

import { apiClient } from './api'

// 타입 정의
export interface OllamaExtractRequest {
  content: string
  model?: string
  extract_summary?: boolean
}

export interface OllamaExtractResponse {
  success: boolean
  message?: string
  data: {
    title: string
    category: string
    keywords: string[]
    summary?: string
    metadata?: any
  }
  timestamp: string
}

export interface OllamaHealthResponse {
  success: boolean
  data: {
    ollama_base: boolean
    http_server: boolean
    terminal_server: boolean
    overall_healthy: boolean
    timestamp: string
  }
}

export interface OllamaModelsResponse {
  success: boolean
  data: {
    models: string[]
  }
  count: number
}

export interface OllamaQueueStatus {
  success: boolean
  data: {
    pending: number
    processing: number
    completed: number
    failed: number
    total_processed: number
    current_processing?: any
  }
}

export interface WebSocketStats {
  success: boolean
  data: {
    connected_clients: number
    ollama_connected: boolean
    server_running: boolean
    server_address: string
  }
}

// Ollama API 클래스
export class OllamaAPI {
  private baseUrl = '/api/v1/ollama'

  /**
   * Ollama 서비스 헬스 체크
   */
  async healthCheck(): Promise<OllamaHealthResponse> {
    const response = await apiClient.get(`${this.baseUrl}/health`)
    return response.data
  }

  /**
   * 사용 가능한 모델 목록 조회
   */
  async listModels(): Promise<OllamaModelsResponse> {
    const response = await apiClient.get(`${this.baseUrl}/models`)
    return response.data
  }

  /**
   * 메타데이터 추출
   */
  async extractMetadata(request: OllamaExtractRequest): Promise<OllamaExtractResponse> {
    const response = await apiClient.post(`${this.baseUrl}/extract`, request)
    return response.data
  }

  /**
   * 배치 메타데이터 추출
   */
  async extractMetadataBatch(requests: OllamaExtractRequest[]): Promise<any> {
    const response = await apiClient.post(`${this.baseUrl}/extract/batch`, requests)
    return response.data
  }

  /**
   * 텍스트 임베딩 생성
   */
  async createEmbedding(text: string, model: string = 'nomic-embed-text'): Promise<any> {
    const response = await apiClient.post(`${this.baseUrl}/embedding`, null, {
      params: { text, model }
    })
    return response.data
  }

  /**
   * 문서 재정렬
   */
  async rerankDocuments(query: string, documents: string[], model: string = 'bge-reranker-base'): Promise<any> {
    const response = await apiClient.post(`${this.baseUrl}/rerank`, documents, {
      params: { query, model }
    })
    return response.data
  }

  /**
   * 큐 상태 조회
   */
  async getQueueStatus(): Promise<OllamaQueueStatus> {
    const response = await apiClient.get(`${this.baseUrl}/queue/status`)
    return response.data
  }

  /**
   * WebSocket 서비스 통계
   */
  async getWebSocketStats(): Promise<WebSocketStats> {
    const response = await apiClient.get(`${this.baseUrl}/websocket/stats`)
    return response.data
  }

  /**
   * 연결 테스트
   */
  async testConnection(): Promise<any> {
    const response = await apiClient.post(`${this.baseUrl}/test/connection`)
    return response.data
  }

  /**
   * API 정보 조회
   */
  async getInfo(): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/`)
    return response.data
  }
}

// WebSocket 클래스
export class OllamaWebSocket {
  private ws: WebSocket | null = null
  private url: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private listeners: Map<string, Function[]> = new Map()

  constructor() {
    // WebSocket URL 구성 (현재 호스트 기준)
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    this.url = `${protocol}//${host}/api/v1/ollama/ws`
  }

  /**
   * WebSocket 연결
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url)

        this.ws.onopen = () => {
          console.log('Ollama WebSocket connected')
          this.reconnectAttempts = 0
          this.emit('connected', {})
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.emit(data.type || 'message', data)
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error)
          }
        }

        this.ws.onclose = () => {
          console.log('Ollama WebSocket disconnected')
          this.emit('disconnected', {})
          this.attemptReconnect()
        }

        this.ws.onerror = (error) => {
          console.error('Ollama WebSocket error:', error)
          this.emit('error', { error })
          reject(error)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * WebSocket 연결 해제
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 메시지 전송
   */
  send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  /**
   * 이벤트 리스너 등록
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  /**
   * 이벤트 리스너 제거
   */
  off(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 이벤트 발생
   */
  private emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }

  /**
   * 재연결 시도
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error)
        })
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('Max reconnection attempts reached')
      this.emit('max_reconnect_attempts', {})
    }
  }

  /**
   * 핑 전송
   */
  ping(): void {
    this.send({ type: 'ping' })
  }

  /**
   * 상태 요청
   */
  requestStatus(): void {
    this.send({ type: 'request_status' })
  }

  /**
   * 연결 상태 확인
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }
}

// 전역 인스턴스
export const ollamaAPI = new OllamaAPI()
export const ollamaWebSocket = new OllamaWebSocket()

export default ollamaAPI
