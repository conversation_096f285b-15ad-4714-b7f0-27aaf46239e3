import { apiClient } from './api'
import { SearchResponse, VectorSearchRequest, VectorSearchResponse, UnifiedSearchRequest } from '@/types/api'

export const searchText = async (query: string, databases?: string[], limit?: number): Promise<SearchResponse> => {
  const response = await apiClient.post('/v1/unified/search', {
    query,
    databases,
    search_type: 'text',
    limit: limit || 10
  })
  return response.data
}

export const searchVector = async (request: VectorSearchRequest): Promise<VectorSearchResponse> => {
  const response = await apiClient.post('/v1/unified/vector-search', request)
  return response.data
}

export const unifiedSearch = async (request: UnifiedSearchRequest): Promise<SearchResponse> => {
  const response = await apiClient.post('/v1/unified/search', request)
  return response.data
}

// TODO: 백엔드에 구현 필요
export const getSearchHistory = async () => {
  // const response = await apiClient.get('/v1/search/history')
  // return response.data
  return { history: [] }
}

// TODO: 백엔드에 구현 필요  
export const getSearchAnalytics = async (period: string = '24h') => {
  // const response = await apiClient.get(`/v1/search/analytics?period=${period}`)
  // return response.data
  return { analytics: {}, period } // period 사용하여 오류 해결
}
