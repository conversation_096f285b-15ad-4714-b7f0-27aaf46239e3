@tailwind base;
@tailwind components;
@tailwind utilities;

/* 브라우저 기본 스타일 리셋 */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#root {
  height: 100%;
}

/* 버튼 기본 스타일 리셋 (Tailwind 클래스보다 낮은 우선순위) */
button {
  border: none;
  outline: none;
  box-shadow: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  /* background는 !important 제거 - Tailwind가 덮어쓸 수 있도록 */
  background: transparent;
}

button:focus {
  outline: none;
  box-shadow: none;
}

/* Tailwind 유틸리티 클래스 우선권 보장 */
@layer utilities {
  .bg-indigo-50 {
    background-color: rgb(238 242 255) !important;
  }
  
  .hover\:bg-indigo-50:hover {
    background-color: rgb(238 242 255) !important;
  }
  
  .bg-transparent {
    background-color: transparent !important;
  }
}

/* Input 기본 스타일 리셋 */
input {
  border: none;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

input:focus {
  outline: none;
  box-shadow: none;
}

/* 커스텀 스크롤바 개선 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

::-webkit-scrollbar-corner {
  background: #f8fafc;
}

/* Firefox 스크롤바 */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f8fafc;
}

/* 부드러운 스크롤 */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* 커스텀 스크롤바 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* 로딩 애니메이션 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 포커스 상태 개선 */
.focus\:ring-2:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

/* 카드 호버 효과 */
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 트랜지션 */
.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-shadow {
  transition: box-shadow 0.2s ease-in-out;
}
