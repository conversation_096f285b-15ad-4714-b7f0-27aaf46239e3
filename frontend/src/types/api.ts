import { DatabaseStatus } from './database';

export interface BaseResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  timestamp: string;
}

export interface HealthData {
  status: 'healthy' | 'unhealthy' | 'degraded';
  databases: DatabaseStatus[];
  uptime: number;
  version: string;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  total: number;
  took: number;
  database_results: Record<string, DatabaseSearchResult>;
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata: Record<string, any>;
  database: string;
  highlight?: Record<string, string[]>;
}

export interface DatabaseSearchResult {
  results: SearchResult[];
  total: number;
  took: number;
  error?: string;
}

export interface VectorSearchRequest {
  vector: number[];
  limit?: number;
  threshold?: number;
  filters?: Record<string, any>;
}

export interface VectorSearchResponse {
  results: VectorSearchResult[];
  total: number;
  took: number;
}

export interface VectorSearchResult {
  id: string;
  content: string;
  score: number;
  similarity: number;
  metadata: Record<string, any>;
  database: string;
}

export interface UnifiedSearchRequest {
  query: string;
  databases?: string[];
  search_type?: 'text' | 'vector' | 'hybrid';
  limit?: number;
  filters?: Record<string, any>;
}

export interface ErrorResponse {
  error: string;
  detail?: string;
  code?: string;
}
