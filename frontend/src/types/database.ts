export interface DatabaseStatus {
  name: string;
  type: 'vector' | 'search' | 'document';
  status: 'healthy' | 'unhealthy' | 'unknown';
  url: string;
  response_time?: number;
  error?: string;
}

export interface DatabaseInfo extends DatabaseStatus {
  version?: string;
  description?: string;
  features: string[];
  metrics: {
    documents_count?: number;
    collections_count?: number;
    storage_size?: number;
    memory_usage?: number;
  };
  configuration: Record<string, any>;
}

export interface DatabaseConfig {
  url: string;
  timeout: number;
  max_connections: number;
  custom_settings: Record<string, any>;
}
