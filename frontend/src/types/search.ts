export interface SearchRequest {
  query: string;
  databases?: string[];
  search_type: 'text' | 'vector' | 'hybrid';
  limit: number;
  offset?: number;
  filters?: Record<string, any>;
}

export interface VectorSearchRequest {
  vector: number[];
  databases?: string[];
  limit: number;
  threshold?: number;
  filters?: Record<string, any>;
}

export interface SearchResult {
  id: string;
  content: string;
  score?: number;
  database: string;
  metadata?: Record<string, any>;
  vector?: number[];
}

export interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  total: number;
  query_time: number;
  message?: string;
}
