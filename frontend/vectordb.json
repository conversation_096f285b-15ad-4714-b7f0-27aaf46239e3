{"openapi": "3.1.0", "info": {"title": "Vector DB Manager", "description": "다양한 벡터 데이터베이스와 전통적인 데이터베이스를 통합 관리하는 백엔드 시스템", "version": "1.0.0"}, "paths": {"/api/v1/unified/health": {"get": {"tags": ["unified"], "summary": "Health Check All", "description": "모든 데이터베이스의 헬스체크를 수행합니다.", "operationId": "health_check_all_api_v1_unified_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DatabaseStatusResponse"}}}}}}}, "/api/v1/unified/search": {"post": {"tags": ["unified"], "summary": "Unified Search", "description": "통합 검색을 수행합니다.", "operationId": "unified_search_api_v1_unified_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnifiedSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnifiedSearchResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/unified/vector-search": {"post": {"tags": ["unified"], "summary": "Vector Search", "description": "벡터 검색을 수행합니다.", "operationId": "vector_search_api_v1_unified_vector_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VectorSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VectorSearchResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/unified/databases": {"get": {"tags": ["unified"], "summary": "List Databases", "description": "사용 가능한 모든 데이터베이스 목록을 반환합니다.", "operationId": "list_databases_api_v1_unified_databases_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}}}, "/api/v1/notion/documents": {"post": {"tags": ["notion-documents"], "summary": "Create Document", "description": "새 Notion 문서 생성", "operationId": "create_document_api_v1_notion_documents_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotionDocumentCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/upload": {"post": {"tags": ["notion-documents"], "summary": "Upload Markdown File", "description": "마크다운 파일 업로드", "operationId": "upload_markdown_file_api_v1_notion_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_markdown_file_api_v1_notion_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/create-from-text": {"post": {"tags": ["notion-documents"], "summary": "Create Document From Text", "description": "텍스트로부터 Notion 문서 생성", "operationId": "create_document_from_text_api_v1_notion_create_from_text_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotionTextCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/convert-text": {"post": {"tags": ["notion-documents"], "summary": "Convert Text", "description": "텍스트 형식 변환 (미리보기용)", "operationId": "convert_text_api_v1_notion_convert_text_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TextConvertRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/documents/{document_id}": {"get": {"tags": ["notion-documents"], "summary": "Get Document", "description": "문서 조회", "operationId": "get_document_api_v1_notion_documents__document_id__get", "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["notion-documents"], "summary": "Update Document", "description": "문서 수정", "operationId": "update_document_api_v1_notion_documents__document_id__put", "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotionDocumentUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["notion-documents"], "summary": "Delete Document", "description": "문서 삭제", "operationId": "delete_document_api_v1_notion_documents__document_id__delete", "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/search": {"post": {"tags": ["notion-documents"], "summary": "Search Documents", "description": "문서 검색", "operationId": "search_documents_api_v1_notion_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotionSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/categories": {"get": {"tags": ["notion-documents"], "summary": "Get Categories", "description": "카테고리 목록 조회", "operationId": "get_categories_api_v1_notion_categories_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}}}, "/api/v1/notion/keywords": {"get": {"tags": ["notion-documents"], "summary": "Get Keywords", "description": "키워드 목록 조회", "operationId": "get_keywords_api_v1_notion_keywords_get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/batch-process": {"post": {"tags": ["notion-documents"], "summary": "Batch Process Folder", "description": "폴더의 마크다운 파일들을 배치 처리", "operationId": "batch_process_folder_api_v1_notion_batch_process_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_batch_process_folder_api_v1_notion_batch_process_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notion/processing-status": {"get": {"tags": ["notion-documents"], "summary": "Get Processing Status", "description": "전체 문서 처리 상태 조회", "operationId": "get_processing_status_api_v1_notion_processing_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}}}, "/api/v1/ollama/health": {"get": {"tags": ["ollama"], "summary": "Health Check", "description": "Ollama 서비스 전체 헬스 체크", "operationId": "health_check_api_v1_ollama_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ollama/models": {"get": {"tags": ["ollama"], "summary": "List Models", "description": "사용 가능한 모델 목록 조회", "operationId": "list_models_api_v1_ollama_models_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ollama/extract": {"post": {"tags": ["ollama"], "summary": "Extract Metadata", "description": "메타데이터 추출", "operationId": "extract_metadata_api_v1_ollama_extract_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OllamaExtractRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ollama/extract/batch": {"post": {"tags": ["ollama"], "summary": "Extract <PERSON><PERSON><PERSON>", "description": "배치 메타데이터 추출", "operationId": "extract_metadata_batch_api_v1_ollama_extract_batch_post", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OllamaExtractRequest"}, "type": "array", "title": "Requests"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ollama/embedding": {"post": {"tags": ["ollama"], "summary": "Create Embedding", "description": "텍스트 임베딩 생성", "operationId": "create_embedding_api_v1_ollama_embedding_post", "parameters": [{"name": "text", "in": "query", "required": true, "schema": {"type": "string", "title": "Text"}}, {"name": "model", "in": "query", "required": false, "schema": {"type": "string", "default": "nomic-embed-text", "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ollama/rerank": {"post": {"tags": ["ollama"], "summary": "Rerank Documents", "description": "문서 재정렬", "operationId": "rerank_documents_api_v1_ollama_rerank_post", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "title": "Query"}}, {"name": "model", "in": "query", "required": false, "schema": {"type": "string", "default": "bge-reranker-base", "title": "Model"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Documents"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ollama/queue/status": {"get": {"tags": ["ollama"], "summary": "Get Queue Status", "description": "큐 상태 조회", "operationId": "get_queue_status_api_v1_ollama_queue_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ollama/websocket/stats": {"get": {"tags": ["ollama"], "summary": "Get Websocket Stats", "description": "WebSocket 서비스 통계", "operationId": "get_websocket_stats_api_v1_ollama_websocket_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ollama/test/connection": {"post": {"tags": ["ollama"], "summary": "Test Connection", "description": "연결 테스트", "operationId": "test_connection_api_v1_ollama_test_connection_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ollama/": {"get": {"tags": ["ollama"], "summary": "Ollama Info", "description": "Ollama 통합 API 정보", "operationId": "ollama_info_api_v1_ollama__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/": {"get": {"summary": "Root", "description": "기본 루트 엔드포인트", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "기본 헬스체크 엔드포인트", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"BaseResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "default": "2025-07-14T00:35:25.371929"}}, "type": "object", "title": "BaseResponse", "description": "기본 응답 모델"}, "Body_batch_process_folder_api_v1_notion_batch_process_post": {"properties": {"folder_path": {"type": "string", "title": "Folder Path"}, "ollama_model": {"type": "string", "title": "Ollama Model", "default": "gemma3:1b"}, "overwrite_existing": {"type": "boolean", "title": "Overwrite Existing", "default": false}, "recursive": {"type": "boolean", "title": "Recursive", "default": true}}, "type": "object", "required": ["folder_path"], "title": "Body_batch_process_folder_api_v1_notion_batch_process_post"}, "Body_upload_markdown_file_api_v1_notion_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}, "auto_process": {"type": "boolean", "title": "Auto Process", "default": true}}, "type": "object", "required": ["file"], "title": "Body_upload_markdown_file_api_v1_notion_upload_post"}, "DatabaseStatus": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"type": "string", "title": "Type"}, "status": {"type": "string", "title": "Status"}, "url": {"type": "string", "title": "Url"}, "response_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Response Time"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["name", "type", "status", "url"], "title": "DatabaseStatus", "description": "데이터베이스 상태 모델"}, "DatabaseStatusResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "default": "2025-07-14T00:35:25.371929"}, "databases": {"items": {"$ref": "#/components/schemas/DatabaseStatus"}, "type": "array", "title": "Databases", "default": []}, "healthy_count": {"type": "integer", "title": "Healthy Count", "default": 0}, "total_count": {"type": "integer", "title": "Total Count", "default": 0}}, "type": "object", "title": "DatabaseStatusResponse", "description": "데이터베이스 상태 응답 모델"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "NotionDocumentCreate": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "문서 제목 (자동 추출 가능)"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category", "description": "카테고리 (자동 추출 가능)"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "키워드 목록 (자동 추출 가능)"}, "content": {"type": "string", "title": "Content", "description": "마크다운 내용"}, "original_filename": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Original Filename", "description": "원본 파일명"}, "auto_process": {"type": "boolean", "title": "Auto Process", "description": "Ollama 자동 처리 여부", "default": true}}, "type": "object", "required": ["content"], "title": "NotionDocumentCreate", "description": "Notion 문서 생성 요청 모델"}, "NotionDocumentUpdate": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "문서 제목"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category", "description": "카테고리"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "키워드 목록"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "마크다운 내용"}, "reprocess": {"type": "boolean", "title": "Reprocess", "description": "Ollama 재처리 여부", "default": false}}, "type": "object", "title": "NotionDocumentUpdate", "description": "Notion 문서 수정 요청 모델"}, "NotionSearchRequest": {"properties": {"query": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query", "description": "검색 쿼리"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category", "description": "카테고리 필터"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "키워드 필터"}, "date_from": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date From", "description": "시작 날짜"}, "date_to": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date To", "description": "종료 날짜"}, "processing_status": {"anyOf": [{"$ref": "#/components/schemas/ProcessingStatus"}, {"type": "null"}], "description": "처리 상태 필터"}, "limit": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Limit", "description": "결과 개수", "default": 20}, "offset": {"type": "integer", "minimum": 0.0, "title": "Offset", "description": "오프셋", "default": 0}, "sort_by": {"type": "string", "title": "Sort By", "description": "정렬 기준", "default": "created_at"}, "sort_order": {"type": "string", "title": "Sort Order", "description": "정렬 순서 (asc/desc)", "default": "desc"}}, "type": "object", "title": "NotionSearchRequest", "description": "Notion 문서 검색 요청 모델"}, "NotionTextCreate": {"properties": {"text": {"type": "string", "title": "Text", "description": "입력 텍스트 (마크다운 또는 일반 텍스트)"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "문서 제목 (선택사항)"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category", "description": "카테고리 (선택사항)"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "키워드 목록 (선택사항)"}, "auto_process": {"type": "boolean", "title": "Auto Process", "description": "Ollama 자동 처리 여부", "default": true}, "convert_to_markdown": {"type": "boolean", "title": "Convert To <PERSON>down", "description": "마크다운 변환 여부", "default": true}, "source_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Description", "description": "텍스트 출처 설명"}}, "type": "object", "required": ["text"], "title": "NotionTextCreate", "description": "텍스트로 Notion 문서 생성 요청 모델"}, "OllamaExtractRequest": {"properties": {"content": {"type": "string", "title": "Content", "description": "마크다운 내용"}, "model": {"type": "string", "title": "Model", "description": "사용할 Ollama 모델", "default": "gemma3:1b"}, "extract_summary": {"type": "boolean", "title": "Extract Summary", "description": "요약도 함께 추출할지 여부", "default": true}}, "type": "object", "required": ["content"], "title": "OllamaExtractRequest", "description": "Ollama 메타데이터 추출 요청"}, "ProcessingStatus": {"type": "string", "enum": ["pending", "processing", "completed", "failed"], "title": "ProcessingStatus", "description": "문서 처리 상태"}, "SearchResult": {"properties": {"id": {"type": "string", "title": "Id"}, "content": {"type": "string", "title": "Content"}, "score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Score"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "source": {"type": "string", "title": "Source"}}, "type": "object", "required": ["id", "content", "source"], "title": "SearchResult", "description": "검색 결과 단일 항목"}, "TextConvertRequest": {"properties": {"text": {"type": "string", "title": "Text", "description": "변환할 텍스트"}, "target_format": {"type": "string", "title": "Target Format", "description": "대상 형식", "default": "markdown"}, "source_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Type", "description": "소스 타입 힌트"}, "force_conversion": {"type": "boolean", "title": "Force Conversion", "description": "강제 변환 여부", "default": false}}, "type": "object", "required": ["text"], "title": "TextConvertRequest", "description": "텍스트 변환 요청 모델"}, "UnifiedSearchRequest": {"properties": {"query": {"type": "string", "title": "Query", "description": "검색 쿼리"}, "databases": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Databases", "description": "검색할 데이터베이스 목록"}, "search_type": {"type": "string", "title": "Search Type", "description": "검색 타입 (text, vector, hybrid)", "default": "text"}, "limit": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Limit", "description": "결과 개수", "default": 10}, "filters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Filters", "description": "검색 필터"}}, "type": "object", "required": ["query"], "title": "UnifiedSearchRequest", "description": "통합 검색 요청 모델"}, "UnifiedSearchResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "default": "2025-07-14T00:35:25.371929"}, "results": {"items": {"$ref": "#/components/schemas/SearchResult"}, "type": "array", "title": "Results", "default": []}, "total": {"type": "integer", "title": "Total", "default": 0}, "query": {"type": "string", "title": "Query"}, "search_time": {"type": "number", "title": "Search Time"}, "databases_searched": {"items": {"type": "string"}, "type": "array", "title": "Databases Searched", "default": []}, "database_results": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Database Results", "default": {}}}, "type": "object", "required": ["query", "search_time"], "title": "UnifiedSearchResponse", "description": "통합 검색 응답 모델"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VectorSearchRequest": {"properties": {"vector": {"items": {"type": "number"}, "type": "array", "title": "Vector", "description": "검색할 벡터"}, "limit": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Limit", "description": "결과 개수", "default": 10}, "threshold": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "유사도 임계값"}, "filters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Filters", "description": "검색 필터"}}, "type": "object", "required": ["vector"], "title": "VectorSearchRequest", "description": "벡터 검색 요청 모델"}, "VectorSearchResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "default": "2025-07-14T00:35:25.371929"}, "results": {"items": {"$ref": "#/components/schemas/VectorSearchResult"}, "type": "array", "title": "Results", "default": []}, "total": {"type": "integer", "title": "Total", "default": 0}, "search_time": {"type": "number", "title": "Search Time"}}, "type": "object", "required": ["search_time"], "title": "VectorSearchResponse", "description": "벡터 검색 응답 모델"}, "VectorSearchResult": {"properties": {"id": {"type": "string", "title": "Id"}, "content": {"type": "string", "title": "Content"}, "similarity": {"type": "number", "title": "Similarity"}, "vector": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "null"}], "title": "Vector"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "source": {"type": "string", "title": "Source"}}, "type": "object", "required": ["id", "content", "similarity", "source"], "title": "VectorSearchResult", "description": "벡터 검색 결과 단일 항목"}}}}