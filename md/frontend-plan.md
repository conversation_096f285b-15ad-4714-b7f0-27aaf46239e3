# Vector Database Manager Frontend Admin 페이지 구현 계획

## 📋 프로젝트 개요

### 목적
Vector Database Manager 백엔드 시스템을 위한 현대적이고 직관적인 웹 기반 관리자 대시보드 구축

### 현재 백엔드 시스템 분석
- **지원 데이터베이스**: Weaviate, Qdrant, Chroma, Elasticsearch, Meilisearch, MongoDB
- **핵심 API**: 통합 헬스체크, 통합 검색, 벡터 검색, 데이터베이스 관리
- **백엔드 포트**: 7200 (FastAPI)
- **아키텍처**: 레이어드 아키텍처 (API → Service → Client → Core)

## 🎯 기술 스택 선택

### Frontend Framework
**React 18 + TypeScript + Vite**
```json
{
  "framework": "React 18.2.0",
  "language": "TypeScript 5.0+",
  "build-tool": "Vite 4.4+",
  "package-manager": "npm"
}
```

**선택 이유:**
- 빠른 개발 및 HMR (Hot Module Replacement)
- TypeScript로 백엔드 API와 타입 안정성 확보
- 현대적 빌드 도구로 최적화된 번들링
- 컴포넌트 기반 구조로 확장성 우수

### UI 라이브러리
**Ant Design 5.x + Tailwind CSS 3.x**
```json
{
  "ui-library": "antd 5.8+",
  "styling": "tailwindcss 3.3+",
  "charts": "@ant-design/charts 1.4+",
  "icons": "@ant-design/icons 5.2+"
}
```

**선택 이유:**
- 엔터프라이즈급 컴포넌트 라이브러리
- 대시보드/admin 페이지에 최적화된 컴포넌트들
- 풍부한 차트 및 테이블 컴포넌트
- Tailwind CSS로 커스텀 스타일링 유연성

### 상태 관리
**Zustand + React Query (TanStack Query)**
```json
{
  "state-management": "zustand 4.4+",
  "server-state": "@tanstack/react-query 4.32+",
  "forms": "react-hook-form 7.45+",
  "validation": "zod 3.21+"
}
```

## 🏗 프로젝트 구조 설계

### 디렉토리 구조
```
frontend/
├── public/
│   ├── favicon.ico
│   ├── logo192.png
│   └── index.html
├── src/
│   ├── components/                 # 재사용 컴포넌트
│   │   ├── common/                # 공통 컴포넌트
│   │   │   ├── Loading.tsx
│   │   │   ├── ErrorBoundary.tsx
│   │   │   ├── StatusBadge.tsx
│   │   │   └── RefreshButton.tsx
│   │   ├── database/              # DB 관련 컴포넌트
│   │   │   ├── DatabaseCard.tsx
│   │   │   ├── DatabaseList.tsx
│   │   │   ├── DatabaseDetail.tsx
│   │   │   ├── ConnectionStatus.tsx
│   │   │   └── DatabaseMetrics.tsx
│   │   ├── search/                # 검색 관련 컴포넌트
│   │   │   ├── SearchInput.tsx
│   │   │   ├── SearchFilters.tsx
│   │   │   ├── SearchResults.tsx
│   │   │   ├── VectorSearchForm.tsx
│   │   │   └── SearchHistory.tsx
│   │   └── charts/                # 차트 컴포넌트
│   │       ├── HealthStatusChart.tsx
│   │       ├── ResponseTimeChart.tsx
│   │       ├── SearchActivityChart.tsx
│   │       └── DatabaseUsageChart.tsx
│   ├── pages/                     # 페이지 컴포넌트
│   │   ├── Dashboard/             # 대시보드
│   │   │   ├── index.tsx
│   │   │   ├── OverviewSection.tsx
│   │   │   ├── HealthSection.tsx
│   │   │   ├── MetricsSection.tsx
│   │   │   └── ActivitySection.tsx
│   │   ├── DatabaseManagement/    # DB 관리
│   │   │   ├── index.tsx
│   │   │   ├── DatabaseGrid.tsx
│   │   │   ├── DatabaseDetails.tsx
│   │   │   └── ConnectionTest.tsx
│   │   ├── SearchConsole/         # 검색 콘솔
│   │   │   ├── index.tsx
│   │   │   ├── TextSearch.tsx
│   │   │   ├── VectorSearch.tsx
│   │   │   ├── SearchAnalytics.tsx
│   │   │   └── SearchHistory.tsx
│   │   └── Settings/              # 설정
│   │       ├── index.tsx
│   │       ├── DatabaseConfig.tsx
│   │       ├── ApiSettings.tsx
│   │       └── SecuritySettings.tsx
│   ├── services/                  # API 서비스
│   │   ├── api.ts                # API 클라이언트 설정
│   │   ├── database.ts           # DB 관련 API
│   │   ├── search.ts             # 검색 관련 API
│   │   ├── health.ts             # 헬스체크 API
│   │   └── auth.ts               # 인증 API
│   ├── types/                     # TypeScript 타입
│   │   ├── database.ts           # DB 타입 정의
│   │   ├── search.ts             # 검색 타입 정의
│   │   ├── api.ts                # API 응답 타입
│   │   ├── chart.ts              # 차트 데이터 타입
│   │   └── common.ts             # 공통 타입
│   ├── hooks/                     # 커스텀 훅
│   │   ├── useDatabase.ts        # DB 상태 관리
│   │   ├── useSearch.ts          # 검색 상태 관리
│   │   ├── useHealth.ts          # 헬스체크 상태 관리
│   │   ├── useWebSocket.ts       # 실시간 데이터
│   │   └── useLocalStorage.ts    # 로컬 스토리지
│   ├── stores/                    # Zustand 스토어
│   │   ├── databaseStore.ts      # DB 상태 스토어
│   │   ├── searchStore.ts        # 검색 상태 스토어
│   │   ├── settingsStore.ts      # 설정 스토어
│   │   └── authStore.ts          # 인증 스토어
│   ├── utils/                     # 유틸리티
│   │   ├── formatters.ts         # 데이터 포맷팅
│   │   ├── validators.ts         # 유효성 검사
│   │   ├── constants.ts          # 상수 정의
│   │   ├── helpers.ts            # 도우미 함수
│   │   └── dateUtils.ts          # 날짜 유틸리티
│   ├── layouts/                   # 레이아웃
│   │   ├── AdminLayout.tsx       # 메인 Admin 레이아웃
│   │   ├── Sidebar.tsx           # 사이드바
│   │   ├── Header.tsx            # 헤더
│   │   └── Footer.tsx            # 푸터
│   ├── styles/                    # 스타일
│   │   ├── globals.css           # 전역 스타일
│   │   ├── components.css        # 컴포넌트 스타일
│   │   └── themes.css            # 테마 스타일
│   ├── App.tsx                    # 메인 App 컴포넌트
│   ├── main.tsx                   # 애플리케이션 진입점
│   └── vite-env.d.ts             # Vite 타입 정의
├── package.json                   # 의존성 및 스크립트
├── vite.config.ts                # Vite 설정
├── tailwind.config.js            # Tailwind CSS 설정
├── tsconfig.json                 # TypeScript 설정
├── .eslintrc.js                  # ESLint 설정
├── .prettierrc                   # Prettier 설정
└── README.md                     # Frontend 문서
```

## 📱 주요 페이지 및 기능 설계

### 1. 메인 대시보드 (`/dashboard`)

#### 1.1 페이지 구성
```typescript
// src/pages/Dashboard/index.tsx
import React from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import { useHealth } from '@/hooks/useHealth';
import OverviewSection from './OverviewSection';
import HealthSection from './HealthSection';
import MetricsSection from './MetricsSection';
import ActivitySection from './ActivitySection';

const Dashboard: React.FC = () => {
  const { healthData, isLoading } = useHealth();

  return (
    <div className="dashboard-container space-y-6">
      {/* 상단 통계 카드들 */}
      <OverviewSection healthData={healthData} />
      
      {/* 헬스 상태 섹션 */}
      <HealthSection />
      
      {/* 성능 메트릭 섹션 */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <MetricsSection />
        </Col>
        <Col span={12}>
          <ActivitySection />
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
```

#### 1.2 핵심 컴포넌트

**OverviewSection.tsx** - 전체 시스템 개요
```typescript
// src/pages/Dashboard/OverviewSection.tsx
import React from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import { DatabaseOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { HealthData } from '@/types/api';

interface OverviewSectionProps {
  healthData: HealthData | null;
}

const OverviewSection: React.FC<OverviewSectionProps> = ({ healthData }) => {
  const healthyCount = healthData?.databases?.filter(db => db.status === 'healthy').length || 0;
  const totalCount = healthData?.databases?.length || 0;
  const unhealthyCount = totalCount - healthyCount;

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={8}>
        <Card>
          <Statistic
            title="총 데이터베이스"
            value={totalCount}
            prefix={<DatabaseOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={8}>
        <Card>
          <Statistic
            title="정상 상태"
            value={healthyCount}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={8}>
        <Card>
          <Statistic
            title="오류 상태"
            value={unhealthyCount}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default OverviewSection;
```

**HealthSection.tsx** - 실시간 헬스 모니터링
```typescript
// src/pages/Dashboard/HealthSection.tsx
import React from 'react';
import { Card, Table, Tag, Space, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { useHealth } from '@/hooks/useHealth';
import { DatabaseStatus } from '@/types/database';

const HealthSection: React.FC = () => {
  const { healthData, isLoading, refetch } = useHealth();

  const columns = [
    {
      title: '데이터베이스',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: DatabaseStatus) => (
        <Space>
          <strong>{name}</strong>
          <Tag color={getTagColor(record.type)}>{record.type}</Tag>
        </Space>
      ),
    },
    {
      title: '상태',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'healthy' ? 'green' : 'red'}>
          {status === 'healthy' ? '정상' : '오류'}
        </Tag>
      ),
    },
    {
      title: '응답시간',
      dataIndex: 'response_time',
      key: 'response_time',
      render: (time: number) => time ? `${time.toFixed(2)}ms` : '-',
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      render: (url: string) => <code className="text-sm">{url}</code>,
    },
  ];

  const getTagColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'vector': 'purple',
      'search': 'blue',
      'document': 'orange',
    };
    return colorMap[type] || 'default';
  };

  return (
    <Card 
      title="데이터베이스 상태" 
      extra={
        <Button 
          icon={<ReloadOutlined />} 
          onClick={() => refetch()}
          loading={isLoading}
        >
          새로고침
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={healthData?.databases || []}
        loading={isLoading}
        rowKey="name"
        pagination={false}
        size="small"
      />
    </Card>
  );
};

export default HealthSection;
```

### 2. 데이터베이스 관리 (`/databases`)

#### 2.1 메인 페이지 구성
```typescript
// src/pages/DatabaseManagement/index.tsx
import React, { useState } from 'react';
import { Row, Col, Card } from 'antd';
import DatabaseGrid from './DatabaseGrid';
import DatabaseDetails from './DatabaseDetails';
import { useDatabases } from '@/hooks/useDatabase';

const DatabaseManagement: React.FC = () => {
  const [selectedDatabase, setSelectedDatabase] = useState<string>('');
  const { databases, isLoading } = useDatabases();

  return (
    <div className="database-management">
      <Row gutter={[16, 16]} className="h-full">
        <Col xs={24} lg={12}>
          <Card title="데이터베이스 목록" className="h-full">
            <DatabaseGrid 
              databases={databases}
              selectedDatabase={selectedDatabase}
              onSelect={setSelectedDatabase}
              loading={isLoading}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="상세 정보" className="h-full">
            <DatabaseDetails 
              databaseName={selectedDatabase}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DatabaseManagement;
```

#### 2.2 데이터베이스 그리드
```typescript
// src/pages/DatabaseManagement/DatabaseGrid.tsx
import React from 'react';
import { List, Avatar, Badge, Space, Button } from 'antd';
import { DatabaseOutlined, SettingOutlined, ReloadOutlined } from '@ant-design/icons';
import { DatabaseStatus } from '@/types/database';

interface DatabaseGridProps {
  databases: DatabaseStatus[];
  selectedDatabase: string;
  onSelect: (name: string) => void;
  loading: boolean;
}

const DatabaseGrid: React.FC<DatabaseGridProps> = ({
  databases,
  selectedDatabase,
  onSelect,
  loading
}) => {
  const getDatabaseIcon = (type: string) => {
    const iconMap: Record<string, string> = {
      'weaviate': '🔮',
      'qdrant': '🎯',
      'chroma': '🎨',
      'elasticsearch': '🔍',
      'meilisearch': '⚡',
      'mongodb': '🍃',
    };
    return iconMap[type.toLowerCase()] || '💾';
  };

  return (
    <List
      loading={loading}
      dataSource={databases}
      renderItem={(database) => (
        <List.Item
          className={`cursor-pointer transition-all duration-200 ${
            selectedDatabase === database.name 
              ? 'bg-blue-50 border-l-4 border-blue-500' 
              : 'hover:bg-gray-50'
          }`}
          onClick={() => onSelect(database.name)}
          actions={[
            <Button type="text" icon={<SettingOutlined />} size="small" />,
            <Button type="text" icon={<ReloadOutlined />} size="small" />
          ]}
        >
          <List.Item.Meta
            avatar={
              <Badge 
                status={database.status === 'healthy' ? 'success' : 'error'}
                dot
              >
                <Avatar icon={getDatabaseIcon(database.type)} />
              </Badge>
            }
            title={<strong>{database.name}</strong>}
            description={
              <Space direction="vertical" size="small">
                <span className="text-sm text-gray-600">{database.url}</span>
                <span className="text-xs text-gray-500">
                  응답시간: {database.response_time?.toFixed(2) || '-'}ms
                </span>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );
};

export default DatabaseGrid;
```

### 3. 검색 콘솔 (`/search`)

#### 3.1 통합 검색 인터페이스
```typescript
// src/pages/SearchConsole/index.tsx
import React, { useState } from 'react';
import { Card, Tabs, Row, Col } from 'antd';
import { SearchOutlined, AimOutlined, BarChartOutlined } from '@ant-design/icons';
import TextSearch from './TextSearch';
import VectorSearch from './VectorSearch';
import SearchAnalytics from './SearchAnalytics';

const SearchConsole: React.FC = () => {
  const [activeTab, setActiveTab] = useState('text');

  const tabItems = [
    {
      key: 'text',
      label: (
        <span>
          <SearchOutlined />
          텍스트 검색
        </span>
      ),
      children: <TextSearch />,
    },
    {
      key: 'vector',
      label: (
        <span>
          <AimOutlined />
          벡터 검색
        </span>
      ),
      children: <VectorSearch />,
    },
    {
      key: 'analytics',
      label: (
        <span>
          <BarChartOutlined />
          검색 분석
        </span>
      ),
      children: <SearchAnalytics />,
    },
  ];

  return (
    <div className="search-console">
      <Card>
        <Tabs 
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </div>
  );
};

export default SearchConsole;
```

#### 3.2 텍스트 검색 컴포넌트
```typescript
// src/pages/SearchConsole/TextSearch.tsx
import React, { useState } from 'react';
import { Input, Button, Select, Row, Col, Card, List, Tag, Space, Spin } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { useSearch } from '@/hooks/useSearch';
import { SearchRequest } from '@/types/search';

const { Search } = Input;
const { Option } = Select;

const TextSearch: React.FC = () => {
  const [searchParams, setSearchParams] = useState<SearchRequest>({
    query: '',
    databases: [],
    search_type: 'text',
    limit: 10,
  });
  
  const { searchResults, isLoading, executeSearch } = useSearch();

  const databaseOptions = [
    { value: 'weaviate', label: 'Weaviate' },
    { value: 'qdrant', label: 'Qdrant' },
    { value: 'chroma', label: 'Chroma' },
    { value: 'elasticsearch', label: 'Elasticsearch' },
    { value: 'meilisearch', label: 'Meilisearch' },
    { value: 'mongodb', label: 'MongoDB' },
  ];

  const handleSearch = (query: string) => {
    const params = { ...searchParams, query };
    setSearchParams(params);
    executeSearch(params);
  };

  return (
    <div className="text-search space-y-6">
      {/* 검색 입력 섹션 */}
      <Card>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={16}>
            <Search
              placeholder="검색어를 입력하세요..."
              size="large"
              onSearch={handleSearch}
              loading={isLoading}
              enterButton={
                <Button type="primary" icon={<SearchOutlined />}>
                  검색
                </Button>
              }
            />
          </Col>
          <Col xs={24} md={8}>
            <Select
              mode="multiple"
              placeholder="데이터베이스 선택"
              style={{ width: '100%' }}
              value={searchParams.databases}
              onChange={(databases) => 
                setSearchParams(prev => ({ ...prev, databases }))
              }
            >
              {databaseOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 검색 결과 섹션 */}
      <Card title="검색 결과">
        <Spin spinning={isLoading}>
          {searchResults.length > 0 ? (
            <List
              dataSource={searchResults}
              renderItem={(result, index) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>결과 #{index + 1}</span>
                        <Tag color="blue">{result.database}</Tag>
                        {result.score && (
                          <Tag color="green">Score: {result.score.toFixed(3)}</Tag>
                        )}
                      </Space>
                    }
                    description={
                      <div className="space-y-2">
                        <div className="text-gray-700">{result.content}</div>
                        {result.metadata && (
                          <div className="text-sm text-gray-500">
                            메타데이터: {JSON.stringify(result.metadata)}
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          ) : (
            <div className="text-center text-gray-500 py-8">
              검색 결과가 없습니다.
            </div>
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default TextSearch;
```

### 4. 설정 관리 (`/settings`)

#### 4.1 설정 메인 페이지
```typescript
// src/pages/Settings/index.tsx
import React from 'react';
import { Card, Tabs, Row, Col } from 'antd';
import { DatabaseOutlined, ApiOutlined, SecurityScanOutlined } from '@ant-design/icons';
import DatabaseConfig from './DatabaseConfig';
import ApiSettings from './ApiSettings';
import SecuritySettings from './SecuritySettings';

const Settings: React.FC = () => {
  const tabItems = [
    {
      key: 'database',
      label: (
        <span>
          <DatabaseOutlined />
          데이터베이스 설정
        </span>
      ),
      children: <DatabaseConfig />,
    },
    {
      key: 'api',
      label: (
        <span>
          <ApiOutlined />
          API 설정
        </span>
      ),
      children: <ApiSettings />,
    },
    {
      key: 'security',
      label: (
        <span>
          <SecurityScanOutlined />
          보안 설정
        </span>
      ),
      children: <SecuritySettings />,
    },
  ];

  return (
    <div className="settings-page">
      <Card>
        <Tabs items={tabItems} size="large" />
      </Card>
    </div>
  );
};

export default Settings;
```

## 🔧 핵심 기술 구현

### 1. API 서비스 레이어

#### 1.1 기본 API 클라이언트
```typescript
// src/services/api.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

class ApiClient {
  private client: AxiosInstance;
  
  constructor() {
    this.client = axios.create({
      baseURL: 'http://localhost:7200/api/v1',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    this.setupInterceptors();
  }
  
  private setupInterceptors() {
    // 요청 인터셉터
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // 응답 인터셉터
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }
  
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }
  
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }
  
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }
  
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

#### 1.2 데이터베이스 API 서비스
```typescript
// src/services/database.ts
import { apiClient } from './api';
import { DatabaseStatus, DatabaseInfo } from '@/types/database';

export const databaseService = {
  // 모든 데이터베이스 상태 조회
  async getAllDatabases(): Promise<DatabaseStatus[]> {
    return apiClient.get<DatabaseStatus[]>('/unified/databases');
  },
  
  // 특정 데이터베이스 상세 정보 조회
  async getDatabaseInfo(name: string): Promise<DatabaseInfo> {
    return apiClient.get<DatabaseInfo>(`/databases/${name}/info`);
  },
  
  // 데이터베이스 연결 테스트
  async testConnection(name: string): Promise<{ success: boolean; message: string }> {
    return apiClient.post(`/databases/${name}/test-connection`);
  },
  
  // 데이터베이스 설정 업데이트
  async updateDatabaseConfig(name: string, config: any): Promise<void> {
    return apiClient.put(`/databases/${name}/config`, config);
  },
};
```

#### 1.3 헬스체크 API 서비스
```typescript
// src/services/health.ts
import { apiClient } from './api';
import { HealthResponse } from '@/types/api';

export const healthService = {
  // 전체 시스템 헬스체크
  async checkHealth(): Promise<HealthResponse> {
    return apiClient.get<HealthResponse>('/unified/health');
  },
  
  // 특정 데이터베이스 헬스체크
  async checkDatabaseHealth(name: string): Promise<HealthResponse> {
    return apiClient.get<HealthResponse>(`/databases/${name}/health`);
  },
};
```

#### 1.4 검색 API 서비스
```typescript
// src/services/search.ts
import { apiClient } from './api';
import { SearchRequest, VectorSearchRequest, SearchResponse } from '@/types/search';

export const searchService = {
  // 통합 텍스트 검색
  async textSearch(params: SearchRequest): Promise<SearchResponse> {
    return apiClient.post<SearchResponse>('/unified/search', params);
  },
  
  // 벡터 검색
  async vectorSearch(params: VectorSearchRequest): Promise<SearchResponse> {
    return apiClient.post<SearchResponse>('/unified/vector-search', params);
  },
  
  // 검색 히스토리 조회
  async getSearchHistory(): Promise<any[]> {
    return apiClient.get<any[]>('/search/history');
  },
};
```

### 2. 상태 관리 (Zustand)

#### 2.1 데이터베이스 스토어
```typescript
// src/stores/databaseStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { DatabaseStatus } from '@/types/database';
import { databaseService } from '@/services/database';

interface DatabaseStore {
  databases: DatabaseStatus[];
  selectedDatabase: string | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchDatabases: () => Promise<void>;
  selectDatabase: (name: string) => void;
  testConnection: (name: string) => Promise<boolean>;
  updateDatabaseConfig: (name: string, config: any) => Promise<void>;
}

export const useDatabaseStore = create<DatabaseStore>()(
  devtools(
    (set, get) => ({
      databases: [],
      selectedDatabase: null,
      isLoading: false,
      error: null,
      
      fetchDatabases: async () => {
        set({ isLoading: true, error: null });
        try {
          const databases = await databaseService.getAllDatabases();
          set({ databases, isLoading: false });
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },
      
      selectDatabase: (name: string) => {
        set({ selectedDatabase: name });
      },
      
      testConnection: async (name: string) => {
        try {
          const result = await databaseService.testConnection(name);
          return result.success;
        } catch (error) {
          console.error('Connection test failed:', error);
          return false;
        }
      },
      
      updateDatabaseConfig: async (name: string, config: any) => {
        try {
          await databaseService.updateDatabaseConfig(name, config);
          // 성공 후 데이터베이스 목록 새로고침
          get().fetchDatabases();
        } catch (error) {
          set({ error: (error as Error).message });
        }
      },
    }),
    { name: 'database-store' }
  )
);
```

#### 2.2 검색 스토어
```typescript
// src/stores/searchStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { SearchRequest, SearchResponse } from '@/types/search';
import { searchService } from '@/services/search';

interface SearchStore {
  searchResults: any[];
  searchHistory: any[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  executeTextSearch: (params: SearchRequest) => Promise<void>;
  executeVectorSearch: (params: any) => Promise<void>;
  loadSearchHistory: () => Promise<void>;
  clearResults: () => void;
}

export const useSearchStore = create<SearchStore>()(
  devtools(
    (set, get) => ({
      searchResults: [],
      searchHistory: [],
      isLoading: false,
      error: null,
      
      executeTextSearch: async (params: SearchRequest) => {
        set({ isLoading: true, error: null });
        try {
          const response = await searchService.textSearch(params);
          set({ 
            searchResults: response.results || [], 
            isLoading: false 
          });
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },
      
      executeVectorSearch: async (params: any) => {
        set({ isLoading: true, error: null });
        try {
          const response = await searchService.vectorSearch(params);
          set({ 
            searchResults: response.results || [], 
            isLoading: false 
          });
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },
      
      loadSearchHistory: async () => {
        try {
          const history = await searchService.getSearchHistory();
          set({ searchHistory: history });
        } catch (error) {
          console.error('Failed to load search history:', error);
        }
      },
      
      clearResults: () => {
        set({ searchResults: [], error: null });
      },
    }),
    { name: 'search-store' }
  )
);
```

### 3. 커스텀 훅

#### 3.1 헬스체크 훅
```typescript
// src/hooks/useHealth.ts
import { useQuery } from '@tanstack/react-query';
import { healthService } from '@/services/health';

export const useHealth = () => {
  const {
    data: healthData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['health'],
    queryFn: healthService.checkHealth,
    refetchInterval: 30000, // 30초마다 자동 갱신
    retry: 3,
  });

  return {
    healthData,
    isLoading,
    error,
    refetch,
  };
};
```

#### 3.2 데이터베이스 훅
```typescript
// src/hooks/useDatabase.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { databaseService } from '@/services/database';
import { message } from 'antd';

export const useDatabases = () => {
  return useQuery({
    queryKey: ['databases'],
    queryFn: databaseService.getAllDatabases,
    staleTime: 60000, // 1분간 캐시 유지
  });
};

export const useDatabaseInfo = (name: string) => {
  return useQuery({
    queryKey: ['database', name],
    queryFn: () => databaseService.getDatabaseInfo(name),
    enabled: !!name,
  });
};

export const useConnectionTest = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: databaseService.testConnection,
    onSuccess: (data, variables) => {
      message.success(`${variables} 연결 테스트 성공!`);
      queryClient.invalidateQueries({ queryKey: ['databases'] });
    },
    onError: (error, variables) => {
      message.error(`${variables} 연결 테스트 실패: ${error.message}`);
    },
  });
};
```

#### 3.3 실시간 데이터 훅 (WebSocket)
```typescript
// src/hooks/useWebSocket.ts
import { useEffect, useState } from 'react';
import { message } from 'antd';

interface WebSocketData {
  type: string;
  data: any;
  timestamp: string;
}

export const useWebSocket = (url: string) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [data, setData] = useState<WebSocketData | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const ws = new WebSocket(url);
    
    ws.onopen = () => {
      setIsConnected(true);
      setSocket(ws);
      console.log('WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      try {
        const parsedData = JSON.parse(event.data);
        setData(parsedData);
      } catch (error) {
        console.error('Failed to parse WebSocket data:', error);
      }
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      message.error('실시간 연결 오류가 발생했습니다.');
    };
    
    ws.onclose = () => {
      setIsConnected(false);
      setSocket(null);
      console.log('WebSocket disconnected');
    };
    
    return () => {
      ws.close();
    };
  }, [url]);

  const sendMessage = (message: any) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify(message));
    }
  };

  return {
    data,
    isConnected,
    sendMessage,
  };
};
```

### 4. TypeScript 타입 정의

#### 4.1 데이터베이스 타입
```typescript
// src/types/database.ts
export interface DatabaseStatus {
  name: string;
  type: 'vector' | 'search' | 'document';
  status: 'healthy' | 'unhealthy' | 'unknown';
  url: string;
  response_time?: number;
  error?: string;
}

export interface DatabaseInfo extends DatabaseStatus {
  version?: string;
  description?: string;
  features: string[];
  metrics: {
    documents_count?: number;
    collections_count?: number;
    storage_size?: number;
    memory_usage?: number;
  };
  configuration: Record<string, any>;
}

export interface DatabaseConfig {
  url: string;
  timeout: number;
  max_connections: number;
  custom_settings: Record<string, any>;
}
```

#### 4.2 검색 타입
```typescript
// src/types/search.ts
export interface SearchRequest {
  query: string;
  databases?: string[];
  search_type: 'text' | 'vector' | 'hybrid';
  limit: number;
  offset?: number;
  filters?: Record<string, any>;
}

export interface VectorSearchRequest {
  vector: number[];
  databases?: string[];
  limit: number;
  threshold?: number;
  filters?: Record<string, any>;
}

export interface SearchResult {
  id: string;
  content: string;
  score?: number;
  database: string;
  metadata?: Record<string, any>;
  vector?: number[];
}

export interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  total: number;
  query_time: number;
  message?: string;
}
```

#### 4.3 API 응답 타입
```typescript
// src/types/api.ts
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: string;
}

export interface HealthResponse extends BaseResponse {
  status: 'healthy' | 'unhealthy';
  databases: DatabaseStatus[];
  uptime: number;
  version: string;
}

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

## 📦 패키지 설정

### package.json
```json
{
  "name": "vector-db-manager-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "antd": "^5.8.6",
    "@ant-design/icons": "^5.2.6",
    "@ant-design/charts": "^1.4.2",
    "axios": "^1.5.0",
    "@tanstack/react-query": "^4.32.6",
    "zustand": "^4.4.1",
    "react-hook-form": "^7.45.4",
    "zod": "^3.21.4",
    "@hookform/resolvers": "^3.3.1",
    "dayjs": "^1.11.9",
    "lodash": "^4.17.21",
    "clsx": "^2.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@types/lodash": "^4.14.195",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "prettier": "^3.0.0",
    "tailwindcss": "^3.3.3",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.27",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}
```

### vite.config.ts
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:7200',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd', '@ant-design/icons'],
          charts: ['@ant-design/charts'],
        },
      },
    },
  },
});
```

### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
        },
        success: {
          50: '#f0fdf4',
          500: '#22c55e',
          600: '#16a34a',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',
        },
        danger: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',
        },
      },
    },
  },
  plugins: [],
  corePlugins: {
    preflight: false, // Ant Design과의 충돌 방지
  },
}
```

## 🚀 구현 단계별 가이드

### Phase 1: 기본 설정 및 구조 생성 (1-2일)
1. **프로젝트 초기화**
   ```bash
   npm create vite@latest vector-db-manager-frontend -- --template react-ts
   cd vector-db-manager-frontend
   npm install
   ```

2. **필수 패키지 설치**
   ```bash
   npm install antd @ant-design/icons @ant-design/charts
   npm install axios @tanstack/react-query zustand
   npm install react-router-dom react-hook-form @hookform/resolvers zod
   npm install tailwindcss autoprefixer postcss
   npm install -D @types/lodash prettier eslint
   ```

3. **기본 구조 생성**
   - 디렉토리 구조 생성
   - 기본 컴포넌트 틀 작성
   - 라우팅 설정

### Phase 2: 핵심 기능 구현 (3-5일)
1. **API 서비스 레이어 구현**
   - 기본 API 클라이언트 설정
   - 각 서비스별 API 함수 구현
   - 에러 처리 및 인터셉터 설정

2. **상태 관리 구현**
   - Zustand 스토어 구현
   - React Query 설정
   - 커스텀 훅 작성

3. **기본 레이아웃 구현**
   - AdminLayout 컴포넌트
   - 사이드바 및 네비게이션
   - 헤더 및 사용자 인터페이스

### Phase 3: 대시보드 페이지 구현 (2-3일)
1. **메인 대시보드**
   - 시스템 개요 카드들
   - 실시간 헬스 상태 테이블
   - 성능 메트릭 차트

2. **차트 및 시각화**
   - 응답시간 차트
   - 데이터베이스 상태 차트
   - 검색 활동 분석

### Phase 4: 데이터베이스 관리 페이지 (2-3일)
1. **데이터베이스 목록**
   - 그리드 형태의 DB 목록
   - 상태별 필터링
   - 실시간 상태 업데이트

2. **상세 관리 기능**
   - 개별 DB 상세 정보
   - 연결 테스트 기능
   - 설정 변경 인터페이스

### Phase 5: 검색 콘솔 구현 (3-4일)
1. **텍스트 검색**
   - 통합 검색 인터페이스
   - 필터 및 옵션 설정
   - 결과 표시 및 페이지네이션

2. **벡터 검색**
   - 벡터 입력 인터페이스
   - 유사도 임계값 설정
   - 결과 시각화

3. **검색 분석**
   - 검색 히스토리
   - 성능 분석 차트
   - 사용량 통계

### Phase 6: 설정 및 부가 기능 (1-2일)
1. **설정 페이지**
   - 데이터베이스 설정 관리
   - API 설정
   - 보안 설정

2. **추가 기능**
   - 다크 테마 지원
   - 반응형 디자인 최적화
   - 접근성 개선

### Phase 7: 테스트 및 최적화 (1-2일)
1. **성능 최적화**
   - 코드 스플리팅
   - 지연 로딩
   - 메모리 최적화

2. **테스트**
   - 컴포넌트 테스트
   - API 통합 테스트
   - 사용자 시나리오 테스트

## 🔧 배포 및 운영

### Docker 설정
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### nginx.conf
```nginx
server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://vectordb-manager:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### docker-compose.yml 업데이트
```yaml
# 기존 docker-compose.yml에 추가
services:
  # 기존 vectordb-manager 서비스...
  
  # Frontend 서비스 추가
  vectordb-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: vector-db-manager-frontend
    ports:
      - "3000:80"
    depends_on:
      - vectordb-manager
    networks:
      - dbs-network
    restart: unless-stopped
```

## 📊 예상 결과물

### 주요 기능 스크린샷 (구현 후)
1. **대시보드**: 6개 DB 상태, 실시간 메트릭, 차트
2. **DB 관리**: 개별 DB 상세 정보, 연결 테스트, 설정
3. **검색 콘솔**: 통합 검색, 벡터 검색, 결과 분석
4. **설정**: 시스템 설정, API 구성, 보안 옵션

### 성능 목표
- **초기 로딩**: < 3초
- **페이지 전환**: < 1초
- **실시간 업데이트**: 30초 간격
- **검색 응답**: < 2초

### 호환성
- **브라우저**: Chrome 90+, Firefox 88+, Safari 14+
- **디바이스**: 데스크톱, 태블릿, 모바일 (반응형)
- **접근성**: WCAG 2.1 AA 준수

## 🎉 마무리

이 계획에 따라 구현하면 Vector Database Manager를 위한 완전한 관리자 대시보드를 구축할 수 있습니다. 각 단계별로 점진적으로 개발하여 안정적이고 확장 가능한 프론트엔드 애플리케이션을 만들 수 있을 것입니다.

구현 과정에서 필요한 추가적인 기능이나 수정사항이 있다면 언제든지 계획을 조정할 수 있도록 유연하게 설계되어 있습니다.
