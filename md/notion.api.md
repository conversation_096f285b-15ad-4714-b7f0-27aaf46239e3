# React에서 Notion API 사용 완전 가이드 (2025)

## 목차
1. [개요](#개요)
2. [CORS 문제와 해결책](#cors-문제와-해결책)
3. [Notion Integration 설정](#notion-integration-설정)
4. [React + Next.js 프로젝트 설정](#react--nextjs-프로젝트-설정)
5. [API 구현 방법들](#api-구현-방법들)
6. [TypeScript 타입 정의](#typescript-타입-정의)
7. [실제 구현 예제](#실제-구현-예제)
8. [고급 사용법](#고급-사용법)
9. [배포 및 최적화](#배포-및-최적화)
10. [트러블슈팅](#트러블슈팅)

## 개요

Notion API를 React 애플리케이션에서 사용할 때 가장 중요한 점은 **CORS(Cross-Origin Resource Sharing) 제한**입니다. Notion API는 보안상의 이유로 브라우저에서 직접 호출할 수 없으며, 반드시 서버사이드에서 호출해야 합니다.

### 주요 특징
- **공식 SDK**: `@notionhq/client` (v4.0.0 최신 버전)
- **Rate Limit**: 초당 3개 요청
- **인증**: Integration Token 방식
- **지원 기능**: Pages, Databases, Blocks, Users, Comments

## CORS 문제와 해결책

### 문제점
```javascript
// ❌ 이렇게 하면 CORS 에러 발생
const { Client } = require('@notionhq/client');
const notion = new Client({ auth: 'your-token' });
// 브라우저에서 직접 호출 불가능
```

### 해결책들

#### 1. Next.js API Routes (권장)
```javascript
// pages/api/notion/posts.js
import { Client } from '@notionhq/client';

const notion = new Client({
  auth: process.env.NOTION_TOKEN,
});

export default async function handler(req, res) {
  try {
    const response = await notion.databases.query({
      database_id: process.env.NOTION_DATABASE_ID,
    });
    res.status(200).json(response);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

#### 2. Express.js 백엔드 프록시
```javascript
// server.js
const express = require('express');
const cors = require('cors');
const { Client } = require('@notionhq/client');

const app = express();
app.use(cors());
app.use(express.json());

const notion = new Client({
  auth: process.env.NOTION_TOKEN,
});

app.get('/api/notion/pages', async (req, res) => {
  try {
    const response = await notion.databases.query({
      database_id: process.env.NOTION_DATABASE_ID,
    });
    res.json(response);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(5000);
```

#### 3. Cloudflare Workers 프록시
```javascript
// worker.js
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET,HEAD,POST,OPTIONS",
  "Access-Control-Max-Age": "86400",
};

const API_URL = "https://api.notion.com/v1/databases/YOUR_DB_ID/query";
const NOTION_TOKEN = "YOUR_TOKEN";

async function handleRequest(request) {
  const response = await fetch(API_URL, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${NOTION_TOKEN}`,
      'Notion-Version': '2022-06-28',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });

  const data = await response.json();
  
  return new Response(JSON.stringify(data), {
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/json',
    },
  });
}

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});
```

## Notion Integration 설정

### 1. Integration 생성
1. [Notion Integrations](https://www.notion.so/my-integrations) 페이지 방문
2. "New integration" 클릭
3. 이름 설정 및 Capabilities 선택:
   - Read content
   - Update content
   - Insert content
4. Integration Token 복사

### 2. 데이터베이스 연결
1. Notion 데이터베이스 페이지에서 "..." 메뉴 클릭
2. "Add connections" 선택
3. 생성한 Integration 추가

### 3. Database ID 확인
```
https://www.notion.so/myworkspace/a8aec43384f447ed84390e8e42c2e089?v=...
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                Database ID
```

## React + Next.js 프로젝트 설정

### 1. 프로젝트 초기화
```bash
npx create-next-app@latest my-notion-app --typescript --tailwind --eslint
cd my-notion-app
npm install @notionhq/client
```

### 2. 환경변수 설정
```bash
# .env.local
NOTION_TOKEN=secret_your_integration_token
NOTION_DATABASE_ID=your_database_id
```

### 3. 패키지 구조
```
src/
├── components/
│   ├── NotionRenderer.tsx
│   └── PostCard.tsx
├── lib/
│   ├── notion.ts
│   └── types.ts
├── pages/
│   ├── api/
│   │   └── notion/
│   │       ├── posts.ts
│   │       └── [id].ts
│   ├── blog/
│   │   └── [slug].tsx
│   └── index.tsx
└── styles/
    └── globals.css
```

## API 구현 방법들

### 1. 기본 Notion 클라이언트 설정

```typescript
// lib/notion.ts
import { Client } from '@notionhq/client';
import { 
  DatabaseObjectResponse, 
  PageObjectResponse,
  BlockObjectResponse 
} from '@notionhq/client/build/src/api-endpoints';

export const notion = new Client({
  auth: process.env.NOTION_TOKEN,
});

// React.cache를 사용한 캐싱 (Next.js 13+ App Router)
import React from 'react';

export const fetchDatabase = React.cache(async (databaseId: string) => {
  return notion.databases.query({
    database_id: databaseId,
    filter: {
      property: 'Status',
      select: {
        equals: 'Published',
      },
    },
    sorts: [
      {
        property: 'Created',
        direction: 'descending',
      },
    ],
  });
});

export const fetchPage = React.cache(async (pageId: string) => {
  return notion.pages.retrieve({ page_id: pageId });
});

export const fetchPageBlocks = React.cache(async (pageId: string) => {
  return notion.blocks.children.list({
    block_id: pageId,
    page_size: 100,
  });
});
```

### 2. API Routes 구현

```typescript
// pages/api/notion/posts.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { notion } from '../../../lib/notion';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const response = await notion.databases.query({
      database_id: process.env.NOTION_DATABASE_ID!,
      filter: {
        property: 'Status',
        select: {
          equals: 'Published',
        },
      },
      sorts: [
        {
          property: 'Created',
          direction: 'descending',
        },
      ],
    });

    const posts = response.results.map((page: any) => ({
      id: page.id,
      title: page.properties.Title?.title[0]?.text?.content || '',
      slug: page.properties.Slug?.rich_text[0]?.text?.content || '',
      excerpt: page.properties.Excerpt?.rich_text[0]?.text?.content || '',
      status: page.properties.Status?.select?.name || '',
      created: page.properties.Created?.created_time || '',
      tags: page.properties.Tags?.multi_select?.map((tag: any) => tag.name) || [],
    }));

    res.status(200).json({ posts });
  } catch (error) {
    console.error('Notion API Error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch posts',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
```

```typescript
// pages/api/notion/posts/[id].ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { notion } from '../../../../lib/notion';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;

  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const page = await notion.pages.retrieve({ page_id: id as string });
    const blocks = await notion.blocks.children.list({
      block_id: id as string,
      page_size: 100,
    });

    res.status(200).json({ page, blocks });
  } catch (error) {
    console.error('Notion API Error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch page',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
```

### 3. SSG/SSR 데이터 페칭

```typescript
// pages/blog/[slug].tsx
import { GetStaticProps, GetStaticPaths } from 'next';
import { notion } from '../../lib/notion';

interface BlogPostProps {
  page: any;
  blocks: any[];
}

export default function BlogPost({ page, blocks }: BlogPostProps) {
  return (
    <article>
      <h1>{page.properties.Title.title[0]?.text.content}</h1>
      <div>
        {blocks.map((block) => (
          <NotionBlock key={block.id} block={block} />
        ))}
      </div>
    </article>
  );
}

export const getStaticPaths: GetStaticPaths = async () => {
  const response = await notion.databases.query({
    database_id: process.env.NOTION_DATABASE_ID!,
  });

  const paths = response.results.map((page: any) => ({
    params: {
      slug: page.properties.Slug.rich_text[0]?.text.content,
    },
  }));

  return {
    paths,
    fallback: 'blocking',
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const slug = params?.slug as string;

  const response = await notion.databases.query({
    database_id: process.env.NOTION_DATABASE_ID!,
    filter: {
      property: 'Slug',
      rich_text: {
        equals: slug,
      },
    },
  });

  const page = response.results[0];
  if (!page) {
    return { notFound: true };
  }

  const blocks = await notion.blocks.children.list({
    block_id: page.id,
  });

  return {
    props: {
      page,
      blocks: blocks.results,
    },
    revalidate: 3600, // 1시간마다 재생성
  };
};
```

## TypeScript 타입 정의

```typescript
// lib/types.ts
import { 
  DatabaseObjectResponse,
  PageObjectResponse,
  BlockObjectResponse,
} from '@notionhq/client/build/src/api-endpoints';

export interface NotionPage {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  status: string;
  created: string;
  tags: string[];
  coverImage?: string;
}

export interface NotionDatabase {
  id: string;
  title: string;
  description: string;
  properties: Record<string, any>;
}

export interface NotionBlock {
  id: string;
  type: string;
  content: any;
  children?: NotionBlock[];
}

// Rich Text 타입
export interface RichText {
  type: 'text' | 'mention' | 'equation';
  text?: {
    content: string;
    link?: {
      url: string;
    };
  };
  annotations: {
    bold: boolean;
    italic: boolean;
    strikethrough: boolean;
    underline: boolean;
    code: boolean;
    color: string;
  };
  plain_text: string;
  href?: string;
}

// Property 타입들
export interface TitleProperty {
  id: string;
  type: 'title';
  title: RichText[];
}

export interface RichTextProperty {
  id: string;
  type: 'rich_text';
  rich_text: RichText[];
}

export interface SelectProperty {
  id: string;
  type: 'select';
  select: {
    id: string;
    name: string;
    color: string;
  } | null;
}

export interface MultiSelectProperty {
  id: string;
  type: 'multi_select';
  multi_select: Array<{
    id: string;
    name: string;
    color: string;
  }>;
}

export interface DateProperty {
  id: string;
  type: 'date';
  date: {
    start: string;
    end?: string;
    time_zone?: string;
  } | null;
}
```

## 실제 구현 예제

### 1. 블로그 포스트 목록 컴포넌트

```typescript
// components/BlogList.tsx
import { useState, useEffect } from 'react';
import { NotionPage } from '../lib/types';

interface BlogListProps {
  initialPosts?: NotionPage[];
}

export default function BlogList({ initialPosts = [] }: BlogListProps) {
  const [posts, setPosts] = useState<NotionPage[]>(initialPosts);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPosts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/notion/posts');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch posts');
      }
      
      setPosts(data.posts);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (initialPosts.length === 0) {
      fetchPosts();
    }
  }, [initialPosts.length]);

  if (loading) {
    return <div className="text-center py-8">Loading posts...</div>;
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        <p>Error: {error}</p>
        <button 
          onClick={fetchPosts}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {posts.map((post) => (
        <PostCard key={post.id} post={post} />
      ))}
    </div>
  );
}
```

### 2. 포스트 카드 컴포넌트

```typescript
// components/PostCard.tsx
import Link from 'next/link';
import { NotionPage } from '../lib/types';

interface PostCardProps {
  post: NotionPage;
}

export default function PostCard({ post }: PostCardProps) {
  return (
    <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      {post.coverImage && (
        <img 
          src={post.coverImage} 
          alt={post.title}
          className="w-full h-48 object-cover"
        />
      )}
      
      <div className="p-6">
        <h2 className="text-xl font-bold mb-2 line-clamp-2">
          <Link 
            href={`/blog/${post.slug}`}
            className="text-gray-900 hover:text-blue-600 transition-colors"
          >
            {post.title}
          </Link>
        </h2>
        
        <p className="text-gray-600 mb-4 line-clamp-3">
          {post.excerpt}
        </p>
        
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <span 
                key={tag}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded"
              >
                {tag}
              </span>
            ))}
          </div>
          
          <time className="text-sm text-gray-500">
            {new Date(post.created).toLocaleDateString('ko-KR')}
          </time>
        </div>
      </div>
    </article>
  );
}
```

### 3. Notion Block 렌더러

```typescript
// components/NotionRenderer.tsx
import { BlockObjectResponse } from '@notionhq/client/build/src/api-endpoints';

interface NotionRendererProps {
  blocks: BlockObjectResponse[];
}

export default function NotionRenderer({ blocks }: NotionRendererProps) {
  return (
    <div className="notion-content">
      {blocks.map((block) => (
        <NotionBlock key={block.id} block={block} />
      ))}
    </div>
  );
}

function NotionBlock({ block }: { block: BlockObjectResponse }) {
  const { type } = block;

  switch (type) {
    case 'paragraph':
      return (
        <p className="mb-4">
          <RichText text={block.paragraph.rich_text} />
        </p>
      );

    case 'heading_1':
      return (
        <h1 className="text-3xl font-bold mb-6 mt-8">
          <RichText text={block.heading_1.rich_text} />
        </h1>
      );

    case 'heading_2':
      return (
        <h2 className="text-2xl font-bold mb-4 mt-6">
          <RichText text={block.heading_2.rich_text} />
        </h2>
      );

    case 'heading_3':
      return (
        <h3 className="text-xl font-bold mb-3 mt-4">
          <RichText text={block.heading_3.rich_text} />
        </h3>
      );

    case 'bulleted_list_item':
      return (
        <li className="ml-6 mb-2 list-disc">
          <RichText text={block.bulleted_list_item.rich_text} />
        </li>
      );

    case 'numbered_list_item':
      return (
        <li className="ml-6 mb-2 list-decimal">
          <RichText text={block.numbered_list_item.rich_text} />
        </li>
      );

    case 'code':
      return (
        <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto mb-4">
          <code className={`language-${block.code.language}`}>
            <RichText text={block.code.rich_text} />
          </code>
        </pre>
      );

    case 'quote':
      return (
        <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-4">
          <RichText text={block.quote.rich_text} />
        </blockquote>
      );

    case 'divider':
      return <hr className="my-8 border-gray-300" />;

    case 'image':
      const imageUrl = block.image.type === 'external' 
        ? block.image.external.url 
        : block.image.file.url;
      const caption = block.image.caption.length > 0 
        ? block.image.caption[0].plain_text 
        : '';

      return (
        <figure className="mb-6">
          <img 
            src={imageUrl} 
            alt={caption}
            className="w-full rounded-md"
          />
          {caption && (
            <figcaption className="text-center text-gray-600 text-sm mt-2">
              {caption}
            </figcaption>
          )}
        </figure>
      );

    default:
      return (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-yellow-800">
            Unsupported block type: {type}
          </p>
        </div>
      );
  }
}

function RichText({ text }: { text: any[] }) {
  return (
    <>
      {text.map((segment, index) => {
        const {
          annotations: { bold, italic, strikethrough, underline, code, color },
          text: textContent,
          href,
        } = segment;

        let className = '';
        if (bold) className += 'font-bold ';
        if (italic) className += 'italic ';
        if (strikethrough) className += 'line-through ';
        if (underline) className += 'underline ';
        if (code) className += 'bg-gray-100 px-1 rounded text-sm font-mono ';
        if (color !== 'default') className += `text-${color}-600 `;

        const content = textContent?.content || '';

        if (href) {
          return (
            <a
              key={index}
              href={href}
              className={`${className} text-blue-600 hover:underline`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {content}
            </a>
          );
        }

        return (
          <span key={index} className={className}>
            {content}
          </span>
        );
      })}
    </>
  );
}
```

### 4. 데이터 훅 (Custom Hook)

```typescript
// hooks/useNotionData.ts
import { useState, useEffect, useCallback } from 'react';

interface UseNotionDataOptions {
  endpoint: string;
  dependencies?: any[];
  enabled?: boolean;
}

export function useNotionData<T>({ 
  endpoint, 
  dependencies = [], 
  enabled = true 
}: UseNotionDataOptions) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(endpoint);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch data');
      }

      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [endpoint, enabled]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}

// 사용 예제
export function useBlogPosts() {
  return useNotionData<{ posts: NotionPage[] }>({
    endpoint: '/api/notion/posts',
  });
}

export function useBlogPost(id: string) {
  return useNotionData<{ page: any; blocks: any[] }>({
    endpoint: `/api/notion/posts/${id}`,
    dependencies: [id],
    enabled: !!id,
  });
}
```

## 고급 사용법

### 1. 페이지네이션 구현

```typescript
// lib/notion-paginated.ts
export async function fetchPostsPaginated(
  startCursor?: string,
  pageSize: number = 10
) {
  const response = await notion.databases.query({
    database_id: process.env.NOTION_DATABASE_ID!,
    start_cursor: startCursor,
    page_size: pageSize,
    filter: {
      property: 'Status',
      select: {
        equals: 'Published',
      },
    },
    sorts: [
      {
        property: 'Created',
        direction: 'descending',
      },
    ],
  });

  return {
    posts: response.results,
    nextCursor: response.next_cursor,
    hasMore: response.has_more,
  };
}
```

### 2. 검색 기능

```typescript
// pages/api/notion/search.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { notion } from '../../../lib/notion';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { query, filter } = req.query;

  try {
    const response = await notion.databases.query({
      database_id: process.env.NOTION_DATABASE_ID!,
      filter: {
        and: [
          {
            property: 'Status',
            select: {
              equals: 'Published',
            },
          },
          query ? {
            or: [
              {
                property: 'Title',
                title: {
                  contains: query as string,
                },
              },
              {
                property: 'Content',
                rich_text: {
                  contains: query as string,
                },
              },
            ],
          } : undefined,
          filter ? {
            property: 'Tags',
            multi_select: {
              contains: filter as string,
            },
          } : undefined,
        ].filter(Boolean) as any,
      },
    });

    const posts = response.results.map((page: any) => ({
      id: page.id,
      title: page.properties.Title?.title[0]?.text?.content || '',
      // ... 다른 속성들
    }));

    res.status(200).json({ posts });
  } catch (error) {
    res.status(500).json({ error: 'Search failed' });
  }
}
```

### 3. 캐싱 전략

```typescript
// lib/cache.ts
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class SimpleCache {
  private cache = new Map<string, CacheItem<any>>();

  set<T>(key: string, data: T, ttl: number = 3600000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }
}

export const cache = new SimpleCache();

// 캐시를 사용한 데이터 페칭
export async function getCachedPosts() {
  const cacheKey = 'notion-posts';
  const cached = cache.get(cacheKey);
  
  if (cached) {
    return cached;
  }

  const response = await notion.databases.query({
    database_id: process.env.NOTION_DATABASE_ID!,
  });

  cache.set(cacheKey, response, 1800000); // 30분 캐시
  return response;
}
```

### 4. 실시간 업데이트 (Webhook)

```typescript
// pages/api/notion/webhook.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import crypto from 'crypto';
import { cache } from '../../../lib/cache';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Webhook 서명 검증
  const signature = req.headers['notion-webhook-signature'] as string;
  const timestamp = req.headers['notion-webhook-timestamp'] as string;
  
  if (!verifyWebhookSignature(req.body, signature, timestamp)) {
    return res.status(401).json({ message: 'Invalid signature' });
  }

  // 캐시 무효화
  cache.clear();

  // 필요시 추가 처리 (예: 정적 페이지 재생성)
  if (process.env.NODE_ENV === 'production') {
    await res.revalidate('/blog');
  }

  res.status(200).json({ message: 'Webhook processed' });
}

function verifyWebhookSignature(
  body: any, 
  signature: string, 
  timestamp: string
): boolean {
  const secret = process.env.NOTION_WEBHOOK_SECRET!;
  const payload = `${timestamp}.${JSON.stringify(body)}`;
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return signature === `v1=${expectedSignature}`;
}
```

## 배포 및 최적화

### 1. Vercel 배포 설정

```javascript
// vercel.json
{
  "env": {
    "NOTION_TOKEN": "@notion-token",
    "NOTION_DATABASE_ID": "@notion-database-id",
    "NOTION_WEBHOOK_SECRET": "@notion-webhook-secret"
  },
  "functions": {
    "pages/api/notion/*.js": {
      "maxDuration": 30
    }
  }
}
```

### 2. Next.js 설정 최적화

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: [
      'www.notion.so',
      'notion.so',
      's3.us-west-2.amazonaws.com',
      'images.unsplash.com',
    ],
  },
  async rewrites() {
    return [
      {
        source: '/api/notion/:path*',
        destination: '/api/notion/:path*',
      },
    ];
  },
  async headers() {
    return [
      {
        source: '/api/notion/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 's-maxage=1800, stale-while-revalidate=3600',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

### 3. 성능 최적화

```typescript
// lib/optimized-notion.ts
import { Client } from '@notionhq/client';
import { LRUCache } from 'lru-cache';

// LRU 캐시 설정
const cache = new LRUCache<string, any>({
  max: 100,
  ttl: 1000 * 60 * 30, // 30분
});

export const notion = new Client({
  auth: process.env.NOTION_TOKEN,
  // 요청 타임아웃 설정
  timeoutMs: 30000,
});

// 배치 요청 함수
export async function batchFetchPages(pageIds: string[]) {
  const results = await Promise.allSettled(
    pageIds.map(async (id) => {
      const cacheKey = `page-${id}`;
      const cached = cache.get(cacheKey);
      
      if (cached) return cached;
      
      const page = await notion.pages.retrieve({ page_id: id });
      cache.set(cacheKey, page);
      return page;
    })
  );

  return results
    .filter(result => result.status === 'fulfilled')
    .map(result => (result as PromiseFulfilledResult<any>).value);
}

// 점진적 데이터 로딩
export async function fetchPostsWithPagination(
  cursor?: string,
  limit: number = 10
) {
  try {
    const response = await notion.databases.query({
      database_id: process.env.NOTION_DATABASE_ID!,
      start_cursor: cursor,
      page_size: limit,
      filter: {
        property: 'Status',
        select: { equals: 'Published' },
      },
    });

    return {
      posts: response.results,
      nextCursor: response.next_cursor,
      hasMore: response.has_more,
    };
  } catch (error) {
    console.error('Fetch posts error:', error);
    throw new Error('Failed to fetch posts');
  }
}
```

### 4. 에러 처리 및 재시도 로직

```typescript
// lib/retry-logic.ts
interface RetryOptions {
  maxRetries: number;
  delay: number;
  backoffFactor: number;
}

export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {
    maxRetries: 3,
    delay: 1000,
    backoffFactor: 2,
  }
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= options.maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === options.maxRetries) {
        throw lastError;
      }
      
      // Rate limit 에러인 경우 더 오래 대기
      const delay = error.message.includes('rate_limited') 
        ? options.delay * 5 
        : options.delay * Math.pow(options.backoffFactor, attempt - 1);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

// 사용 예제
export async function fetchPostsWithRetry() {
  return withRetry(async () => {
    return notion.databases.query({
      database_id: process.env.NOTION_DATABASE_ID!,
    });
  });
}
```

## 트러블슈팅

### 1. 자주 발생하는 에러들

#### CORS 에러
```
Access to fetch at 'https://api.notion.com/v1/...' from origin 'http://localhost:3000' 
has been blocked by CORS policy
```

**해결책:**
- 클라이언트에서 직접 호출하지 말고 서버사이드에서 호출
- Next.js API Routes 또는 별도 백엔드 서버 사용

#### Rate Limiting
```
{
  "object": "error",
  "status": 429,
  "code": "rate_limited",
  "message": "Rate limited"
}
```

**해결책:**
```typescript
// 요청 간 지연 추가
async function rateLimitedRequest(fn: () => Promise<any>) {
  try {
    return await fn();
  } catch (error: any) {
    if (error.code === 'rate_limited') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return await fn();
    }
    throw error;
  }
}
```

#### 권한 에러
```
{
  "object": "error", 
  "status": 403,
  "code": "unauthorized",
  "message": "Insufficient permissions for this endpoint"
}
```

**해결책:**
- Integration 권한 확인
- 데이터베이스에 Integration 연결 확인
- 토큰 값 재확인

### 2. 디버깅 도구

```typescript
// lib/debug.ts
import { Client, LogLevel } from '@notionhq/client';

export const debugNotion = new Client({
  auth: process.env.NOTION_TOKEN,
  logLevel: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.WARN,
});

// 요청/응답 로깅
export function logNotionRequest(
  method: string, 
  endpoint: string, 
  data?: any
) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Notion ${method} ${endpoint}`, data);
  }
}

export function logNotionResponse(
  method: string, 
  endpoint: string, 
  response: any
) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Notion ${method} ${endpoint} response:`, {
      status: response.status,
      resultCount: response.results?.length,
      hasMore: response.has_more,
    });
  }
}
```

### 3. 테스트 코드

```typescript
// __tests__/notion.test.ts
import { jest } from '@jest/globals';
import { notion } from '../lib/notion';

// Mock Notion client
jest.mock('@notionhq/client');

describe('Notion API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('fetchPosts should return formatted posts', async () => {
    const mockResponse = {
      results: [
        {
          id: 'test-id',
          properties: {
            Title: {
              title: [{ text: { content: 'Test Post' } }],
            },
            Status: {
              select: { name: 'Published' },
            },
          },
        },
      ],
    };

    (notion.databases.query as jest.Mock).mockResolvedValue(mockResponse);

    const posts = await fetchPosts();
    
    expect(posts).toHaveLength(1);
    expect(posts[0].title).toBe('Test Post');
  });

  test('should handle API errors gracefully', async () => {
    (notion.databases.query as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    await expect(fetchPosts()).rejects.toThrow('API Error');
  });
});
```

### 4. 환경별 설정

```typescript
// lib/config.ts
interface NotionConfig {
  token: string;
  databaseId: string;
  webhookSecret?: string;
  rateLimit: {
    requestsPerSecond: number;
    burstLimit: number;
  };
  cache: {
    ttl: number;
    maxSize: number;
  };
}

const config: NotionConfig = {
  token: process.env.NOTION_TOKEN!,
  databaseId: process.env.NOTION_DATABASE_ID!,
  webhookSecret: process.env.NOTION_WEBHOOK_SECRET,
  rateLimit: {
    requestsPerSecond: process.env.NODE_ENV === 'production' ? 2 : 3,
    burstLimit: 10,
  },
  cache: {
    ttl: process.env.NODE_ENV === 'production' ? 1800000 : 300000, // 30분 vs 5분
    maxSize: 100,
  },
};

export default config;
```

## 마무리

이 가이드를 통해 React 애플리케이션에서 Notion API를 효과적으로 활용할 수 있습니다. 주요 포인트들을 정리하면:

### 핵심 요점
1. **CORS 제한**: 반드시 서버사이드에서 API 호출
2. **Rate Limiting**: 초당 3개 요청 제한 준수
3. **에러 처리**: 재시도 로직과 적절한 에러 핸들링
4. **캐싱**: 성능 최적화를 위한 적절한 캐싱 전략
5. **타입 안정성**: TypeScript로 타입 안정성 확보

### 추천 아키텍처
```
Frontend (React) → API Routes (Next.js) → Notion API
                ↓
              Cache Layer (Memory/Redis)
                ↓
              Error Handling & Retry Logic
```

### 다음 단계
- Notion 블록을 Markdown으로 변환하는 라이브러리 활용
- 이미지 최적화 및 CDN 연동
- 전문 검색 엔진 (Elasticsearch) 연동
- 실시간 동기화 시스템 구축

이 가이드가 Notion API를 활용한 React 애플리케이션 개발에 도움이 되길 바랍니다!