```markdown
# Ollama Express Middleware Server

이 프로젝트는 Ollama HTTP 서버와 연동하여 클라이언트 요청을 받으면 큐에 저장하고, 처리 상태를 관리하며, 최종 응답을 클라이언트로 전달하는 **지능형 미들웨어**입니다.

---

## 📂 파일 구조

```

ollama-middleware/
├── package.json
├── .env                     # 환경 변수 (OLLAMA\_HOST, OLLAMA\_PORT 등)
├── server.js               # 엔트리 포인트
├── config/
│   └── ollama.js           # Ollama API 설정
├── routes/
│   └── ollama.js           # 라우터 정의
├── controllers/
│   └── ollamaController.js # 요청 처리 로직
├── services/
│   └── ollamaService.js    # Ollama HTTP 호출 추상화
├── middlewares/
│   ├── queue.js            # 요청 큐 관리
│   └── statusTracker.js    # 상태 추적
└── utils/
└── logger.js           # 로깅 유틸

````

---

## 🏗️ 전체 구조 및 흐름

1. **클라이언트 요청**: `/api/ollama/run` POST 요청으로 들어옴
2. **큐 등록**: `middlewares/queue.js` 에서 요청을 큐에 적재하고 `requestId` 발급
3. **처리 상태 초기화**: `middlewares/statusTracker.js` 에서 상태 `pending` 저장
4. **비동기 처리**: `controllers/ollamaController.js`가 `services/ollamaService.js` 호출
5. **Ollama 호출**: HTTP 요청 전송 후 스트리밍 또는 완료 응답 수신
6. **상태 업데이트**: 진행 단계별로 `statusTracker`에 업데이트
7. **클라이언트 폴링/WebSocket**: `/api/ollama/status/:id` 또는 WebSocket으로 상태/결과 전송

---

## 💡 핵심 코드

### package.json
```json
{
  "name": "ollama-middleware",
  "version": "1.0.0",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "axios": "^1.4.0",
    "dotenv": "^16.0.0",
    "express": "^4.18.2",
    "uuid": "^9.0.0",
    "ws": "^8.13.0"
  }
}
````

### config/ollama.js

```js
const dotenv = require('dotenv');
dotenv.config();

module.exports = {
  host: process.env.OLLAMA_HOST || 'localhost',
  port: process.env.OLLAMA_PORT || 11434,
  basePath: '/api',
};
```

### server.js

```js
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const ollamaRouter = require('./routes/ollama');
const queue = require('./middlewares/queue');
const statusTracker = require('./middlewares/statusTracker');
const logger = require('./utils/logger');

const app = express();
app.use(express.json());

// 요청 큐 및 상태 트래커 초기화
queue.init();
statusTracker.init();

// 라우터
app.use('/api/ollama', ollamaRouter);

const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// WebSocket 연결 처리
wss.on('connection', ws => {
  ws.on('message', msg => {
    // 클라이언트가 구독 요청 -> statusTracker에 연결
    const { requestId } = JSON.parse(msg);
    statusTracker.subscribe(requestId, ws);
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => logger.info(`Server listening on ${PORT}`));
```

### middlewares/queue.js

```js
const { EventEmitter } = require('events');
const queue = new EventEmitter();

module.exports = {
  init: () => {
    // worker 프로세스 등록
    queue.on('enqueue', async (job) => {
      queue.emit('process', job);
    });
  },
  add: (job) => queue.emit('enqueue', job),
  onProcess: (cb) => queue.on('process', cb),
};
```

### middlewares/statusTracker.js

```js
const { v4: uuidv4 } = require('uuid');
const trackers = new Map();

module.exports = {
  init: () => {},
  create: () => {
    const id = uuidv4();
    trackers.set(id, { status: 'pending', clients: [] });
    return id;
  },
  update: (id, update) => {
    const track = trackers.get(id);
    Object.assign(track, update);
    track.clients.forEach(ws => ws.send(JSON.stringify({ id, ...track })));
  },
  subscribe: (id, ws) => {
    trackers.get(id).clients.push(ws);
  },
};
```

### services/ollamaService.js

```js
const axios = require('axios');
const config = require('../config/ollama');

async function runModel(requestId, prompt, onProgress) {
  const url = `http://${config.host}:${config.port}${config.basePath}/generate`;
  const response = await axios.post(url, { model: prompt.model, prompt: prompt.text }, { responseType: 'stream' });
  response.data.on('data', chunk => onProgress(chunk.toString()));
  return new Promise(resolve => response.data.on('end', resolve));
}

module.exports = { runModel };
```

### routes/ollama.js

```js
const express = require('express');
const router = express.Router();
const ollamaController = require('../controllers/ollamaController');

// 모델 실행 요청
router.post('/run', ollamaController.handleRun);

// 상태 조회 (폴링 방식)
router.get('/status/:id', ollamaController.handleStatus);

module.exports = router;
```

### controllers/ollamaController.js

```js
const queue = require('../middlewares/queue');
const statusTracker = require('../middlewares/statusTracker');
const ollamaService = require('../services/ollamaService');

exports.handleRun = (req, res) => {
  const { model, text } = req.body;
  const requestId = statusTracker.create();
  queue.add({ requestId, model, text });
  res.json({ requestId });
};

// 큐 처리자 등록
queue.onProcess(async ({ requestId, model, text }) => {
  statusTracker.update(requestId, { status: 'processing' });
  let result = '';
  await ollamaService.runModel(requestId, { model, text }, chunk => {
    result += chunk;
    statusTracker.update(requestId, { status: 'stream', partial: chunk });
  });
  statusTracker.update(requestId, { status: 'completed', result });
});

exports.handleStatus = (req, res) => {
  const { id } = req.params;
  const track = statusTracker.get(id);
  res.json(track);
};
```

---

위 구조를 기반으로 필요에 따라 **에러 핸들링**, **인증/인가**, **결과 캐싱** 등을 추가하여 확장할 수 있습니다.

```
```
