# 🚀 Ollama-Server Swagger API 문서 서비스 구현 계획

## 📋 Ultrathink 분석 결과

### 1. 현재 상황 분석
- **기존 서비스**: Chat, Metadata, Embedding, Rerank, Queue 등 5개 주요 서비스
- **API 엔드포인트**: 총 30+ 개의 REST API 엔드포인트
- **문서화 상태**: 마크다운 기반 API_GUIDE.md 존재, 하지만 인터랙티브하지 않음
- **개발자 경험**: 수동으로 cURL 명령어 복사/붙여넣기 필요

### 2. Swagger 구현 목표
- **자동 API 문서 생성**: 코드에서 자동으로 스키마 추출
- **인터랙티브 테스트**: 브라우저에서 직접 API 호출 가능
- **실시간 업데이트**: 코드 변경 시 문서 자동 갱신
- **개발자 친화적**: 명확한 예시와 스키마 정의

## 🎯 구현 전략

### Phase 1: 기반 설정 (30분)
```javascript
// 1. Swagger 의존성 추가
npm install swagger-jsdoc swagger-ui-express

// 2. 스키마 정의 파일 생성
// - schemas/common.js: 공통 응답 스키마
// - schemas/embedding.js: 임베딩 API 스키마  
// - schemas/rerank.js: 리랭크 API 스키마
// - schemas/queue.js: 큐 관리 스키마
// - schemas/metadata.js: 메타데이터 스키마
```

### Phase 2: 라우터별 JSDoc 주석 추가 (45분)
```javascript
/**
 * @swagger
 * /api/embedding/text:
 *   post:
 *     summary: 단일 텍스트 임베딩
 *     tags: [Embedding]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmbeddingTextRequest'
 *     responses:
 *       200:
 *         description: 임베딩 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/EmbeddingTextResponse'
 */
```

### Phase 3: 동적 스키마 생성기 (60분)
```javascript
// utils/swaggerGenerator.js
class SwaggerGenerator {
  generateFromRoutes() {
    // 라우터 파일들을 스캔하여 자동으로 스키마 생성
  }
  
  generateExamples() {
    // 실제 API 호출 결과를 기반으로 예시 생성
  }
}
```

## 📁 파일 구조 계획

```
ollama-server/
├── docs/
│   ├── swagger/
│   │   ├── swagger.config.js     # Swagger 설정
│   │   ├── schemas/
│   │   │   ├── common.js         # 공통 스키마
│   │   │   ├── embedding.js      # 임베딩 스키마
│   │   │   ├── rerank.js         # 리랭크 스키마
│   │   │   ├── queue.js          # 큐 스키마
│   │   │   └── metadata.js       # 메타데이터 스키마
│   │   └── examples/
│   │       ├── embedding.json    # 임베딩 예시
│   │       ├── rerank.json       # 리랭크 예시
│   │       └── queue.json        # 큐 예시
│   └── api-docs.html             # 커스텀 문서 페이지
├── middlewares/
│   └── swagger.js                # Swagger 미들웨어
└── utils/
    ├── swaggerGenerator.js       # 동적 스키마 생성
    └── apiExampleGenerator.js    # 실시간 예시 생성
```

## 🔧 핵심 구현 컴포넌트

### 1. Swagger 설정 파일
```javascript
// docs/swagger/swagger.config.js
const swaggerConfig = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Ollama Server API',
      version: '2.0.0',
      description: 'AI 서비스를 위한 통합 Ollama API 서버',
      contact: {
        name: 'API Support',
        url: 'http://localhost:7218/api/health'
      }
    },
    servers: [
      {
        url: 'http://localhost:7218',
        description: 'HTTP API Server'
      },
      {
        url: 'http://localhost:7219', 
        description: 'Terminal Server'
      }
    ],
    tags: [
      { name: 'System', description: '시스템 상태 및 헬스체크' },
      { name: 'Embedding', description: '텍스트 임베딩 서비스' },
      { name: 'Rerank', description: '문서 재정렬 서비스' },
      { name: 'Queue', description: '요청 큐 관리' },
      { name: 'Metadata', description: '메타데이터 추출' },
      { name: 'Ollama', description: '기본 Ollama 채팅' }
    ]
  },
  apis: ['./routes/*.js', './controllers/*.js']
};
```

### 2. 동적 예시 생성기
```javascript
// utils/apiExampleGenerator.js
class ApiExampleGenerator {
  constructor(services) {
    this.embeddingService = services.embedding;
    this.rerankService = services.rerank;
    this.queueService = services.queue;
  }

  async generateLiveExamples() {
    // 실제 서비스 호출하여 실시간 예시 생성
    const examples = {
      embedding: await this.generateEmbeddingExample(),
      rerank: await this.generateRerankExample(),
      queue: await this.generateQueueExample()
    };
    return examples;
  }

  async generateEmbeddingExample() {
    try {
      const result = await this.embeddingService.embedText(
        "Sample text for documentation", 
        "nomic-embed-text"
      );
      return {
        request: { text: "Sample text for documentation" },
        response: result
      };
    } catch (error) {
      return { error: "Service unavailable" };
    }
  }
}
```

### 3. 인터랙티브 테스트 환경
```javascript
// middlewares/swagger.js
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');

function setupSwagger(app, services) {
  const specs = swaggerJsdoc(swaggerConfig);
  
  // 실시간 예시 업데이트
  app.get('/api-docs.json', async (req, res) => {
    const liveSpecs = await updateSpecsWithLiveExamples(specs, services);
    res.json(liveSpecs);
  });

  // Swagger UI 설정
  app.use('/api-docs', swaggerUi.serve);
  app.get('/api-docs', swaggerUi.setup(null, {
    swaggerOptions: {
      url: '/api-docs.json',
      requestInterceptor: (req) => {
        // 실제 서버 상태를 반영한 요청 수정
        return req;
      }
    },
    customCssUrl: '/swagger-custom.css',
    customSiteTitle: 'Ollama Server API Documentation'
  }));
}
```

## 🎨 고급 기능 계획

### 1. 실시간 서비스 상태 표시
```javascript
// 각 API 엔드포인트에 실시간 상태 배지 표시
// 🟢 Available  🟡 Degraded  🔴 Unavailable  ⚫ Unknown
```

### 2. 자동 cURL 생성기
```javascript
// Swagger UI에서 "Copy as cURL" 버튼 추가
// 실제 서버 URL과 현재 설정을 반영한 cURL 명령어 생성
```

### 3. 큐 상태 실시간 모니터링
```javascript
// /api-docs/queue-monitor 페이지에서 
// 실시간 큐 상태, 처리 중인 작업, 통계 표시
```

### 4. API 성능 메트릭스
```javascript
// 각 엔드포인트별 평균 응답시간, 성공률 표시
// 마지막 24시간 통계 차트
```

## 📊 구현 순서

### 1단계: 기본 Swagger 설정 (1시간)
- [ ] swagger-jsdoc, swagger-ui-express 설치
- [ ] 기본 swagger.config.js 생성
- [ ] /api-docs 엔드포인트 생성
- [ ] 기본 스키마 정의

### 2단계: 스키마 및 주석 추가 (2시간)
- [ ] 각 서비스별 스키마 파일 생성
- [ ] 라우터 파일에 JSDoc 주석 추가
- [ ] 요청/응답 예시 추가
- [ ] 에러 응답 스키마 정의

### 3단계: 고급 기능 구현 (1.5시간)
- [ ] 실시간 예시 생성기 구현
- [ ] 서비스 상태 연동
- [ ] 커스텀 CSS 스타일링
- [ ] 실시간 큐 모니터링 페이지

### 4단계: 테스트 및 최적화 (30분)
- [ ] 모든 엔드포인트 문서화 확인
- [ ] 예시 정확성 검증
- [ ] 성능 최적화
- [ ] 사용자 경험 개선

## 🎯 예상 결과

### 접근 URL
- **Swagger UI**: `http://localhost:7218/api-docs`
- **JSON 스키마**: `http://localhost:7218/api-docs.json`
- **큐 모니터**: `http://localhost:7218/api-docs/queue-monitor`

### 주요 기능
1. **인터랙티브 API 테스트**: 브라우저에서 직접 API 호출
2. **실시간 상태 표시**: 각 서비스의 현재 상태 확인
3. **자동 예시 생성**: 실제 서비스 응답 기반 예시
4. **성능 메트릭스**: API 응답시간 및 성공률 모니터링
5. **cURL 생성**: 클립보드로 복사 가능한 cURL 명령어

이 계획에 따라 구현하면 개발자들이 ollama-server API를 쉽게 이해하고 테스트할 수 있는 완전한 문서화 시스템을 구축할 수 있습니다.

## 🚀 실행 체크리스트

### Phase 1 시작
- [ ] 의존성 설치: `npm install swagger-jsdoc swagger-ui-express`
- [ ] 디렉토리 구조 생성
- [ ] 기본 설정 파일 작성

### Phase 2 진행  
- [ ] 공통 스키마 정의
- [ ] 각 서비스별 스키마 작성
- [ ] JSDoc 주석 추가

### Phase 3 완성
- [ ] Swagger UI 연동
- [ ] 실시간 예시 생성
- [ ] 테스트 및 검증

### Phase 4 최적화
- [ ] 성능 튜닝
- [ ] UI/UX 개선
- [ ] 문서 완성도 검토
