Ultrathink 모드 + 최신 정보 분석 반영한 **깊이 있는 설계**입니다.

---

## 🎯 목표

텍스트를 API로 보내면 LLM이 **title**, **category**, **keywords** (5개)를 추출해 JSON으로 응답.

---

## 🔍 최신 모델 정보 & 추천 전략

* `gemma3:1b` 이상에서 **128K 토큰 컨텍스트**, 멀티랭귀지 지원 효과적 ([ollama.com][1])
* **Few‑shot prompting**으로 결과 신뢰도↑: 클래스 예시들을 섞어서 제공 ([gptaiflow.com][2])
* **출력 JSON 강제화** + 구조 정의 → 파싱 안정성↑&#x20;

---

## 🛠️ Prompt 설계

### ✅ 1. System Prompt

```text
You are an AI assistant that extracts structured metadata from text.

Output MUST be valid JSON with keys:
- "title": String
- "category": String
- "keywords": [String] (exactly 5 items)

Only output the JSON—nothing else.
```

---

### ✅ 2. Few‑shot Examples

클래스와 키워드 예시를 섞고 현실감 있게 구성:

```text
Example 1:
Text: "New study reveals how sleep improves memory retention in elderly adults."
Result:
{
  "title": "Sleep Boosts Memory in Elderly",
  "category": "Health",
  "keywords": ["sleep", "memory", "elderly", "study", "cognitive health"]
}

Example 2:
Text: "Top 10 programming languages in 2025: trends, salaries, and community support."
Result:
{
  "title": "Top Programming Languages of 2025",
  "category": "Technology",
  "keywords": ["programming","languages","2025","trends","community"]
}
```

---

### 🔧 3. User Input Section

```text
Text: "{user_text}"
Result:
```

---

### ✅ 4. 완전 Prompt 예시

```text
SYSTEM:
You are an AI assistant that extracts structured metadata from text.

Output MUST be valid JSON with keys:
- "title": String
- "category": String
- "keywords": [String] (exactly 5 items)

Only output the JSON—nothing else.

EXAMPLES:
Text: "New study reveals how sleep improves memory retention in elderly adults."
Result:
{
  "title": "Sleep Boosts Memory in Elderly",
  "category": "Health",
  "keywords": ["sleep", "memory", "elderly", "study", "cognitive health"]
}

Text: "Top 10 programming languages in 2025: trends, salaries, and community support."
Result:
{
  "title": "Top Programming Languages of 2025",
  "category": "Technology",
  "keywords": ["programming","languages","2025","trends","community"]
}

END EXAMPLES

Now process the text below and return JSON in the required format.

Text: "{user_text}"
Result:
```

---

## ⚙️ 사용 예시

### HTTP → Ollama API 호출 (Node.js + Axios)

```js
await axios.post('http://localhost:11434/api/generate', {
  model: 'gemma3:1b',
  prompt: fullPrompt,
  temperature: 0,
  stop: ['\n\n']
});
```

### CLI → Terminal 방식

```bash
ollama run gemma3:1b --prompt "$fullPrompt"
```

> 두 방식 모두 정확한 JSON 출력이 핵심이므로 **system prompt + JSON schema + few‑shot 예시 + stop token** 조합이 매우 중요합니다.

---

## ✅ 요약 전략

* **JSON 출력 포맷 엄격 강제** (no extra text)
* **Few‑shot 예시**는 다양한 카테고리로 섞어서 제공 ([Reddit][3], [gptaiflow.com][2])
* **Context 길이**는 Gemma3×128K → 대용량 텍스트도 처리 가능 ([Hugging Face][4])
* **temperature=0**, 구조화된 `stop` 토큰 사용 → 예측 가능한 응답

---

이 구조를 `ollama run` 또는 HTTP API 모두에서 활용할 수 있습니다.
다음은 이 prompt를 Express 서브프로세스 구조나 API 호출 구조에 통합한 예시가 필요하시면 말씀해주세요!

[1]: https://ollama.com/library/gemma3?utm_source=chatgpt.com "gemma3 - Ollama"
[2]: https://gptaiflow.com/blog/google-ai-prompt-engineering-best-practices-guide-2025?utm_source=chatgpt.com "Google AI Prompt Engineering Best Practices: 12 Key Techniques ..."
[3]: https://www.reddit.com/r/ollama/comments/1evmrqz/issues_using_ollamallama31_for_text/?utm_source=chatgpt.com "Issues using Ollama/Llama3.1 for text classification in Python - Reddit"
[4]: https://huggingface.co/blog/gemma3?utm_source=chatgpt.com "Gemma 3: Google's all new multimodal, multilingual, long context ..."
