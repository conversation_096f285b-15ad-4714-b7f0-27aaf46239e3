# Notion API Express Server

Notion API를 위한 Express.js 프록시 서버입니다. CORS 문제를 해결하고 프론트엔드에서 안전하게 Notion API를 사용할 수 있도록 합니다.

## 기능

- ✅ Notion 데이터베이스 조회
- ✅ 페이지 CRUD 작업
- ✅ 블록 관리
- ✅ 검색 기능
- ✅ 웹훅 처리
- ✅ Rate Limiting
- ✅ 에러 처리
- ✅ CORS 지원
- ✅ 헬스체크

## 설치 및 설정

### 1. 의존성 설치

```bash
cd notion-server
npm install
```

### 2. 환경 변수 설정

`.env.example`을 `.env`로 복사하고 설정:

```bash
cp .env.example .env
```

필수 환경 변수:
- `NOTION_TOKEN`: Notion Integration Token
- `NOTION_DATABASE_ID`: 기본 데이터베이스 ID (선택사항)

### 3. 서버 실행

개발 모드:
```bash
npm run dev
```

프로덕션 모드:
```bash
npm start
```

## API 엔드포인트

### 헬스체크

- `GET /health` - 서버 상태 확인
- `GET /health/notion` - Notion API 연결 상태 확인

### 데이터베이스

- `GET /api/notion/posts` - 포스트 목록 조회
- `GET /api/notion/database` - 데이터베이스 정보 조회

### 페이지

- `GET /api/notion/posts/:id` - 특정 페이지 조회
- `POST /api/notion/posts` - 새 페이지 생성
- `PATCH /api/notion/posts/:id` - 페이지 업데이트

### 블록

- `GET /api/notion/posts/:id/blocks` - 페이지 블록 조회
- `POST /api/notion/posts/:id/blocks` - 블록 추가

### 검색

- `GET /api/notion/search` - 전체 검색

### 웹훅

- `POST /api/notion/webhook` - Notion 웹훅 처리

## 요청 예제

### 포스트 목록 조회

```bash
curl "http://localhost:5000/api/notion/posts?status=Published&page_size=10"
```

### 특정 페이지 조회

```bash
curl "http://localhost:5000/api/notion/posts/YOUR_PAGE_ID?include_blocks=true"
```

### 검색

```bash
curl "http://localhost:5000/api/notion/search?query=검색어"
```

## 프론트엔드 연동 예제

### React/JavaScript

```javascript
// 포스트 목록 조회
async function fetchPosts() {
  const response = await fetch('http://localhost:5000/api/notion/posts');
  const data = await response.json();
  return data.posts;
}

// 특정 페이지 조회
async function fetchPost(id) {
  const response = await fetch(`http://localhost:5000/api/notion/posts/${id}?include_blocks=true`);
  const data = await response.json();
  return data;
}

// 검색
async function searchPosts(query) {
  const response = await fetch(`http://localhost:5000/api/notion/search?query=${encodeURIComponent(query)}`);
  const data = await response.json();
  return data.results;
}
```

## 에러 처리

서버는 표준화된 에러 응답을 반환합니다:

```json
{
  "error": "에러 메시지",
  "status": 400,
  "timestamp": "2025-01-13T10:00:00.000Z"
}
```

일반적인 에러 코드:
- `400` - 잘못된 요청
- `401` - 인증 실패
- `403` - 권한 없음
- `404` - 리소스 없음
- `429` - 요청 한도 초과
- `500` - 서버 에러

## 개발 도구

### 로깅

서버는 Morgan을 사용하여 HTTP 요청을 로깅합니다.

### Rate Limiting

기본 설정:
- 15분 동안 IP당 100개 요청
- 환경 변수로 조정 가능

### CORS

기본적으로 `http://localhost:3000`을 허용하며, `ALLOWED_ORIGINS` 환경 변수로 추가 도메인을 설정할 수 있습니다.

## 배포

### Docker

```bash
# 이미지 빌드
docker build -t notion-server .

# 컨테이너 실행
docker run -p 5000:5000 --env-file .env notion-server
```

### PM2

```bash
npm install -g pm2
pm2 start server.js --name notion-server
```

## 보안 고려사항

1. **환경 변수**: 민감한 정보는 환경 변수로 관리
2. **CORS**: 필요한 도메인만 허용
3. **Rate Limiting**: API 남용 방지
4. **헬멧**: 보안 헤더 적용
5. **입력 검증**: 모든 입력 데이터 검증

## 라이센스

MIT
