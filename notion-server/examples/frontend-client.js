// Frontend에서 Notion API 서버 사용 예제

class NotionAPIClient {
  constructor(baseURL = 'http://localhost:5000') {
    this.baseURL = baseURL;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // 헬스체크
  async healthCheck() {
    return this.request('/health');
  }

  async notionHealthCheck() {
    return this.request('/health/notion');
  }

  // 포스트 관련
  async getPosts(params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request(`/api/notion/posts${query ? `?${query}` : ''}`);
  }

  async getPost(id, includeBlocks = false) {
    const query = includeBlocks ? '?include_blocks=true' : '';
    return this.request(`/api/notion/posts/${id}${query}`);
  }

  async createPost(data) {
    return this.request('/api/notion/posts', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updatePost(id, data) {
    return this.request(`/api/notion/posts/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  // 블록 관련
  async getPageBlocks(pageId, params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request(`/api/notion/posts/${pageId}/blocks${query ? `?${query}` : ''}`);
  }

  async addBlocks(pageId, children) {
    return this.request(`/api/notion/posts/${pageId}/blocks`, {
      method: 'POST',
      body: JSON.stringify({ children }),
    });
  }

  // 검색
  async search(query, params = {}) {
    const searchParams = new URLSearchParams({ query, ...params }).toString();
    return this.request(`/api/notion/search?${searchParams}`);
  }

  // 데이터베이스 정보
  async getDatabaseInfo(databaseId = null) {
    const query = databaseId ? `?database_id=${databaseId}` : '';
    return this.request(`/api/notion/database${query}`);
  }
}

// 사용 예제
const notionAPI = new NotionAPIClient();

// React Hook 예제
function useNotionPosts() {
  const [posts, setPosts] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  React.useEffect(() => {
    async function fetchPosts() {
      try {
        setLoading(true);
        const data = await notionAPI.getPosts({ 
          status: 'Published', 
          page_size: 10 
        });
        setPosts(data.posts);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, []);

  return { posts, loading, error };
}

// Vue Composition API 예제
function useNotionPosts() {
  const posts = ref([]);
  const loading = ref(true);
  const error = ref(null);

  const fetchPosts = async () => {
    try {
      loading.value = true;
      const data = await notionAPI.getPosts({ 
        status: 'Published', 
        page_size: 10 
      });
      posts.value = data.posts;
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  onMounted(fetchPosts);

  return { posts, loading, error, fetchPosts };
}

// Vanilla JavaScript 예제
async function loadBlogPosts() {
  try {
    const data = await notionAPI.getPosts({ 
      status: 'Published', 
      page_size: 10 
    });
    
    const postsContainer = document.getElementById('posts');
    postsContainer.innerHTML = data.posts.map(post => `
      <article class="post">
        <h2>${post.title}</h2>
        <p>${post.excerpt}</p>
        <div class="tags">
          ${post.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
        </div>
        <time>${new Date(post.created).toLocaleDateString()}</time>
      </article>
    `).join('');
  } catch (error) {
    console.error('Failed to load posts:', error);
  }
}

// Export for ES modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NotionAPIClient;
}
