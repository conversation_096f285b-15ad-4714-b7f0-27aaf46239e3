// 비동기 함수 에러 처리 래퍼
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// 404 에러 핸들러
function notFoundHandler(req, res, next) {
  const error = new Error(`Route not found - ${req.originalUrl}`);
  error.status = 404;
  next(error);
}

// 전역 에러 핸들러
function errorHandler(err, req, res, next) {
  let error = { ...err };
  error.message = err.message;

  // 로그 출력
  console.error(err);

  // Notion API 에러 처리
  if (err.code === 'unauthorized') {
    error.message = 'Notion API 인증 실패';
    error.status = 401;
  } else if (err.code === 'forbidden') {
    error.message = '접근 권한이 없습니다';
    error.status = 403;
  } else if (err.code === 'object_not_found') {
    error.message = '요청한 리소스를 찾을 수 없습니다';
    error.status = 404;
  } else if (err.code === 'rate_limited') {
    error.message = '요청 한도를 초과했습니다';
    error.status = 429;
  } else if (err.code === 'invalid_request') {
    error.message = '잘못된 요청입니다';
    error.status = 400;
  }

  // 기본 에러 상태 코드
  if (!error.status) {
    error.status = 500;
  }

  // 개발 환경에서는 스택 트레이스 포함
  const response = {
    error: error.message || 'Server Error',
    status: error.status,
    timestamp: new Date().toISOString(),
  };

  if (process.env.NODE_ENV === 'development') {
    response.stack = err.stack;
  }

  res.status(error.status).json(response);
}

module.exports = {
  asyncHandler,
  notFoundHandler,
  errorHandler,
};
