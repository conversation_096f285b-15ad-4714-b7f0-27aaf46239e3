const Joi = require('joi');

// 환경 변수 검증
function validateEnv() {
  const requiredEnvVars = ['NOTION_TOKEN'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
    process.exit(1);
  }

  if (!process.env.NOTION_DATABASE_ID) {
    console.warn('⚠️  NOTION_DATABASE_ID not set. Some features may not work.');
  }

  console.log('✅ Environment variables validated');
}

// 페이지 ID 검증 미들웨어
function validatePageId(req, res, next) {
  const { id } = req.params;
  
  if (!id) {
    return res.status(400).json({ error: 'Page ID is required' });
  }

  // Notion page ID는 32자리 UUID (하이픈 포함 36자리)
  const pageIdRegex = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
  const cleanId = id.replace(/-/g, '');
  
  if (cleanId.length !== 32 || !pageIdRegex.test(id)) {
    return res.status(400).json({ error: 'Invalid page ID format' });
  }

  next();
}

// 데이터베이스 쿼리 검증
const databaseQuerySchema = Joi.object({
  status: Joi.string().optional(),
  page_size: Joi.number().integer().min(1).max(100).optional(),
  start_cursor: Joi.string().optional(),
  sort_by: Joi.string().optional(),
  sort_direction: Joi.string().valid('ascending', 'descending').optional(),
});

function validateDatabaseQuery(req, res, next) {
  const { error } = databaseQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({ 
      error: 'Invalid query parameters',
      details: error.details.map(detail => detail.message),
    });
  }

  next();
}

// 페이지 생성 데이터 검증
const createPageSchema = Joi.object({
  properties: Joi.object().required(),
  children: Joi.array().optional(),
});

function validateCreatePage(req, res, next) {
  const { error } = createPageSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({ 
      error: 'Invalid page data',
      details: error.details.map(detail => detail.message),
    });
  }

  next();
}

// 페이지 업데이트 데이터 검증
const updatePageSchema = Joi.object({
  properties: Joi.object().required(),
});

function validateUpdatePage(req, res, next) {
  const { error } = updatePageSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({ 
      error: 'Invalid update data',
      details: error.details.map(detail => detail.message),
    });
  }

  next();
}

module.exports = {
  validateEnv,
  validatePageId,
  validateDatabaseQuery,
  validateCreatePage,
  validateUpdatePage,
};
