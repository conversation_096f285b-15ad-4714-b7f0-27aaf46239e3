{"name": "notion-server", "version": "1.0.0", "description": "Express server for Notion API proxy", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["notion", "api", "express", "proxy"], "author": "", "license": "MIT", "dependencies": {"@notionhq/client": "^2.2.15", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}