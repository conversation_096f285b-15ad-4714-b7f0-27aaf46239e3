const express = require('express');
const router = express.Router();

router.get('/', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version,
    environment: process.env.NODE_ENV || 'development',
  });
});

router.get('/notion', async (req, res) => {
  try {
    const { Client } = require('@notionhq/client');
    
    if (!process.env.NOTION_TOKEN) {
      return res.status(500).json({
        status: 'error',
        message: 'Notion token not configured',
      });
    }

    const notion = new Client({
      auth: process.env.NOTION_TOKEN,
    });

    // 간단한 데이터베이스 조회로 연결 테스트
    if (process.env.NOTION_DATABASE_ID) {
      await notion.databases.retrieve({ 
        database_id: process.env.NOTION_DATABASE_ID 
      });
    }

    res.json({
      status: 'healthy',
      notion: 'connected',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      notion: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

module.exports = router;
