const express = require('express');
const router = express.Router();
const NotionService = require('../services/notionService');
const { validateDatabaseQuery, validatePageId } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const notionService = new NotionService();

// 데이터베이스 조회
router.get('/posts', asyncHandler(async (req, res) => {
  const { 
    status = 'Published', 
    page_size = 10, 
    start_cursor,
    sort_by = 'Created',
    sort_direction = 'descending'
  } = req.query;

  const filter = status ? {
    property: 'Status',
    select: {
      equals: status,
    },
  } : {};

  const sorts = [{
    property: sort_by,
    direction: sort_direction,
  }];

  const response = await notionService.queryDatabase(
    filter, 
    sorts, 
    parseInt(page_size), 
    start_cursor
  );

  const posts = response.results.map(page => notionService.formatPostData(page));

  res.json({
    posts,
    nextCursor: response.nextCursor,
    hasMore: response.hasMore,
  });
}));

// 특정 페이지 조회
router.get('/posts/:id', validatePageId, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { include_blocks = 'false' } = req.query;

  const page = await notionService.getPage(id);
  const formattedPage = notionService.formatPostData(page);

  if (include_blocks === 'true') {
    const blocks = await notionService.getPageBlocks(id);
    formattedPage.blocks = blocks.results;
  }

  res.json(formattedPage);
}));

// 페이지 블록 조회
router.get('/posts/:id/blocks', validatePageId, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { page_size = 100, start_cursor } = req.query;

  const response = await notionService.getPageBlocks(
    id, 
    parseInt(page_size), 
    start_cursor
  );

  res.json({
    blocks: response.results,
    nextCursor: response.nextCursor,
    hasMore: response.hasMore,
  });
}));

// 검색
router.get('/search', asyncHandler(async (req, res) => {
  const { 
    query, 
    filter_type,
    filter_value,
    page_size = 10, 
    start_cursor 
  } = req.query;

  if (!query) {
    return res.status(400).json({ error: 'Search query is required' });
  }

  const filter = {};
  if (filter_type && filter_value) {
    filter.property = filter_type;
    filter.value = filter_value;
  }

  const response = await notionService.search(
    query, 
    filter, 
    {}, 
    parseInt(page_size), 
    start_cursor
  );

  const results = response.results.map(item => {
    if (item.object === 'page') {
      return notionService.formatPostData(item);
    }
    return item;
  });

  res.json({
    results,
    nextCursor: response.nextCursor,
    hasMore: response.hasMore,
  });
}));

// 데이터베이스 정보 조회
router.get('/database', asyncHandler(async (req, res) => {
  const { database_id } = req.query;
  
  const database = await notionService.getDatabase(database_id);
  
  res.json({
    id: database.id,
    title: database.title[0]?.text?.content || '',
    description: database.description[0]?.text?.content || '',
    properties: database.properties,
    url: database.url,
    created: database.created_time,
    lastEdited: database.last_edited_time,
  });
}));

// 페이지 생성
router.post('/posts', asyncHandler(async (req, res) => {
  const { properties, children = [] } = req.body;

  if (!properties) {
    return res.status(400).json({ error: 'Properties are required' });
  }

  const parent = {
    database_id: process.env.NOTION_DATABASE_ID,
  };

  const page = await notionService.createPage(parent, properties, children);
  const formattedPage = notionService.formatPostData(page);

  res.status(201).json(formattedPage);
}));

// 페이지 업데이트
router.patch('/posts/:id', validatePageId, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { properties } = req.body;

  if (!properties) {
    return res.status(400).json({ error: 'Properties are required' });
  }

  const page = await notionService.updatePage(id, properties);
  const formattedPage = notionService.formatPostData(page);

  res.json(formattedPage);
}));

// 블록 추가
router.post('/posts/:id/blocks', validatePageId, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { children } = req.body;

  if (!children || !Array.isArray(children)) {
    return res.status(400).json({ error: 'Children blocks are required' });
  }

  const response = await notionService.appendBlocks(id, children);

  res.status(201).json({
    results: response.results,
  });
}));

// 웹훅 처리
router.post('/webhook', asyncHandler(async (req, res) => {
  const signature = req.headers['notion-webhook-signature'];
  const timestamp = req.headers['notion-webhook-timestamp'];
  
  // TODO: 웹훅 서명 검증 로직 추가
  console.log('Webhook received:', {
    signature,
    timestamp,
    body: req.body,
  });

  res.status(200).json({ message: 'Webhook processed successfully' });
}));

module.exports = router;
