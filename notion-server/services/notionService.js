const { Client } = require('@notionhq/client');

class NotionService {
  constructor() {
    this.notion = new Client({
      auth: process.env.NOTION_TOKEN,
    });
    this.databaseId = process.env.NOTION_DATABASE_ID;
  }

  // 데이터베이스 쿼리
  async queryDatabase(filter = {}, sorts = [], pageSize = 100, startCursor = null) {
    try {
      const response = await this.notion.databases.query({
        database_id: this.databaseId,
        filter,
        sorts,
        page_size: pageSize,
        start_cursor: startCursor,
      });

      return {
        results: response.results,
        nextCursor: response.next_cursor,
        hasMore: response.has_more,
      };
    } catch (error) {
      console.error('Database query error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 페이지 조회
  async getPage(pageId) {
    try {
      const page = await this.notion.pages.retrieve({ page_id: pageId });
      return page;
    } catch (error) {
      console.error('Get page error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 페이지 블록 조회
  async getPageBlocks(pageId, pageSize = 100, startCursor = null) {
    try {
      const response = await this.notion.blocks.children.list({
        block_id: pageId,
        page_size: pageSize,
        start_cursor: startCursor,
      });

      return {
        results: response.results,
        nextCursor: response.next_cursor,
        hasMore: response.has_more,
      };
    } catch (error) {
      console.error('Get page blocks error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 페이지 생성
  async createPage(parent, properties, children = []) {
    try {
      const response = await this.notion.pages.create({
        parent,
        properties,
        children,
      });
      return response;
    } catch (error) {
      console.error('Create page error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 페이지 업데이트
  async updatePage(pageId, properties) {
    try {
      const response = await this.notion.pages.update({
        page_id: pageId,
        properties,
      });
      return response;
    } catch (error) {
      console.error('Update page error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 검색
  async search(query, filter = {}, sort = {}, pageSize = 100, startCursor = null) {
    try {
      const response = await this.notion.search({
        query,
        filter,
        sort,
        page_size: pageSize,
        start_cursor: startCursor,
      });

      return {
        results: response.results,
        nextCursor: response.next_cursor,
        hasMore: response.has_more,
      };
    } catch (error) {
      console.error('Search error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 데이터베이스 정보 조회
  async getDatabase(databaseId = null) {
    try {
      const id = databaseId || this.databaseId;
      const database = await this.notion.databases.retrieve({ database_id: id });
      return database;
    } catch (error) {
      console.error('Get database error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 블록 추가
  async appendBlocks(blockId, children) {
    try {
      const response = await this.notion.blocks.children.append({
        block_id: blockId,
        children,
      });
      return response;
    } catch (error) {
      console.error('Append blocks error:', error);
      throw this.handleNotionError(error);
    }
  }

  // 에러 처리
  handleNotionError(error) {
    if (error.code === 'unauthorized') {
      return new Error('Notion API 인증 실패. 토큰을 확인하세요.');
    }
    if (error.code === 'forbidden') {
      return new Error('접근 권한이 없습니다. Integration 권한을 확인하세요.');
    }
    if (error.code === 'object_not_found') {
      return new Error('요청한 페이지 또는 데이터베이스를 찾을 수 없습니다.');
    }
    if (error.code === 'rate_limited') {
      return new Error('요청 한도를 초과했습니다. 잠시 후 다시 시도하세요.');
    }
    if (error.code === 'invalid_request') {
      return new Error('잘못된 요청입니다. 요청 형식을 확인하세요.');
    }
    return error;
  }

  // 포맷된 포스트 데이터 반환
  formatPostData(page) {
    return {
      id: page.id,
      title: page.properties.Title?.title?.[0]?.text?.content || '',
      slug: page.properties.Slug?.rich_text?.[0]?.text?.content || '',
      excerpt: page.properties.Excerpt?.rich_text?.[0]?.text?.content || '',
      status: page.properties.Status?.select?.name || '',
      created: page.properties.Created?.created_time || page.created_time,
      lastEdited: page.last_edited_time,
      tags: page.properties.Tags?.multi_select?.map(tag => tag.name) || [],
      coverImage: page.cover?.external?.url || page.cover?.file?.url || null,
      url: page.url,
    };
  }
}

module.exports = NotionService;
