#!/bin/bash

# Notion Server 시작 스크립트

echo "🚀 Starting Notion API Server..."

# 환경 변수 확인
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# 의존성 설치
if [ ! -d node_modules ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# 개발 모드 또는 프로덕션 모드 선택
if [ "$NODE_ENV" = "production" ]; then
    echo "🌟 Starting in production mode..."
    npm start
else
    echo "🔧 Starting in development mode..."
    npm run dev
fi
