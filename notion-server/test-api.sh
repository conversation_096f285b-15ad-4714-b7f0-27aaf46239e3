#!/bin/bash

# Notion API 테스트 스크립트

BASE_URL="http://localhost:5000"

echo "🧪 Testing Notion API Server..."

# 헬스체크 테스트
echo -e "\n1. Health Check:"
curl -s "$BASE_URL/health" | jq '.'

echo -e "\n2. Notion Health Check:"
curl -s "$BASE_URL/health/notion" | jq '.'

# 포스트 목록 조회 테스트 (Notion 토큰이 설정된 경우에만 작동)
echo -e "\n3. Posts List (requires valid Notion token):"
curl -s "$BASE_URL/api/notion/posts?page_size=5" | jq '.'

# 데이터베이스 정보 조회 테스트
echo -e "\n4. Database Info (requires valid Notion token and database ID):"
curl -s "$BASE_URL/api/notion/database" | jq '.'

# 검색 테스트
echo -e "\n5. Search (requires valid Notion token):"
curl -s "$BASE_URL/api/notion/search?query=test" | jq '.'

echo -e "\n✅ API tests completed!"
