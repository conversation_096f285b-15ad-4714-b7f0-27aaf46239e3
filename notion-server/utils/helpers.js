const crypto = require('crypto');

// 웹훅 서명 검증
function verifyWebhookSignature(body, signature, timestamp) {
  const secret = process.env.NOTION_WEBHOOK_SECRET;
  
  if (!secret) {
    console.warn('Webhook secret not configured');
    return false;
  }

  const payload = `${timestamp}.${JSON.stringify(body)}`;
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return signature === `v1=${expectedSignature}`;
}

// 재시도 로직
async function withRetry(fn, options = {}) {
  const {
    maxRetries = 3,
    delay = 1000,
    backoffFactor = 2,
  } = options;

  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Rate limit 에러인 경우 더 오래 대기
      const waitTime = error.code === 'rate_limited' 
        ? delay * 5 
        : delay * Math.pow(backoffFactor, attempt - 1);
      
      console.log(`Retry attempt ${attempt} failed, waiting ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
}

// UUID 생성
function generateUUID() {
  return crypto.randomUUID();
}

// 페이지 ID 정규화 (하이픈 제거/추가)
function normalizePageId(pageId) {
  const cleanId = pageId.replace(/-/g, '');
  
  if (cleanId.length !== 32) {
    throw new Error('Invalid page ID length');
  }
  
  return `${cleanId.slice(0, 8)}-${cleanId.slice(8, 12)}-${cleanId.slice(12, 16)}-${cleanId.slice(16, 20)}-${cleanId.slice(20)}`;
}

// 응답 데이터 포맷팅
function formatResponse(data, metadata = {}) {
  return {
    success: true,
    data,
    ...metadata,
    timestamp: new Date().toISOString(),
  };
}

// 에러 응답 포맷팅
function formatErrorResponse(message, code = null, details = null) {
  return {
    success: false,
    error: message,
    code,
    details,
    timestamp: new Date().toISOString(),
  };
}

module.exports = {
  verifyWebhookSignature,
  withRetry,
  generateUUID,
  normalizePageId,
  formatResponse,
  formatErrorResponse,
};
