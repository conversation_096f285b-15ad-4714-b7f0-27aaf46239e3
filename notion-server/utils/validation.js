function validateEnv() {
  const requiredEnvVars = ['NOTION_TOKEN'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
    process.exit(1);
  }

  if (!process.env.NOTION_DATABASE_ID) {
    console.warn('⚠️  NOTION_DATABASE_ID not set. Some features may not work.');
  }

  console.log('✅ Environment variables validated');
}

module.exports = {
  validateEnv,
};
