# Ollama Server API 가이드

완전한 Ollama 서버 API 문서입니다. 이 서버는 Ollama와의 상호작용을 위한 다양한 서비스를 제공합니다.

## 🎯 서비스 개요

| 서비스 | 포트 | 설명 |
|--------|------|------|
| HTTP API | 7218 | RESTful API 서버 |
| Terminal | 7219 | 터미널 기반 CLI 서버 |

### 제공 서비스
- **Ollama Chat**: 텍스트 생성 및 대화
- **Metadata**: 텍스트에서 메타데이터 추출
- **Embedding**: 텍스트를 벡터로 변환
- **Rerank**: 문서 관련성 재정렬
- **Queue**: 요청 큐 관리 및 모니터링

---

## 🔧 환경 설정

```bash
# .env 파일
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
OLLAMA_MODEL=gemma3:1b

# 새로운 모델 설정
DEFAULT_EMBEDDING_MODEL=nomic-embed-text
DEFAULT_RERANK_MODEL=bge-reranker-base

# 큐 설정
QUEUE_SIZE=100
QUEUE_PROCESS_INTERVAL=1000

# 서버 포트
PORT=11501
TERMINAL_PORT=11502
```

---

## 📊 시스템 상태

### 전체 헬스 체크
```bash
GET /api/health
```

**응답 예시:**
```json
{
  "status": "healthy",
  "services": {
    "ollama": {
      "status": "healthy",
      "timestamp": "2025-01-13T12:00:00Z"
    },
    "embedding": {
      "service": "embedding",
      "ollama": {
        "connected": true,
        "host": "localhost",
        "port": "11434"
      },
      "defaultModel": "nomic-embed-text"
    },
    "rerank": {
      "service": "rerank",
      "ollama": {
        "connected": true
      },
      "defaultModel": "bge-reranker-base"
    },
    "queue": {
      "queueSize": 0,
      "processing": true,
      "stats": {
        "totalProcessed": 150,
        "totalFailed": 2
      }
    }
  }
}
```

---

## 🔄 큐 관리 API

### 큐 상태 조회
```bash
GET /api/queue/status
```

### 큐 통계
```bash
GET /api/queue/stats
```

**응답 예시:**
```json
{
  "success": true,
  "data": {
    "queue": {
      "current": 5,
      "max": 100,
      "utilization": "5.00%"
    },
    "processing": {
      "active": true,
      "currentJob": {
        "id": "uuid-123",
        "type": "embedding"
      }
    },
    "performance": {
      "totalProcessed": 1000,
      "totalFailed": 5,
      "successRate": "99.50%",
      "averageProcessTime": "1250ms"
    }
  }
}
```

### 대기 작업 목록
```bash
GET /api/queue/upcoming?limit=10
```

### 작업 취소
```bash
DELETE /api/queue/job/{jobId}
```

### 큐 초기화
```bash
DELETE /api/queue/clear
Content-Type: application/json

{
  "confirm": "yes"
}
```

### 프로세서 제어
```bash
POST /api/queue/processor
Content-Type: application/json

{
  "action": "start"  // or "stop"
}
```

---

## 🔤 Embedding API

### 단일 텍스트 임베딩
```bash
POST /api/embedding/text
Content-Type: application/json

{
  "text": "텍스트 내용",
  "model": "nomic-embed-text"  // 옵션
}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "text": "텍스트 내용",
    "model": "nomic-embed-text",
    "embedding": [0.1, 0.2, -0.3, ...],
    "dimensions": 768,
    "timestamp": "2025-01-13T12:00:00Z"
  }
}
```

### 배치 임베딩
```bash
POST /api/embedding/batch
Content-Type: application/json

{
  "texts": [
    "첫 번째 문서",
    "두 번째 문서",
    "세 번째 문서"
  ],
  "model": "nomic-embed-text"
}
```

### 문서 임베딩 (청크 분할)
```bash
POST /api/embedding/document
Content-Type: application/json

{
  "text": "매우 긴 문서 내용...",
  "chunkSize": 512,
  "overlap": 50,
  "model": "nomic-embed-text"
}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "originalText": "원본 텍스트",
    "totalChunks": 3,
    "model": "nomic-embed-text",
    "chunks": [
      {
        "text": "첫 번째 청크",
        "embedding": [...],
        "chunkIndex": 0,
        "chunkStart": 0,
        "chunkEnd": 512
      }
    ]
  }
}
```

### 스트리밍 배치 임베딩
```bash
POST /api/embedding/stream
Content-Type: application/json

{
  "texts": ["텍스트1", "텍스트2"],
  "model": "nomic-embed-text"
}
```

**Server-Sent Events 응답:**
```
data: {"type": "result", "index": 0, "result": {...}}
data: {"type": "result", "index": 1, "result": {...}}
data: {"type": "complete", "totalProcessed": 2}
```

### 사용 가능한 모델
```bash
GET /api/embedding/models
```

### 서비스 상태
```bash
GET /api/embedding/status
GET /api/embedding/health
```

---

## 🎯 Rerank API

### 문서 리랭킹
```bash
POST /api/rerank
Content-Type: application/json

{
  "query": "검색 쿼리",
  "documents": [
    "첫 번째 문서",
    "두 번째 문서",
    {"text": "객체 형태 문서"}
  ],
  "model": "bge-reranker-base",
  "topK": 5,
  "returnDocuments": true,
  "threshold": 0.1
}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "query": "검색 쿼리",
    "model": "bge-reranker-base",
    "totalDocuments": 3,
    "returnedDocuments": 2,
    "results": [
      {
        "index": 0,
        "relevanceScore": 0.95,
        "reason": "Highly relevant because...",
        "document": "첫 번째 문서"
      },
      {
        "index": 2,
        "relevanceScore": 0.82,
        "reason": "Relevant because...",
        "document": {"text": "객체 형태 문서"}
      }
    ]
  }
}
```

### 배치 리랭킹
```bash
POST /api/rerank/batch
Content-Type: application/json

{
  "queries": ["쿼리1", "쿼리2"],
  "documents": ["문서1", "문서2", "문서3"],
  "model": "bge-reranker-base",
  "topK": 2
}
```

### 스트리밍 리랭킹
```bash
POST /api/rerank/stream
Content-Type: application/json

{
  "queries": ["쿼리1", "쿼리2"],
  "documents": ["문서1", "문서2"]
}
```

### 점수만 계산
```bash
POST /api/rerank/score
Content-Type: application/json

{
  "query": "검색 쿼리",
  "documents": ["문서1", "문서2"],
  "model": "bge-reranker-base"
}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "query": "검색 쿼리",
    "model": "bge-reranker-base",
    "scores": [
      {
        "index": 0,
        "relevanceScore": 0.95,
        "reason": "Highly relevant"
      },
      {
        "index": 1,
        "relevanceScore": 0.23,
        "reason": "Less relevant"
      }
    ]
  }
}
```

### 모델 및 상태
```bash
GET /api/rerank/models
GET /api/rerank/status
GET /api/rerank/health
```

---

## 🤖 기존 Ollama API

### 스트리밍 채팅
```bash
POST /api/ollama/stream
Content-Type: application/json

{
  "model": "gemma3:1b",
  "prompt": "안녕하세요"
}
```

### 일반 채팅
```bash
POST /api/ollama/chat
```

### 요청 상태 확인
```bash
GET /api/ollama/status/{requestId}
```

---

## 📝 메타데이터 추출 API

### 자동 방식 (HTTP/Terminal 폴백)
```bash
POST /api/metadata/extract/auto
Content-Type: application/json

{
  "text": "추출할 텍스트",
  "textType": "academic",
  "model": "gemma3:1b"
}
```

### HTTP 방식
```bash
POST /api/metadata/extract/http
```

### Terminal 방식
```bash
POST /api/metadata/extract/terminal
```

### 스트리밍 추출
```bash
POST /api/metadata/extract/stream
```

### 배치 추출
```bash
POST /api/metadata/extract/batch
```

---

## 🚀 사용 예시

### 1. 텍스트 임베딩 후 유사도 검색
```bash
# 1. 문서들을 임베딩
curl -X POST http://localhost:7218/api/embedding/batch \
  -H "Content-Type: application/json" \
  -d '{
    "texts": [
      "인공지능은 컴퓨터가 인간의 지능을 모방하는 기술입니다",
      "머신러닝은 데이터로부터 패턴을 학습하는 방법입니다",
      "딥러닝은 신경망을 이용한 기계학습 방법입니다"
    ]
  }'

# 2. 쿼리 임베딩
curl -X POST http://localhost:7218/api/embedding/text \
  -H "Content-Type: application/json" \
  -d '{"text": "AI와 머신러닝의 차이점"}'

# 3. 유사도 계산은 클라이언트에서 수행
```

### 2. 검색 결과 리랭킹
```bash
curl -X POST http://localhost:7218/api/rerank \
  -H "Content-Type: application/json" \
  -d '{
    "query": "머신러닝 알고리즘",
    "documents": [
      "선형 회귀는 기본적인 머신러닝 알고리즘입니다",
      "오늘 날씨가 좋습니다",
      "딥러닝은 여러 레이어를 가진 신경망입니다",
      "Python은 프로그래밍 언어입니다"
    ],
    "topK": 2
  }'
```

### 3. 큐 상태 모니터링
```bash
# 큐 상태 확인
curl http://localhost:7218/api/queue/stats

# 여러 작업을 동시에 제출
for i in {1..10}; do
  curl -X POST http://localhost:7218/api/embedding/text \
    -H "Content-Type: application/json" \
    -d "{\"text\": \"Test document $i\"}" &
done

# 큐 상태 다시 확인
curl http://localhost:7218/api/queue/status
```

---

## 🔧 개발자 참고사항

### 에러 응답 형식
```json
{
  "error": "에러 메시지",
  "code": "ERROR_CODE",
  "timestamp": "2025-01-13T12:00:00Z"
}
```

### 일반적인 에러 코드
- `MISSING_TEXT`: 필수 텍스트 누락
- `MISSING_QUERY`: 필수 쿼리 누락
- `MISSING_DOCUMENTS`: 필수 문서 배열 누락
- `BATCH_TOO_LARGE`: 배치 크기 초과
- `QUEUE_FULL`: 큐 포화 상태
- `OLLAMA_CONNECTION_ERROR`: Ollama 서버 연결 실패

### 제한사항
- 배치 임베딩: 최대 50개 텍스트
- 리랭킹: 최대 100개 문서, 10개 쿼리
- 큐 크기: 기본 100개 (설정 가능)
- 요청 타임아웃: 5분

### 우선순위 시스템
1. 메타데이터 추출 (우선순위 1)
2. 채팅/생성 (우선순위 2)  
3. 임베딩/리랭킹 (우선순위 3)
4. 문서 처리 (우선순위 4)
5. 기타 (우선순위 5)

---

## 🧪 테스트

```bash
# 전체 서비스 테스트 실행
./test-new-services.sh

# 특정 기능만 테스트
./test-metadata.sh
```

모든 API는 JSON 형식으로 요청/응답하며, CORS가 활성화되어 있어 웹 브라우저에서도 사용 가능합니다.
