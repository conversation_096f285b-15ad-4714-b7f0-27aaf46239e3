# Ollama Express 서버 Dockerfile
# 호스트의 Ollama(11434)에 연결하는 Express 서비스들
# - HTTP API 서버 (11501)
# - 터미널 서버 (11502)

FROM node:20-alpine

# 필요한 시스템 패키지 설치 (Ollama CLI 제외)
RUN apk add --no-cache \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# 비루트 사용자 생성
RUN addgroup -g 1001 -S nodejs && \
    adduser -S ollama -u 1001

# 작업 디렉토리 설정
WORKDIR /app

# 환경 변수 설정
ENV NODE_ENV=development
ENV PORT=11501
ENV TERMINAL_PORT=11502
ENV LOG_LEVEL=info
ENV OLLAMA_HOST=host.docker.internal:11434

# 패키지 의존성 복사 및 설치 (개발 의존성 포함)
COPY package*.json ./
RUN npm ci && npm cache clean --force

# 소스 코드 복사
COPY --chown=ollama:nodejs . .

# 로그 디렉토리 생성
RUN mkdir -p logs && chown ollama:nodejs logs

# 포트 노출
EXPOSE 11501 11502

# 헬스체크 - 호스트 Ollama 연결 확인
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:11501/api/metadata/health || exit 1

# 비루트 사용자로 실행
USER ollama

# 양쪽 서버 시작 (개발 모드 - nodemon 사용)
CMD ["npm", "run", "dev:both"]
