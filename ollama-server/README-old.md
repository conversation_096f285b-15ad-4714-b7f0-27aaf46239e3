# Ollama Middleware Server

Ollama HTTP 서버와 연동하여 클라이언트 요청을 받으면 큐에 저장하고, 처리 상태를 관리하며, 최종 응답을 클라이언트로 전달하는 지능형 미들웨어입니다.

## 🚀 특징

- **비동기 큐 처리**: 요청을 큐에 저장하여 순차적으로 처리
- **실시간 상태 추적**: WebSocket을 통한 실시간 진행 상황 모니터링
- **스트리밍 지원**: Ollama의 스트리밍 응답을 실시간으로 전달
- **에러 처리**: 강력한 에러 처리 및 복구 메커니즘
- **확장 가능**: 모듈화된 구조로 쉬운 확장

## 📦 설치

```bash
# 의존성 설치
npm install

# 환경 변수 설정
cp .env.example .env
```

## 🔧 환경 변수

```env
# Ollama 서버 설정
OLLAMA_HOST=localhost
OLLAMA_PORT=11434

# 미들웨어 서버 포트
PORT=3000

# 로그 레벨
LOG_LEVEL=info
```

## 🏃‍♂️ 실행

```bash
# 프로덕션 모드
npm start

# 개발 모드 (nodemon)
npm run dev
```

## 📡 API 엔드포인트

### 1. 모델 실행 요청
```http
POST /api/ollama/run
Content-Type: application/json

{
  "model": "gemma3:1b",
  "text": "Hello, how are you?"
}

Response:
{
  "requestId": "uuid-here",
  "status": "queued",
  "message": "Request has been queued for processing"
}
```

### 2. 상태 조회
```http
GET /api/ollama/status/{requestId}

Response:
{
  "status": "completed", // pending, processing, streaming, completed, failed
  "result": "Generated text here",
  "createdAt": 1625097600000,
  "completedAt": 1625097605000
}
```

### 3. 모델 목록 조회
```http
GET /api/ollama/models

Response:
{
  "models": [
    {
      "name": "gemma3:1b",
      "size": "1.2GB"
    }
  ]
}
```

### 4. 헬스 체크
```http
GET /api/ollama/health

Response:
{
  "status": "healthy",
  "timestamp": "2023-07-01T12:00:00.000Z"
}
```

### 5. 요청 목록 (관리용)
```http
GET /api/ollama/list

Response:
{
  "total": 5,
  "trackers": [...]
}
```

## 🔌 WebSocket 연결

WebSocket을 통해 실시간 상태 업데이트를 받을 수 있습니다.

```javascript
const ws = new WebSocket('ws://localhost:3000/ws');

ws.onopen = () => {
  // 특정 요청 구독
  ws.send(JSON.stringify({
    type: 'subscribe',
    requestId: 'your-request-id'
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Status update:', data);
};
```

## 📁 프로젝트 구조

```
ollama-server/
├── package.json
├── .env
├── server.js               # 엔트리 포인트
├── config/
│   └── ollama.js          # Ollama API 설정
├── routes/
│   └── ollama.js          # API 라우터
├── controllers/
│   └── ollamaController.js # 요청 처리 로직
├── services/
│   └── ollamaService.js   # Ollama HTTP 호출
├── middlewares/
│   ├── queue.js           # 요청 큐 관리
│   └── statusTracker.js   # 상태 추적
└── utils/
    └── logger.js          # 로깅 유틸
```

## 🔄 처리 흐름

1. **클라이언트 요청**: `/api/ollama/run` POST 요청
2. **큐 등록**: 요청을 큐에 적재하고 `requestId` 발급
3. **상태 초기화**: 상태를 `pending`으로 설정
4. **비동기 처리**: Ollama API 호출 및 스트리밍 처리
5. **상태 업데이트**: 진행 단계별로 상태 업데이트
6. **결과 전달**: WebSocket 또는 폴링으로 결과 전송

## 🛠️ 개발

### 로그 확인
```bash
# 모든 로그 보기
LOG_LEVEL=debug npm run dev

# 에러만 보기
LOG_LEVEL=error npm start
```

### 테스트
```bash
# API 테스트
curl -X POST http://localhost:3000/api/ollama/run \
  -H "Content-Type: application/json" \
  -d '{"model": "gemma3:1b", "text": "Hello world"}'

# 상태 확인
curl http://localhost:3000/api/ollama/status/{requestId}
```

## 📝 라이센스

MIT
