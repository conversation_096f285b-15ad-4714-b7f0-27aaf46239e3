# Ollama Middleware Server

Ollama와 연동하여 클라이언트 요청을 받으면 큐에 저장하고, 처리 상태를 관리하는 지능형 미들웨어입니다.

두 가지 방식을 지원합니다:
1. **HTTP API 방식** (포트 11501): Ollama HTTP 서버와 통신
2. **터미널 방식** (포트 11502): Ollama CLI를 직접 실행하여 더 빠른 응답

## 🚀 기능

### HTTP API 서버 (11501 포트)
- 큐 기반 요청 처리
- 실시간 상태 추적
- WebSocket 지원
- 동시 요청 제한
- 타임아웃 관리

### 터미널 서버 (11502 포트)
- Ollama CLI 직접 실행
- 실시간 스트리밍 (SSE)
- 즉시 응답 모드
- 더 빠른 처리 속도
- 디버깅 정보 제공

## 📦 설치 및 실행

### 1. 의존성 설치
```bash
npm install
```

### 2. 환경 변수 설정
```bash
cp .env.example .env
# .env 파일을 편집하여 환경 설정
```

### 3. 서버 실행

#### 전체 서버 실행 (두 서버 모두)
```bash
npm run start:both     # 프로덕션 모드
npm run dev:both       # 개발 모드
```

#### 개별 서버 실행
```bash
# HTTP API 서버만
npm start              # 또는 npm run start
npm run dev

# 터미널 서버만  
npm run start:terminal
npm run dev:terminal
```

#### 스크립트로 실행
```bash
./start.sh
```

## 🔧 환경 변수

```env
# Ollama 서버 설정
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
OLLAMA_MODEL=gemma3:1b

# HTTP 서버 포트 (기존 방식)
PORT=11501

# 터미널 서버 포트 (새로운 방식)
TERMINAL_PORT=11502

# 로그 레벨
LOG_LEVEL=info

# WebSocket 설정
WS_HEARTBEAT_INTERVAL=30000

# 요청 제한
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=300000
```

## 📡 API 엔드포인트

### HTTP API 서버 (11501)

#### 모델 실행
```bash
POST http://localhost:11501/api/ollama/run
Content-Type: application/json

{
  "model": "gemma3:1b",
  "prompt": "안녕하세요!"
}
```

#### 상태 조회
```bash
GET http://localhost:11501/api/ollama/status/{requestId}
```

#### WebSocket 연결
```javascript
const ws = new WebSocket('ws://localhost:11501/ws');
```

### 터미널 서버 (11502)

#### 즉시 응답 모드
```bash
POST http://localhost:11502/api/ollama/run
Content-Type: application/json

{
  "model": "gemma3:1b", 
  "prompt": "안녕하세요!"
}
```

#### 실시간 스트리밍 (SSE)
```bash
GET http://localhost:11502/api/ollama/stream?model=gemma3:1b&prompt=안녕하세요!
```

#### JavaScript에서 SSE 사용
```javascript
const eventSource = new EventSource(
  'http://localhost:11502/api/ollama/stream?model=gemma3:1b&prompt=안녕하세요!'
);

eventSource.addEventListener('data', (event) => {
  const data = JSON.parse(event.data);
  console.log('Chunk:', data.chunk);
});

eventSource.addEventListener('end', (event) => {
  console.log('Stream ended');
  eventSource.close();
});
```

#### 사용 가능한 모델 목록
```bash
GET http://localhost:11502/api/ollama/models
```

#### 헬스 체크
```bash
GET http://localhost:11502/api/ollama/health
```

## 🏗️ 아키텍처

### HTTP API 서버 구조
```
클라이언트 → Express → Queue → Ollama HTTP API → 응답
                ↓
            WebSocket → 실시간 상태 전송
```

### 터미널 서버 구조  
```
클라이언트 → Express → spawn('ollama run') → 실시간 stdout → SSE/응답
```

## 📂 프로젝트 구조

```
ollama-server/
├── server.js              # HTTP API 서버 (11501)
├── terminal-server.js     # 터미널 서버 (11502)
├── package.json
├── .env
├── start.sh
├── config/
│   └── ollama.js          # Ollama 설정
├── controllers/
│   └── ollamaController.js # 컨트롤러
├── middlewares/
│   ├── queue.js           # 요청 큐 관리
│   └── statusTracker.js   # 상태 추적
├── routes/
│   └── ollama.js          # API 라우트
├── services/
│   └── ollamaService.js   # Ollama 서비스
└── utils/
    └── logger.js          # 로거
```

## 🔍 로그 및 모니터링

### 로그 레벨
- `error`: 오류 메시지만
- `warn`: 경고 및 오류
- `info`: 일반 정보 (기본값)
- `debug`: 디버그 정보 포함

### 로그 확인
```bash
tail -f logs/app.log
```

## 🚀 성능 최적화

### HTTP API 서버
- 큐 기반 처리로 안정성 향상
- 동시 요청 제한으로 리소스 관리
- WebSocket으로 실시간 상태 제공

### 터미널 서버
- CLI 직접 실행으로 속도 향상
- SSE로 실시간 스트리밍
- 프로세스 관리로 메모리 효율성

## 🛠️ 개발

### 개발 모드 실행
```bash
npm run dev:both
```

### 로그 디버깅
```bash
LOG_LEVEL=debug npm run dev:both
```

## 📋 요구사항

- Node.js 16+
- Ollama CLI 설치
- Ollama 서비스 실행 중

## 🤝 기여

1. Fork the repository
2. Create your feature branch
3. Commit your changes  
4. Push to the branch
5. Create a Pull Request

## 📄 라이선스

MIT License
