const dotenv = require('dotenv');
dotenv.config();

// OLLAMA_HOST가 host:port 형태인지 확인하여 분리
const ollamaHostEnv = process.env.OLLAMA_HOST || 'localhost';
let host, port;

if (ollamaHostEnv.includes(':')) {
  [host, port] = ollamaHostEnv.split(':');
  port = parseInt(port, 10);
} else {
  host = ollamaHostEnv;
  port = parseInt(process.env.OLLAMA_PORT, 10) || 11434;
}

module.exports = {
  host,
  port,
  basePath: '/api',
};
