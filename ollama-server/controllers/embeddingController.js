/**
 * Embedding API 컨트롤러
 * 텍스트 임베딩 관련 API 엔드포인트 처리
 */
class EmbeddingController {
    constructor(embeddingService) {
        this.embeddingService = embeddingService;
    }
    
    /**
     * 단일 텍스트 임베딩
     * POST /api/embedding/text
     */
    async embedText(req, res) {
        try {
            const { text, model } = req.body;
            
            if (!text) {
                return res.status(400).json({
                    error: 'Text is required',
                    code: 'MISSING_TEXT'
                });
            }
            
            const result = await this.embeddingService.embedText(text, model);
            
            res.json({
                success: true,
                data: result
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'EMBEDDING_ERROR'
            });
        }
    }
    
    /**
     * 배치 텍스트 임베딩
     * POST /api/embedding/batch
     */
    async embedBatch(req, res) {
        try {
            const { texts, model } = req.body;
            
            if (!Array.isArray(texts) || texts.length === 0) {
                return res.status(400).json({
                    error: 'Valid texts array is required',
                    code: 'MISSING_TEXTS'
                });
            }
            
            if (texts.length > 50) {
                return res.status(400).json({
                    error: 'Maximum 50 texts allowed per batch',
                    code: 'BATCH_TOO_LARGE'
                });
            }
            
            const results = await this.embeddingService.embedBatch(texts, model);
            
            res.json({
                success: true,
                data: {
                    totalTexts: texts.length,
                    results
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'BATCH_EMBEDDING_ERROR'
            });
        }
    }
    
    /**
     * 문서 임베딩 (청크 분할)
     * POST /api/embedding/document
     */
    async embedDocument(req, res) {
        try {
            const { text, chunkSize, overlap, model } = req.body;
            
            if (!text) {
                return res.status(400).json({
                    error: 'Text is required',
                    code: 'MISSING_TEXT'
                });
            }
            
            const options = {
                chunkSize: chunkSize || 512,
                overlap: overlap || 50,
                model
            };
            
            const result = await this.embeddingService.embedDocument(text, options);
            
            res.json({
                success: true,
                data: result
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'DOCUMENT_EMBEDDING_ERROR'
            });
        }
    }
    
    /**
     * 스트리밍 배치 임베딩
     * POST /api/embedding/stream
     */
    async embedStream(req, res) {
        try {
            const { texts, model } = req.body;
            
            if (!Array.isArray(texts) || texts.length === 0) {
                return res.status(400).json({
                    error: 'Valid texts array is required',
                    code: 'MISSING_TEXTS'
                });
            }
            
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            });
            
            // 연결 유지를 위한 heartbeat
            const heartbeat = setInterval(() => {
                res.write('data: {"type": "heartbeat"}\n\n');
            }, 30000);
            
            try {
                for (let i = 0; i < texts.length; i++) {
                    const text = texts[i];
                    
                    try {
                        const result = await this.embeddingService.embedText(text, model);
                        
                        res.write(`data: ${JSON.stringify({
                            type: 'result',
                            index: i,
                            result
                        })}\n\n`);
                        
                    } catch (error) {
                        res.write(`data: ${JSON.stringify({
                            type: 'error',
                            index: i,
                            error: error.message
                        })}\n\n`);
                    }
                }
                
                res.write(`data: ${JSON.stringify({
                    type: 'complete',
                    totalProcessed: texts.length
                })}\n\n`);
                
            } finally {
                clearInterval(heartbeat);
                res.end();
            }
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'STREAM_EMBEDDING_ERROR'
            });
        }
    }
    
    /**
     * 사용 가능한 모델 목록
     * GET /api/embedding/models
     */
    async getModels(req, res) {
        try {
            const models = await this.embeddingService.getAvailableModels();
            
            res.json({
                success: true,
                data: {
                    models,
                    defaultModel: this.embeddingService.defaultModel,
                    totalModels: models.length
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'MODELS_ERROR'
            });
        }
    }
    
    /**
     * 서비스 상태
     * GET /api/embedding/status
     */
    async getStatus(req, res) {
        try {
            const status = await this.embeddingService.getStatus();
            
            res.json({
                success: true,
                data: status
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'STATUS_ERROR'
            });
        }
    }
    
    /**
     * 헬스 체크
     * GET /api/embedding/health
     */
    async healthCheck(req, res) {
        try {
            const status = await this.embeddingService.getStatus();
            
            if (status.ollama.connected) {
                res.json({
                    success: true,
                    status: 'healthy',
                    service: 'embedding',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(503).json({
                    success: false,
                    status: 'unhealthy',
                    service: 'embedding',
                    error: status.ollama.error,
                    timestamp: new Date().toISOString()
                });
            }
            
        } catch (error) {
            res.status(503).json({
                success: false,
                status: 'unhealthy',
                service: 'embedding',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
}

module.exports = EmbeddingController;
