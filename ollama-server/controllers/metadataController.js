/**
 * 메타데이터 추출 API 컨트롤러
 * - Ultrathink 설계 기반 고급 메타데이터 추출
 * - HTTP/터미널 방식 모두 지원
 * - 실시간 상태 추적
 */

const MetadataExtractionService = require('../services/metadataService');
const statusTracker = require('../middlewares/statusTracker');
const logger = require('../utils/logger');

class MetadataController {
  constructor() {
    this.metadataService = new MetadataExtractionService();
  }

  /**
   * HTTP API 방식으로 메타데이터 추출
   */
  async extractViaHttp(req, res) {
    const { text, textType, model, temperature } = req.body;
    
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Text parameter is required and must be non-empty string'
      });
    }

    const requestId = statusTracker.create();
    
    logger.info('HTTP metadata extraction request', {
      requestId,
      textLength: text.length,
      textType: textType || 'plain',
      model: model || 'default'
    });

    try {
      statusTracker.update(requestId, { 
        status: 'processing',
        method: 'http',
        startTime: new Date().toISOString()
      });

      const result = await this.metadataService.extractViaHttp(text, {
        textType,
        model,
        temperature
      });

      statusTracker.update(requestId, {
        status: 'completed',
        result,
        endTime: new Date().toISOString()
      });

      res.json({
        success: true,
        data: {
          requestId,
          metadata: result,
          method: 'http',
          textLength: text.length,
          processingTime: Date.now() - statusTracker.get(requestId).createdAt
        }
      });

    } catch (error) {
      logger.error('HTTP metadata extraction failed', {
        requestId,
        error: error.message,
        stack: error.stack
      });

      statusTracker.update(requestId, {
        status: 'failed',
        error: error.message,
        endTime: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        error: error.message,
        requestId
      });
    }
  }

  /**
   * 터미널 방식으로 메타데이터 추출
   */
  async extractViaTerminal(req, res) {
    const { text, textType, model, timeout } = req.body;
    
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Text parameter is required and must be non-empty string'
      });
    }

    const requestId = statusTracker.create();
    
    logger.info('Terminal metadata extraction request', {
      requestId,
      textLength: text.length,
      textType: textType || 'plain',
      model: model || 'default'
    });

    try {
      statusTracker.update(requestId, { 
        status: 'processing',
        method: 'terminal',
        startTime: new Date().toISOString()
      });

      const result = await this.metadataService.extractViaTerminal(text, {
        textType,
        model,
        timeout
      });

      statusTracker.update(requestId, {
        status: 'completed',
        result,
        endTime: new Date().toISOString()
      });

      res.json({
        success: true,
        data: {
          requestId,
          metadata: result,
          method: 'terminal',
          textLength: text.length,
          processingTime: Date.now() - statusTracker.get(requestId).createdAt
        }
      });

    } catch (error) {
      logger.error('Terminal metadata extraction failed', {
        requestId,
        error: error.message,
        stack: error.stack
      });

      statusTracker.update(requestId, {
        status: 'failed',
        error: error.message,
        endTime: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        error: error.message,
        requestId
      });
    }
  }

  /**
   * 자동 방식 선택으로 메타데이터 추출
   */
  async extractAuto(req, res) {
    const { text, textType, model, preferHttp = false } = req.body;
    
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Text parameter is required and must be non-empty string'
      });
    }

    const requestId = statusTracker.create();
    
    logger.info('Auto metadata extraction request', {
      requestId,
      textLength: text.length,
      textType: textType || 'plain',
      preferHttp
    });

    try {
      statusTracker.update(requestId, { 
        status: 'processing',
        method: 'auto',
        preferHttp,
        startTime: new Date().toISOString()
      });

      const result = await this.metadataService.extractAuto(text, {
        textType,
        model,
        preferHttp
      });

      statusTracker.update(requestId, {
        status: 'completed',
        result,
        endTime: new Date().toISOString()
      });

      res.json({
        success: true,
        data: {
          requestId,
          metadata: result,
          method: 'auto',
          textLength: text.length,
          processingTime: Date.now() - statusTracker.get(requestId).createdAt
        }
      });

    } catch (error) {
      logger.error('Auto metadata extraction failed', {
        requestId,
        error: error.message,
        stack: error.stack
      });

      statusTracker.update(requestId, {
        status: 'failed',
        error: error.message,
        endTime: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        error: error.message,
        requestId
      });
    }
  }

  /**
   * 실시간 스트리밍 메타데이터 추출 (터미널 방식)
   */
  async extractStream(req, res) {
    const { text, textType, model } = req.query;
    
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Text parameter is required and must be non-empty string'
      });
    }

    const requestId = statusTracker.create();
    
    logger.info('Stream metadata extraction request', {
      requestId,
      textLength: text.length
    });

    // SSE 헤더 설정
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.flushHeaders();

    // 연결 확인 메시지
    res.write(`event: connected\ndata: ${JSON.stringify({
      requestId,
      status: 'connected',
      textLength: text.length
    })}\n\n`);

    try {
      statusTracker.update(requestId, { 
        status: 'processing',
        method: 'stream',
        startTime: new Date().toISOString()
      });

      // 상태 업데이트를 스트림으로 전송
      res.write(`event: status\ndata: ${JSON.stringify({
        requestId,
        status: 'processing',
        message: 'Starting metadata extraction...'
      })}\n\n`);

      const result = await this.metadataService.extractViaTerminal(text, {
        textType,
        model
      });

      statusTracker.update(requestId, {
        status: 'completed',
        result,
        endTime: new Date().toISOString()
      });

      // 결과 전송
      res.write(`event: result\ndata: ${JSON.stringify({
        requestId,
        metadata: result,
        status: 'completed'
      })}\n\n`);

      res.write(`event: end\ndata: ${JSON.stringify({
        requestId,
        status: 'completed'
      })}\n\n`);

      res.end();

    } catch (error) {
      logger.error('Stream metadata extraction failed', {
        requestId,
        error: error.message
      });

      statusTracker.update(requestId, {
        status: 'failed',
        error: error.message,
        endTime: new Date().toISOString()
      });

      res.write(`event: error\ndata: ${JSON.stringify({
        requestId,
        error: error.message,
        status: 'failed'
      })}\n\n`);

      res.end();
    }

    // 클라이언트 연결 종료 처리
    req.on('close', () => {
      logger.info('Stream client disconnected', { requestId });
    });
  }

  /**
   * 요청 상태 조회
   */
  async getStatus(req, res) {
    const { requestId } = req.params;
    
    if (!requestId) {
      return res.status(400).json({
        success: false,
        error: 'Request ID is required'
      });
    }

    const status = statusTracker.get(requestId);
    
    if (!status) {
      return res.status(404).json({
        success: false,
        error: 'Request not found'
      });
    }

    res.json({
      success: true,
      data: status
    });
  }

  /**
   * 서비스 헬스 체크
   */
  async healthCheck(req, res) {
    try {
      const health = await this.metadataService.healthCheck();
      
      res.json({
        success: true,
        data: health
      });

    } catch (error) {
      logger.error('Health check failed', error);
      
      res.status(500).json({
        success: false,
        error: error.message,
        data: {
          status: 'unhealthy',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * 프롬프트 템플릿 정보 조회
   */
  async getPromptInfo(req, res) {
    try {
      const promptTemplate = this.metadataService.promptTemplate;
      
      res.json({
        success: true,
        data: {
          systemPrompt: promptTemplate.systemPrompt,
          exampleCount: promptTemplate.examples.length,
          categories: promptTemplate.categories,
          examples: promptTemplate.examples.slice(0, 3) // 처음 3개 예시만
        }
      });

    } catch (error) {
      logger.error('Failed to get prompt info', error);
      
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 배치 메타데이터 추출 (여러 텍스트 동시 처리)
   */
  async extractBatch(req, res) {
    const { texts, textType, model, method = 'auto' } = req.body;
    
    if (!Array.isArray(texts) || texts.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Texts array is required and must be non-empty'
      });
    }

    if (texts.length > 10) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 10 texts allowed per batch'
      });
    }

    const batchId = statusTracker.create();
    
    logger.info('Batch metadata extraction request', {
      batchId,
      textCount: texts.length,
      method
    });

    try {
      statusTracker.update(batchId, { 
        status: 'processing',
        method: 'batch',
        textCount: texts.length,
        startTime: new Date().toISOString()
      });

      const results = [];
      
      for (let i = 0; i < texts.length; i++) {
        const text = texts[i];
        
        if (typeof text !== 'string' || text.trim().length === 0) {
          results.push({
            index: i,
            success: false,
            error: 'Invalid text input'
          });
          continue;
        }

        try {
          let metadata;
          
          switch (method) {
            case 'http':
              metadata = await this.metadataService.extractViaHttp(text, { textType, model });
              break;
            case 'terminal':
              metadata = await this.metadataService.extractViaTerminal(text, { textType, model });
              break;
            default:
              metadata = await this.metadataService.extractAuto(text, { textType, model });
          }
          
          results.push({
            index: i,
            success: true,
            metadata
          });

        } catch (error) {
          logger.warn(`Batch item ${i} failed`, { error: error.message });
          results.push({
            index: i,
            success: false,
            error: error.message
          });
        }
      }

      statusTracker.update(batchId, {
        status: 'completed',
        results,
        endTime: new Date().toISOString()
      });

      const successCount = results.filter(r => r.success).length;
      
      res.json({
        success: true,
        data: {
          batchId,
          totalCount: texts.length,
          successCount,
          failureCount: texts.length - successCount,
          results,
          processingTime: Date.now() - statusTracker.get(batchId).createdAt
        }
      });

    } catch (error) {
      logger.error('Batch metadata extraction failed', {
        batchId,
        error: error.message
      });

      statusTracker.update(batchId, {
        status: 'failed',
        error: error.message,
        endTime: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        error: error.message,
        batchId
      });
    }
  }
}

module.exports = MetadataController;
