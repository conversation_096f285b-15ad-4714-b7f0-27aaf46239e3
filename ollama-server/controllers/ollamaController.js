const queue = require('../middlewares/queue');
const statusTracker = require('../middlewares/statusTracker');
const ollamaService = require('../services/ollamaService');
const logger = require('../utils/logger');

// 큐 처리자 등록
queue.onProcess(async ({ requestId, model, text }) => {
  try {
    statusTracker.update(requestId, { status: 'processing', startedAt: Date.now() });
    
    let result = '';
    await ollamaService.runModel(requestId, { model, text }, (chunk) => {
      result += chunk;
      statusTracker.update(requestId, { 
        status: 'streaming', 
        partial: chunk,
        currentLength: result.length
      });
    });
    
    statusTracker.update(requestId, { 
      status: 'completed', 
      result,
      completedAt: Date.now()
    });
    
  } catch (error) {
    logger.error(`Job processing failed: ${requestId}`, error);
    statusTracker.update(requestId, { 
      status: 'failed', 
      error: error.message,
      failedAt: Date.now()
    });
  }
});

exports.handleRun = (req, res) => {
  try {
    const { model, text } = req.body;
    
    if (!model || !text) {
      return res.status(400).json({
        error: 'Missing required fields: model and text'
      });
    }
    
    const requestId = statusTracker.create();
    queue.add({ requestId, model, text });
    
    logger.info(`New request queued: ${requestId}`, { model });
    
    res.json({ 
      requestId,
      status: 'queued',
      message: 'Request has been queued for processing'
    });
    
  } catch (error) {
    logger.error('Failed to handle run request', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
};

exports.handleStatus = (req, res) => {
  try {
    const { id } = req.params;
    const track = statusTracker.get(id);
    
    if (!track) {
      return res.status(404).json({
        error: 'Request not found'
      });
    }
    
    res.json(track);
    
  } catch (error) {
    logger.error('Failed to handle status request', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
};

exports.handleList = (req, res) => {
  try {
    const trackers = statusTracker.list();
    res.json({
      total: trackers.length,
      trackers
    });
  } catch (error) {
    logger.error('Failed to handle list request', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
};

exports.handleModels = async (req, res) => {
  try {
    const models = await ollamaService.listModels();
    res.json({ models });
  } catch (error) {
    logger.error('Failed to list models', error);
    res.status(500).json({
      error: 'Failed to fetch models from Ollama'
    });
  }
};

exports.handleHealth = async (req, res) => {
  try {
    const health = await ollamaService.checkHealth();
    res.json(health);
  } catch (error) {
    logger.error('Failed to check health', error);
    res.status(500).json({
      error: 'Failed to check Ollama health'
    });
  }
};
