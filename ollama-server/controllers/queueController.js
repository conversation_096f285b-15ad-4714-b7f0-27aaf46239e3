/**
 * Queue 관리 컨트롤러
 * Ollama 요청 큐 상태 모니터링 및 관리
 */
class QueueController {
    constructor(ollamaQueue) {
        this.queue = ollamaQueue;
    }
    
    /**
     * 큐 상태 조회
     * GET /api/queue/status
     */
    async getStatus(req, res) {
        try {
            const status = this.queue.getStatus();
            
            res.json({
                success: true,
                data: status
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'QUEUE_STATUS_ERROR'
            });
        }
    }
    
    /**
     * 큐 통계 조회
     * GET /api/queue/stats
     */
    async getStats(req, res) {
        try {
            const status = this.queue.getStatus();
            
            res.json({
                success: true,
                data: {
                    queue: {
                        current: status.queueSize,
                        max: status.maxSize,
                        utilization: (status.queueSize / status.maxSize * 100).toFixed(2) + '%'
                    },
                    processing: {
                        active: status.processing,
                        currentJob: status.currentJob
                    },
                    performance: {
                        totalProcessed: status.stats.totalProcessed,
                        totalFailed: status.stats.totalFailed,
                        successRate: status.stats.totalProcessed > 0 
                            ? ((status.stats.totalProcessed / (status.stats.totalProcessed + status.stats.totalFailed)) * 100).toFixed(2) + '%'
                            : '0%',
                        averageProcessTime: Math.round(status.stats.averageProcessTime) + 'ms',
                        queueHighWaterMark: status.stats.queueHighWaterMark
                    },
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'QUEUE_STATS_ERROR'
            });
        }
    }
    
    /**
     * 예정된 작업 목록
     * GET /api/queue/upcoming
     */
    async getUpcoming(req, res) {
        try {
            const limit = parseInt(req.query.limit) || 10;
            const status = this.queue.getStatus();
            
            res.json({
                success: true,
                data: {
                    totalUpcoming: status.queueSize,
                    upcomingJobs: status.upcomingJobs.slice(0, limit)
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'QUEUE_UPCOMING_ERROR'
            });
        }
    }
    
    /**
     * 특정 작업 취소
     * DELETE /api/queue/job/:jobId
     */
    async cancelJob(req, res) {
        try {
            const { jobId } = req.params;
            
            if (!jobId) {
                return res.status(400).json({
                    error: 'Job ID is required',
                    code: 'MISSING_JOB_ID'
                });
            }
            
            const cancelled = this.queue.cancelJob(jobId);
            
            if (cancelled) {
                res.json({
                    success: true,
                    message: 'Job cancelled successfully',
                    jobId
                });
            } else {
                res.status(404).json({
                    error: 'Job not found',
                    code: 'JOB_NOT_FOUND',
                    jobId
                });
            }
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'CANCEL_JOB_ERROR'
            });
        }
    }
    
    /**
     * 큐 초기화 (모든 대기 작업 취소)
     * DELETE /api/queue/clear
     */
    async clearQueue(req, res) {
        try {
            const { confirm } = req.body;
            
            if (confirm !== 'yes') {
                return res.status(400).json({
                    error: 'Queue clear requires confirmation. Send {"confirm": "yes"} in request body',
                    code: 'CONFIRMATION_REQUIRED'
                });
            }
            
            const cancelledJobs = this.queue.clear();
            
            res.json({
                success: true,
                message: 'Queue cleared successfully',
                cancelledJobs
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'CLEAR_QUEUE_ERROR'
            });
        }
    }
    
    /**
     * 큐 프로세서 시작/중지
     * POST /api/queue/processor
     */
    async controlProcessor(req, res) {
        try {
            const { action } = req.body;
            
            if (!action || !['start', 'stop'].includes(action)) {
                return res.status(400).json({
                    error: 'Valid action required (start or stop)',
                    code: 'INVALID_ACTION'
                });
            }
            
            if (action === 'start') {
                this.queue.startProcessor();
                res.json({
                    success: true,
                    message: 'Queue processor started',
                    processing: true
                });
            } else {
                this.queue.stopProcessor();
                res.json({
                    success: true,
                    message: 'Queue processor stopped',
                    processing: false
                });
            }
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'PROCESSOR_CONTROL_ERROR'
            });
        }
    }
    
    /**
     * 큐 헬스 체크
     * GET /api/queue/health
     */
    async healthCheck(req, res) {
        try {
            const status = this.queue.getStatus();
            
            // 큐가 너무 가득 찬 경우 unhealthy
            const utilizationRate = status.queueSize / status.maxSize;
            const isHealthy = utilizationRate < 0.9 && status.processing;
            
            if (isHealthy) {
                res.json({
                    success: true,
                    status: 'healthy',
                    service: 'queue',
                    details: {
                        queueSize: status.queueSize,
                        maxSize: status.maxSize,
                        processing: status.processing,
                        utilization: (utilizationRate * 100).toFixed(2) + '%'
                    },
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(503).json({
                    success: false,
                    status: 'unhealthy',
                    service: 'queue',
                    issues: [
                        utilizationRate >= 0.9 ? 'Queue nearly full' : null,
                        !status.processing ? 'Processor not running' : null
                    ].filter(Boolean),
                    details: {
                        queueSize: status.queueSize,
                        maxSize: status.maxSize,
                        processing: status.processing,
                        utilization: (utilizationRate * 100).toFixed(2) + '%'
                    },
                    timestamp: new Date().toISOString()
                });
            }
            
        } catch (error) {
            res.status(503).json({
                success: false,
                status: 'unhealthy',
                service: 'queue',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
}

module.exports = QueueController;
