/**
 * Rerank API 컨트롤러
 * 문서 재정렬 관련 API 엔드포인트 처리
 */
class RerankController {
    constructor(rerankService) {
        this.rerankService = rerankService;
    }
    
    /**
     * 문서 리랭킹
     * POST /api/rerank
     */
    async rerank(req, res) {
        try {
            const { query, documents, model, topK, returnDocuments, threshold } = req.body;
            
            if (!query) {
                return res.status(400).json({
                    error: 'Query is required',
                    code: 'MISSING_QUERY'
                });
            }
            
            if (!Array.isArray(documents) || documents.length === 0) {
                return res.status(400).json({
                    error: 'Valid documents array is required',
                    code: 'MISSING_DOCUMENTS'
                });
            }
            
            if (documents.length > 100) {
                return res.status(400).json({
                    error: 'Maximum 100 documents allowed',
                    code: 'TOO_MANY_DOCUMENTS'
                });
            }
            
            const options = {
                model,
                topK: topK || documents.length,
                returnDocuments: returnDocuments !== false, // 기본값 true
                threshold: threshold || 0.0
            };
            
            const result = await this.rerankService.rerank(query, documents, options);
            
            res.json({
                success: true,
                data: result
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'RERANK_ERROR'
            });
        }
    }
    
    /**
     * 배치 리랭킹 (여러 쿼리)
     * POST /api/rerank/batch
     */
    async rerankBatch(req, res) {
        try {
            const { queries, documents, model, topK, returnDocuments, threshold } = req.body;
            
            if (!Array.isArray(queries) || queries.length === 0) {
                return res.status(400).json({
                    error: 'Valid queries array is required',
                    code: 'MISSING_QUERIES'
                });
            }
            
            if (!Array.isArray(documents) || documents.length === 0) {
                return res.status(400).json({
                    error: 'Valid documents array is required',
                    code: 'MISSING_DOCUMENTS'
                });
            }
            
            if (queries.length > 10) {
                return res.status(400).json({
                    error: 'Maximum 10 queries allowed per batch',
                    code: 'TOO_MANY_QUERIES'
                });
            }
            
            if (documents.length > 100) {
                return res.status(400).json({
                    error: 'Maximum 100 documents allowed',
                    code: 'TOO_MANY_DOCUMENTS'
                });
            }
            
            const options = {
                model,
                topK: topK || documents.length,
                returnDocuments: returnDocuments !== false,
                threshold: threshold || 0.0
            };
            
            const results = await this.rerankService.rerankBatch(queries, documents, options);
            
            res.json({
                success: true,
                data: {
                    totalQueries: queries.length,
                    totalDocuments: documents.length,
                    results
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'BATCH_RERANK_ERROR'
            });
        }
    }
    
    /**
     * 스트리밍 리랭킹
     * POST /api/rerank/stream
     */
    async rerankStream(req, res) {
        try {
            const { queries, documents, model, topK, returnDocuments, threshold } = req.body;
            
            if (!Array.isArray(queries) || queries.length === 0) {
                return res.status(400).json({
                    error: 'Valid queries array is required',
                    code: 'MISSING_QUERIES'
                });
            }
            
            if (!Array.isArray(documents) || documents.length === 0) {
                return res.status(400).json({
                    error: 'Valid documents array is required',
                    code: 'MISSING_DOCUMENTS'
                });
            }
            
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            });
            
            // 연결 유지를 위한 heartbeat
            const heartbeat = setInterval(() => {
                res.write('data: {"type": "heartbeat"}\n\n');
            }, 30000);
            
            try {
                const options = {
                    model,
                    topK: topK || documents.length,
                    returnDocuments: returnDocuments !== false,
                    threshold: threshold || 0.0
                };
                
                for (let i = 0; i < queries.length; i++) {
                    const query = queries[i];
                    
                    try {
                        const result = await this.rerankService.rerank(query, documents, options);
                        
                        res.write(`data: ${JSON.stringify({
                            type: 'result',
                            index: i,
                            query,
                            result
                        })}\n\n`);
                        
                    } catch (error) {
                        res.write(`data: ${JSON.stringify({
                            type: 'error',
                            index: i,
                            query,
                            error: error.message
                        })}\n\n`);
                    }
                }
                
                res.write(`data: ${JSON.stringify({
                    type: 'complete',
                    totalProcessed: queries.length
                })}\n\n`);
                
            } finally {
                clearInterval(heartbeat);
                res.end();
            }
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'STREAM_RERANK_ERROR'
            });
        }
    }
    
    /**
     * 스코어링 전용 (문서 반환 없이 점수만)
     * POST /api/rerank/score
     */
    async scoreDocuments(req, res) {
        try {
            const { query, documents, model } = req.body;
            
            if (!query) {
                return res.status(400).json({
                    error: 'Query is required',
                    code: 'MISSING_QUERY'
                });
            }
            
            if (!Array.isArray(documents) || documents.length === 0) {
                return res.status(400).json({
                    error: 'Valid documents array is required',
                    code: 'MISSING_DOCUMENTS'
                });
            }
            
            const options = {
                model,
                topK: documents.length,
                returnDocuments: false, // 점수만 반환
                threshold: 0.0
            };
            
            const result = await this.rerankService.rerank(query, documents, options);
            
            res.json({
                success: true,
                data: {
                    query: result.query,
                    model: result.model,
                    scores: result.results.map(item => ({
                        index: item.index,
                        relevanceScore: item.relevanceScore,
                        reason: item.reason
                    }))
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'SCORE_ERROR'
            });
        }
    }
    
    /**
     * 사용 가능한 모델 목록
     * GET /api/rerank/models
     */
    async getModels(req, res) {
        try {
            const models = await this.rerankService.getAvailableModels();
            
            res.json({
                success: true,
                data: {
                    models,
                    defaultModel: this.rerankService.defaultModel,
                    totalModels: models.length
                }
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'MODELS_ERROR'
            });
        }
    }
    
    /**
     * 서비스 상태
     * GET /api/rerank/status
     */
    async getStatus(req, res) {
        try {
            const status = await this.rerankService.getStatus();
            
            res.json({
                success: true,
                data: status
            });
            
        } catch (error) {
            res.status(500).json({
                error: error.message,
                code: 'STATUS_ERROR'
            });
        }
    }
    
    /**
     * 헬스 체크
     * GET /api/rerank/health
     */
    async healthCheck(req, res) {
        try {
            const status = await this.rerankService.getStatus();
            
            if (status.ollama.connected) {
                res.json({
                    success: true,
                    status: 'healthy',
                    service: 'rerank',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(503).json({
                    success: false,
                    status: 'unhealthy',
                    service: 'rerank',
                    error: status.ollama.error,
                    timestamp: new Date().toISOString()
                });
            }
            
        } catch (error) {
            res.status(503).json({
                success: false,
                status: 'unhealthy',
                service: 'rerank',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
}

module.exports = RerankController;
