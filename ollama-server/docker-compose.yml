version: '3.8'

services:
  # Express 메타데이터 서비스 (호스트의 Ollama 11434에 연결)
  ollama-metadata-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ollama-metadata-server
    restart: unless-stopped
    
    # 포트 매핑 (기존 서비스 포트 충돌 방지)
    ports:
      - "7218:11501"  # HTTP API 서버
      - "7219:11502"  # 터미널 서버
      - "7220:7220"   # HTTP WebSocket 서버
      - "7221:7221"   # 터미널 WebSocket 서버
    
    # 환경 변수
    environment:
      - NODE_ENV=development  # 개발 모드로 변경
      - PORT=11501
      - TERMINAL_PORT=11502
      - LOG_LEVEL=info
      - OLLAMA_HOST=host.docker.internal:11434  # 호스트의 Ollama
      - OLLAMA_MODEL=gemma3:1b
      - DEFAULT_EMBEDDING_MODEL=nomic-embed-text
      - DEFAULT_RERANK_MODEL=bge-reranker-base
      - QUEUE_SIZE=100
      - QUEUE_PROCESS_INTERVAL=1000
      - WS_HEARTBEAT_INTERVAL=30000
      - MAX_CONCURRENT_REQUESTS=5
      - REQUEST_TIMEOUT=300000
    
    # 볼륨 마운트 - 개발용 소스 코드 실시간 반영
    volumes:
      - ./logs:/app/logs:rw
      - .:/app:rw
      - /app/node_modules  # node_modules는 컨테이너 내부 것을 사용
    
    # 네트워크 설정 (호스트 접근 허용)
    extra_hosts:
      - "host.docker.internal:host-gateway"
    
    # 리소스 제한
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # 헬스체크
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11501/api/metadata/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 네트워크 - 기존 dbs_dbs-network 사용
    networks:
      - default

# 네트워크
networks:
  default:
    external: true
    name: dbs_dbs-network
