/**
 * @swagger
 * components:
 *   schemas:
 *     SuccessResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *           description: 요청 성공 여부
 *         data:
 *           type: object
 *           description: 응답 데이터
 *         timestamp:
 *           type: string
 *           format: date-time
 *           example: "2025-01-13T12:00:00Z"
 *           description: 응답 생성 시각
 * 
 *     ErrorResponse:
 *       type: object
 *       required:
 *         - error
 *         - code
 *       properties:
 *         error:
 *           type: string
 *           example: "Invalid input provided"
 *           description: 에러 메시지
 *         code:
 *           type: string
 *           example: "VALIDATION_ERROR"
 *           description: 에러 코드
 *         timestamp:
 *           type: string
 *           format: date-time
 *           example: "2025-01-13T12:00:00Z"
 *           description: 에러 발생 시각
 *         details:
 *           type: object
 *           description: 추가 에러 정보
 * 
 *     HealthStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [healthy, degraded, unhealthy]
 *           example: "healthy"
 *           description: 서비스 상태
 *         services:
 *           type: object
 *           properties:
 *             ollama:
 *               $ref: '#/components/schemas/ServiceHealth'
 *             embedding:
 *               $ref: '#/components/schemas/ServiceHealth'
 *             rerank:
 *               $ref: '#/components/schemas/ServiceHealth'
 *             queue:
 *               $ref: '#/components/schemas/QueueHealth'
 *         timestamp:
 *           type: string
 *           format: date-time
 *           example: "2025-01-13T12:00:00Z"
 * 
 *     ServiceHealth:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [healthy, unhealthy, error]
 *           example: "healthy"
 *         connected:
 *           type: boolean
 *           example: true
 *           description: Ollama 서버 연결 상태
 *         host:
 *           type: string
 *           example: "localhost"
 *         port:
 *           type: string
 *           example: "11434"
 *         error:
 *           type: string
 *           description: 에러 메시지 (오류 시에만)
 * 
 *     QueueHealth:
 *       type: object
 *       properties:
 *         queueSize:
 *           type: integer
 *           example: 5
 *           description: 현재 큐 크기
 *         maxSize:
 *           type: integer
 *           example: 100
 *           description: 최대 큐 크기
 *         processing:
 *           type: boolean
 *           example: true
 *           description: 처리 중 여부
 *         utilization:
 *           type: string
 *           example: "5.00%"
 *           description: 큐 사용률
 *         stats:
 *           $ref: '#/components/schemas/QueueStats'
 * 
 *     QueueStats:
 *       type: object
 *       properties:
 *         totalProcessed:
 *           type: integer
 *           example: 1000
 *           description: 총 처리된 작업 수
 *         totalFailed:
 *           type: integer
 *           example: 5
 *           description: 총 실패한 작업 수
 *         averageProcessTime:
 *           type: number
 *           example: 1250.5
 *           description: 평균 처리 시간 (ms)
 *         queueHighWaterMark:
 *           type: integer
 *           example: 25
 *           description: 큐 최대 사용량 기록
 * 
 *     QueueJob:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           example: "550e8400-e29b-41d4-a716-************"
 *           description: 작업 고유 ID
 *         type:
 *           type: string
 *           enum: [chat, embedding, rerank, metadata]
 *           example: "embedding"
 *           description: 작업 타입
 *         priority:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           example: 3
 *           description: 작업 우선순위 (낮을수록 높은 우선순위)
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2025-01-13T12:00:00Z"
 *           description: 작업 생성 시각
 *         startedAt:
 *           type: string
 *           format: date-time
 *           example: "2025-01-13T12:00:05Z"
 *           description: 작업 시작 시각
 * 
 *     ModelInfo:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           example: "nomic-embed-text"
 *           description: 모델 이름
 *         size:
 *           type: string
 *           example: "274MB"
 *           description: 모델 크기
 *         modified:
 *           type: string
 *           format: date-time
 *           example: "2025-01-13T12:00:00Z"
 *           description: 모델 수정 시각
 *         isDefault:
 *           type: boolean
 *           example: true
 *           description: 기본 모델 여부
 * 
 *     TextInput:
 *       type: object
 *       required:
 *         - text
 *       properties:
 *         text:
 *           type: string
 *           minLength: 1
 *           maxLength: 10000
 *           example: "This is a sample text for processing"
 *           description: 처리할 텍스트
 *         model:
 *           type: string
 *           example: "nomic-embed-text"
 *           description: 사용할 모델 (옵션, 기본값 사용)
 * 
 *     BatchTextInput:
 *       type: object
 *       required:
 *         - texts
 *       properties:
 *         texts:
 *           type: array
 *           items:
 *             type: string
 *           minItems: 1
 *           maxItems: 50
 *           example: 
 *             - "First document to process"
 *             - "Second document for batch processing"
 *           description: 처리할 텍스트 배열
 *         model:
 *           type: string
 *           example: "nomic-embed-text"
 *           description: 사용할 모델 (옵션)
 */

// 이 파일은 JSDoc 주석만 포함하고 있어 실제 export는 없습니다
module.exports = {};
