/**
 * @swagger
 * components:
 *   schemas:
 *     EmbeddingTextRequest:
 *       allOf:
 *         - $ref: '#/components/schemas/TextInput'
 *       example:
 *         text: "Machine learning is transforming industries"
 *         model: "nomic-embed-text"
 * 
 *     EmbeddingTextResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 text:
 *                   type: string
 *                   example: "Machine learning is transforming industries"
 *                   description: 원본 텍스트
 *                 model:
 *                   type: string
 *                   example: "nomic-embed-text"
 *                   description: 사용된 모델
 *                 embedding:
 *                   type: array
 *                   items:
 *                     type: number
 *                   example: [0.1, -0.2, 0.3, 0.45, -0.1]
 *                   description: 임베딩 벡터
 *                 dimensions:
 *                   type: integer
 *                   example: 768
 *                   description: 벡터 차원 수
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2025-01-13T12:00:00Z"
 * 
 *     EmbeddingBatchRequest:
 *       allOf:
 *         - $ref: '#/components/schemas/BatchTextInput'
 *       example:
 *         texts: 
 *           - "First document about AI"
 *           - "Second document about machine learning"
 *           - "Third document about data science"
 *         model: "nomic-embed-text"
 * 
 *     EmbeddingBatchResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 totalTexts:
 *                   type: integer
 *                   example: 3
 *                   description: 총 처리된 텍스트 수
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       text:
 *                         type: string
 *                         example: "First document about AI"
 *                       model:
 *                         type: string
 *                         example: "nomic-embed-text"
 *                       embedding:
 *                         type: array
 *                         items:
 *                           type: number
 *                         example: [0.1, -0.2, 0.3]
 *                       dimensions:
 *                         type: integer
 *                         example: 768
 *                       timestamp:
 *                         type: string
 *                         format: date-time
 * 
 *     EmbeddingDocumentRequest:
 *       type: object
 *       required:
 *         - text
 *       properties:
 *         text:
 *           type: string
 *           minLength: 1
 *           maxLength: 50000
 *           example: "This is a very long document that will be split into chunks. It contains multiple paragraphs and sections..."
 *           description: 청크로 분할할 긴 문서
 *         chunkSize:
 *           type: integer
 *           minimum: 100
 *           maximum: 2000
 *           default: 512
 *           example: 512
 *           description: 청크 크기 (문자 수)
 *         overlap:
 *           type: integer
 *           minimum: 0
 *           maximum: 200
 *           default: 50
 *           example: 50
 *           description: 청크 간 중복 크기
 *         model:
 *           type: string
 *           example: "nomic-embed-text"
 *           description: 사용할 모델
 * 
 *     EmbeddingDocumentResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 originalText:
 *                   type: string
 *                   example: "Original long document text..."
 *                   description: 원본 문서 텍스트
 *                 totalChunks:
 *                   type: integer
 *                   example: 5
 *                   description: 총 청크 수
 *                 model:
 *                   type: string
 *                   example: "nomic-embed-text"
 *                 chunks:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       text:
 *                         type: string
 *                         example: "First chunk of the document..."
 *                       embedding:
 *                         type: array
 *                         items:
 *                           type: number
 *                         example: [0.1, -0.2, 0.3]
 *                       chunkIndex:
 *                         type: integer
 *                         example: 0
 *                       chunkStart:
 *                         type: integer
 *                         example: 0
 *                         description: 원본 텍스트에서의 시작 위치
 *                       chunkEnd:
 *                         type: integer
 *                         example: 512
 *                         description: 원본 텍스트에서의 끝 위치
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 * 
 *     EmbeddingStreamRequest:
 *       allOf:
 *         - $ref: '#/components/schemas/BatchTextInput'
 *       example:
 *         texts:
 *           - "Stream processing text 1"
 *           - "Stream processing text 2"
 *         model: "nomic-embed-text"
 * 
 *     EmbeddingModelsResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 models:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ModelInfo'
 *                 defaultModel:
 *                   type: string
 *                   example: "nomic-embed-text"
 *                   description: 기본 모델
 *                 totalModels:
 *                   type: integer
 *                   example: 3
 *                   description: 사용 가능한 모델 수
 * 
 *     EmbeddingStatusResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 service:
 *                   type: string
 *                   example: "embedding"
 *                 ollama:
 *                   $ref: '#/components/schemas/ServiceHealth'
 *                 defaultModel:
 *                   type: string
 *                   example: "nomic-embed-text"
 *                 queue:
 *                   $ref: '#/components/schemas/QueueHealth'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */

module.exports = {};
