/**
 * @swagger
 * components:
 *   schemas:
 *     MetadataExtractRequest:
 *       type: object
 *       required:
 *         - text
 *       properties:
 *         text:
 *           type: string
 *           minLength: 10
 *           maxLength: 10000
 *           example: "New study reveals how sleep improves memory retention in elderly adults through enhanced neural connectivity."
 *           description: 메타데이터를 추출할 텍스트
 *         textType:
 *           type: string
 *           enum: [plain, markdown, html, code, academic]
 *           default: plain
 *           example: "academic"
 *           description: 텍스트 유형 (프롬프트 최적화용)
 *         model:
 *           type: string
 *           example: "gemma3:1b"
 *           description: 사용할 모델 (옵션)
 *         method:
 *           type: string
 *           enum: [auto, http, terminal]
 *           default: auto
 *           example: "auto"
 *           description: 추출 방식 (auto는 HTTP 시도 후 Terminal 폴백)
 * 
 *     MetadataExtractResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 title:
 *                   type: string
 *                   example: "Sleep Boosts Memory in Elderly"
 *                   description: 추출된 제목
 *                 category:
 *                   type: string
 *                   example: "Health"
 *                   description: 분류된 카테고리
 *                 keywords:
 *                   type: array
 *                   items:
 *                     type: string
 *                   minItems: 5
 *                   maxItems: 5
 *                   example: ["sleep", "memory", "elderly", "study", "cognitive health"]
 *                   description: 추출된 키워드 (정확히 5개)
 *                 extractionMethod:
 *                   type: string
 *                   enum: [http, terminal]
 *                   example: "http"
 *                   description: 실제 사용된 추출 방식
 *                 processingTime:
 *                   type: number
 *                   example: 1250.5
 *                   description: 처리 시간 (밀리초)
 *                 textType:
 *                   type: string
 *                   example: "academic"
 *                   description: 지정된 텍스트 유형
 *                 model:
 *                   type: string
 *                   example: "gemma3:1b"
 *                   description: 사용된 모델
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 * 
 *     MetadataBatchRequest:
 *       type: object
 *       required:
 *         - texts
 *       properties:
 *         texts:
 *           type: array
 *           items:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 10000
 *               textType:
 *                 type: string
 *                 enum: [plain, markdown, html, code, academic]
 *                 default: plain
 *           minItems: 1
 *           maxItems: 20
 *           example:
 *             - text: "Climate change impacts global food security"
 *               textType: "academic"
 *             - text: "Top programming languages in 2025"
 *               textType: "plain"
 *           description: 처리할 텍스트 배열
 *         model:
 *           type: string
 *           example: "gemma3:1b"
 *         method:
 *           type: string
 *           enum: [auto, http, terminal]
 *           default: auto
 * 
 *     MetadataBatchResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 totalTexts:
 *                   type: integer
 *                   example: 2
 *                 successfulExtractions:
 *                   type: integer
 *                   example: 2
 *                 failedExtractions:
 *                   type: integer
 *                   example: 0
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       index:
 *                         type: integer
 *                         example: 0
 *                       success:
 *                         type: boolean
 *                         example: true
 *                       data:
 *                         type: object
 *                         properties:
 *                           title:
 *                             type: string
 *                           category:
 *                             type: string
 *                           keywords:
 *                             type: array
 *                             items:
 *                               type: string
 *                           extractionMethod:
 *                             type: string
 *                           processingTime:
 *                             type: number
 *                       error:
 *                         type: string
 *                         description: 에러 메시지 (실패 시에만)
 *                 totalProcessingTime:
 *                   type: number
 *                   example: 3500.2
 *                   description: 전체 처리 시간 (밀리초)
 * 
 *     MetadataSimpleRequest:
 *       type: object
 *       required:
 *         - text
 *       properties:
 *         text:
 *           type: string
 *           example: "Machine learning revolutionizes healthcare diagnosis"
 *         model:
 *           type: string
 *           example: "gemma3:1b"
 * 
 *     MetadataSimpleResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 title:
 *                   type: string
 *                   example: "ML in Healthcare Diagnosis"
 *                 category:
 *                   type: string
 *                   example: "Technology"
 *                 processingTime:
 *                   type: number
 *                   example: 850.3
 * 
 *     MetadataKeywordRequest:
 *       type: object
 *       required:
 *         - text
 *       properties:
 *         text:
 *           type: string
 *           example: "Artificial intelligence and machine learning are transforming various industries"
 *         keywordCount:
 *           type: integer
 *           minimum: 3
 *           maximum: 10
 *           default: 5
 *           example: 5
 *           description: 추출할 키워드 개수
 *         model:
 *           type: string
 *           example: "gemma3:1b"
 * 
 *     MetadataKeywordResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 keywords:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["artificial intelligence", "machine learning", "transforming", "industries", "technology"]
 *                 keywordCount:
 *                   type: integer
 *                   example: 5
 *                 processingTime:
 *                   type: number
 *                   example: 950.1
 * 
 *     MetadataStatusResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 service:
 *                   type: string
 *                   example: "metadata"
 *                 availableMethods:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["http", "terminal"]
 *                   description: 사용 가능한 추출 방식
 *                 supportedTextTypes:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["plain", "markdown", "html", "code", "academic"]
 *                 supportedCategories:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Technology", "Health", "Business", "Science", "Education"]
 *                 defaultModel:
 *                   type: string
 *                   example: "gemma3:1b"
 *                 queue:
 *                   $ref: '#/components/schemas/QueueHealth'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */

module.exports = {};
