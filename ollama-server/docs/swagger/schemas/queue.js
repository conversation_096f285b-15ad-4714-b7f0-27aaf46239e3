/**
 * @swagger
 * components:
 *   schemas:
 *     QueueStatusResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 queueSize:
 *                   type: integer
 *                   example: 5
 *                   description: 현재 대기 중인 작업 수
 *                 maxSize:
 *                   type: integer
 *                   example: 100
 *                   description: 큐 최대 크기
 *                 processing:
 *                   type: boolean
 *                   example: true
 *                   description: 큐 프로세서 실행 상태
 *                 currentJob:
 *                   oneOf:
 *                     - type: "null"
 *                     - $ref: '#/components/schemas/QueueJob'
 *                   description: 현재 처리 중인 작업
 *                 stats:
 *                   $ref: '#/components/schemas/QueueStats'
 *                 upcomingJobs:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/QueueJob'
 *                   maxItems: 5
 *                   description: 다음 5개 예정 작업
 * 
 *     QueueStatsResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 queue:
 *                   type: object
 *                   properties:
 *                     current:
 *                       type: integer
 *                       example: 5
 *                       description: 현재 큐 크기
 *                     max:
 *                       type: integer
 *                       example: 100
 *                       description: 최대 큐 크기
 *                     utilization:
 *                       type: string
 *                       example: "5.00%"
 *                       description: 큐 사용률
 *                 processing:
 *                   type: object
 *                   properties:
 *                     active:
 *                       type: boolean
 *                       example: true
 *                       description: 처리 활성 상태
 *                     currentJob:
 *                       oneOf:
 *                         - type: "null"
 *                         - type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                             type:
 *                               type: string
 *                             startedAt:
 *                               type: string
 *                               format: date-time
 *                 performance:
 *                   type: object
 *                   properties:
 *                     totalProcessed:
 *                       type: integer
 *                       example: 1000
 *                       description: 총 처리 완료된 작업 수
 *                     totalFailed:
 *                       type: integer
 *                       example: 5
 *                       description: 총 실패한 작업 수
 *                     successRate:
 *                       type: string
 *                       example: "99.50%"
 *                       description: 성공률
 *                     averageProcessTime:
 *                       type: string
 *                       example: "1250ms"
 *                       description: 평균 처리 시간
 *                     queueHighWaterMark:
 *                       type: integer
 *                       example: 25
 *                       description: 큐 최대 사용량 기록
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 * 
 *     QueueUpcomingResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 totalUpcoming:
 *                   type: integer
 *                   example: 15
 *                   description: 총 대기 중인 작업 수
 *                 upcomingJobs:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/QueueJob'
 *                   description: 예정된 작업 목록
 * 
 *     QueueCancelRequest:
 *       type: object
 *       properties:
 *         jobId:
 *           type: string
 *           format: uuid
 *           example: "550e8400-e29b-41d4-a716-************"
 *           description: 취소할 작업 ID
 * 
 *     QueueCancelResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Job cancelled successfully"
 *                 jobId:
 *                   type: string
 *                   format: uuid
 *                   example: "550e8400-e29b-41d4-a716-************"
 * 
 *     QueueClearRequest:
 *       type: object
 *       required:
 *         - confirm
 *       properties:
 *         confirm:
 *           type: string
 *           enum: ["yes"]
 *           example: "yes"
 *           description: 큐 초기화 확인 (반드시 "yes" 값 필요)
 * 
 *     QueueClearResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Queue cleared successfully"
 *                 cancelledJobs:
 *                   type: integer
 *                   example: 15
 *                   description: 취소된 작업 수
 * 
 *     QueueProcessorControlRequest:
 *       type: object
 *       required:
 *         - action
 *       properties:
 *         action:
 *           type: string
 *           enum: ["start", "stop"]
 *           example: "start"
 *           description: 프로세서 제어 액션
 * 
 *     QueueProcessorControlResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Queue processor started"
 *                 processing:
 *                   type: boolean
 *                   example: true
 *                   description: 현재 프로세서 상태
 * 
 *     QueueHealthResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         status:
 *           type: string
 *           enum: [healthy, unhealthy]
 *           example: "healthy"
 *         service:
 *           type: string
 *           example: "queue"
 *         details:
 *           type: object
 *           properties:
 *             queueSize:
 *               type: integer
 *               example: 5
 *             maxSize:
 *               type: integer
 *               example: 100
 *             processing:
 *               type: boolean
 *               example: true
 *             utilization:
 *               type: string
 *               example: "5.00%"
 *         issues:
 *           type: array
 *           items:
 *             type: string
 *           example: []
 *           description: 건강성 문제 목록 (unhealthy일 때)
 *         timestamp:
 *           type: string
 *           format: date-time
 */

module.exports = {};
