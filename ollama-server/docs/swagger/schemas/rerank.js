/**
 * @swagger
 * components:
 *   schemas:
 *     RerankRequest:
 *       type: object
 *       required:
 *         - query
 *         - documents
 *       properties:
 *         query:
 *           type: string
 *           minLength: 1
 *           maxLength: 1000
 *           example: "machine learning algorithms"
 *           description: 검색 쿼리
 *         documents:
 *           type: array
 *           items:
 *             oneOf:
 *               - type: string
 *               - type: object
 *                 properties:
 *                   text:
 *                     type: string
 *                   content:
 *                     type: string
 *           minItems: 1
 *           maxItems: 100
 *           example:
 *             - "Deep learning is a subset of machine learning"
 *             - "Cats are domestic animals that like to sleep"
 *             - {"text": "Neural networks are used in AI"}
 *           description: 재정렬할 문서들 (문자열 또는 객체 형태)
 *         model:
 *           type: string
 *           example: "bge-reranker-base"
 *           description: 사용할 모델 (옵션)
 *         topK:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           example: 5
 *           description: 반환할 최대 문서 수
 *         returnDocuments:
 *           type: boolean
 *           default: true
 *           example: true
 *           description: 문서 내용 포함 여부
 *         threshold:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *           default: 0.0
 *           example: 0.1
 *           description: 최소 관련성 점수 임계값
 * 
 *     RerankResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 query:
 *                   type: string
 *                   example: "machine learning algorithms"
 *                 model:
 *                   type: string
 *                   example: "bge-reranker-base"
 *                 totalDocuments:
 *                   type: integer
 *                   example: 5
 *                   description: 총 입력 문서 수
 *                 returnedDocuments:
 *                   type: integer
 *                   example: 3
 *                   description: 반환된 문서 수
 *                 topK:
 *                   type: integer
 *                   example: 5
 *                 threshold:
 *                   type: number
 *                   example: 0.1
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RerankResult'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 * 
 *     RerankResult:
 *       type: object
 *       properties:
 *         index:
 *           type: integer
 *           example: 0
 *           description: 원본 문서 배열에서의 인덱스
 *         relevanceScore:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *           example: 0.95
 *           description: 관련성 점수 (0-1)
 *         reason:
 *           type: string
 *           example: "Highly relevant because it directly discusses machine learning concepts"
 *           description: 점수 산정 이유
 *         document:
 *           oneOf:
 *             - type: string
 *             - type: object
 *           example: "Deep learning is a subset of machine learning"
 *           description: 문서 내용 (returnDocuments가 true일 때만)
 * 
 *     RerankBatchRequest:
 *       type: object
 *       required:
 *         - queries
 *         - documents
 *       properties:
 *         queries:
 *           type: array
 *           items:
 *             type: string
 *           minItems: 1
 *           maxItems: 10
 *           example:
 *             - "machine learning algorithms"
 *             - "deep learning models"
 *           description: 검색 쿼리들
 *         documents:
 *           type: array
 *           items:
 *             oneOf:
 *               - type: string
 *               - type: object
 *           minItems: 1
 *           maxItems: 100
 *           example:
 *             - "Deep learning is a subset of machine learning"
 *             - "Neural networks process information"
 *           description: 재정렬할 문서들
 *         model:
 *           type: string
 *           example: "bge-reranker-base"
 *         topK:
 *           type: integer
 *           example: 5
 *         returnDocuments:
 *           type: boolean
 *           default: true
 *         threshold:
 *           type: number
 *           default: 0.0
 * 
 *     RerankBatchResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 totalQueries:
 *                   type: integer
 *                   example: 2
 *                 totalDocuments:
 *                   type: integer
 *                   example: 5
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       query:
 *                         type: string
 *                         example: "machine learning algorithms"
 *                       model:
 *                         type: string
 *                       totalDocuments:
 *                         type: integer
 *                       returnedDocuments:
 *                         type: integer
 *                       results:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/RerankResult'
 * 
 *     RerankScoreRequest:
 *       type: object
 *       required:
 *         - query
 *         - documents
 *       properties:
 *         query:
 *           type: string
 *           example: "programming languages"
 *         documents:
 *           type: array
 *           items:
 *             oneOf:
 *               - type: string
 *               - type: object
 *           example:
 *             - "Python is a popular programming language"
 *             - "Elephants are large mammals"
 *         model:
 *           type: string
 *           example: "bge-reranker-base"
 * 
 *     RerankScoreResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 query:
 *                   type: string
 *                   example: "programming languages"
 *                 model:
 *                   type: string
 *                   example: "bge-reranker-base"
 *                 scores:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       index:
 *                         type: integer
 *                         example: 0
 *                       relevanceScore:
 *                         type: number
 *                         example: 0.95
 *                       reason:
 *                         type: string
 *                         example: "Highly relevant to programming topic"
 * 
 *     RerankModelsResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/SuccessResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 models:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ModelInfo'
 *                 defaultModel:
 *                   type: string
 *                   example: "bge-reranker-base"
 *                 totalModels:
 *                   type: integer
 *                   example: 2
 */

module.exports = {};
