/**
 * Swagger 기본 설정
 * OpenAPI 3.0 스펙을 따르는 Ollama Server API 문서화
 */

const swaggerConfig = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Ollama Server API',
      version: '2.0.0',
      description: `
# Ollama Server API Documentation

AI 서비스를 위한 통합 Ollama API 서버입니다.

## 주요 기능
- 🤖 **Ollama Chat**: 텍스트 생성 및 대화
- 📝 **Metadata**: 텍스트에서 메타데이터 추출  
- 🔤 **Embedding**: 텍스트를 벡터로 변환
- 🎯 **Rerank**: 문서 관련성 재정렬
- 🔄 **Queue**: 요청 큐 관리 및 모니터링

## 큐 시스템
모든 AI 요청은 큐를 통해 순차적으로 처리되어 Ollama 서버의 안정성을 보장합니다.

## 환경 설정
- Ollama 서버: localhost:11434
- 기본 임베딩 모델: nomic-embed-text
- 기본 리랭크 모델: bge-reranker-base
- 큐 크기: 100개 (설정 가능)
      `,
      contact: {
        name: 'API Support',
        url: 'http://localhost:7218/api/health',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:7218',
        description: 'HTTP API Server (로컬 개발)'
      },
      {
        url: 'http://localhost:7219', 
        description: 'Terminal Server (CLI 인터페이스)'
      }
    ],
    tags: [
      { 
        name: 'System', 
        description: '시스템 상태 및 헬스체크',
        externalDocs: {
          description: '시스템 모니터링 가이드',
          url: 'http://localhost:7218/api/health'
        }
      },
      { 
        name: 'Embedding', 
        description: '텍스트 임베딩 서비스 - 텍스트를 고차원 벡터로 변환' 
      },
      { 
        name: 'Rerank', 
        description: '문서 재정렬 서비스 - 검색 결과의 관련성 기반 재정렬' 
      },
      { 
        name: 'Queue', 
        description: '요청 큐 관리 - 실시간 모니터링 및 제어' 
      },
      { 
        name: 'Metadata', 
        description: '메타데이터 추출 - Few-shot prompting으로 구조화된 정보 추출' 
      },
      { 
        name: 'Ollama', 
        description: '기본 Ollama 채팅 - 텍스트 생성 및 대화형 AI' 
      }
    ],
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API 키 (현재 구현되지 않음, 향후 확장 예정)'
        }
      },
      responses: {
        BadRequest: {
          description: '잘못된 요청',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        InternalError: {
          description: '내부 서버 오류',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        ServiceUnavailable: {
          description: '서비스 사용 불가',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        }
      }
    }
  },
  apis: [
    './routes/*.js',
    './controllers/*.js', 
    './docs/swagger/schemas/*.js'
  ]
};

module.exports = swaggerConfig;
