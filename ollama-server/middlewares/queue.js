const { EventEmitter } = require('events');
const logger = require('../utils/logger');

const queue = new EventEmitter();

module.exports = {
  init: () => {
    logger.info('Queue system initialized');
    
    // worker 프로세스 등록
    queue.on('enqueue', async (job) => {
      logger.info(`Job enqueued: ${job.requestId}`);
      queue.emit('process', job);
    });
  },
  
  add: (job) => {
    logger.debug(`Adding job to queue: ${job.requestId}`);
    queue.emit('enqueue', job);
  },
  
  onProcess: (callback) => {
    queue.on('process', async (job) => {
      try {
        logger.info(`Processing job: ${job.requestId}`);
        await callback(job);
      } catch (error) {
        logger.error(`Job processing failed: ${job.requestId}`, error);
      }
    });
  },
};
