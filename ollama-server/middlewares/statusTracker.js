const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

const trackers = new Map();

module.exports = {
  init: () => {
    logger.info('Status tracker initialized');
    
    // 주기적으로 오래된 tracker 정리 (1시간 이상 된 것들)
    setInterval(() => {
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;
      
      for (const [id, track] of trackers.entries()) {
        if (now - track.createdAt > oneHour) {
          trackers.delete(id);
          logger.debug(`Cleaned up old tracker: ${id}`);
        }
      }
    }, 30 * 60 * 1000); // 30분마다 정리
  },
  
  create: () => {
    const id = uuidv4();
    trackers.set(id, { 
      status: 'pending', 
      clients: [], 
      createdAt: Date.now(),
      updatedAt: Date.now()
    });
    logger.debug(`Created tracker: ${id}`);
    return id;
  },
  
  update: (id, update) => {
    const track = trackers.get(id);
    if (!track) {
      logger.warn(`Tracker not found: ${id}`);
      return;
    }
    
    Object.assign(track, update, { updatedAt: Date.now() });
    logger.debug(`Updated tracker: ${id}`, update);
    
    // WebSocket 클라이언트들에게 상태 업데이트 전송
    track.clients.forEach(ws => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ id, ...track }));
      }
    });
  },
  
  get: (id) => {
    const track = trackers.get(id);
    if (!track) {
      logger.warn(`Tracker not found: ${id}`);
      return null;
    }
    
    // clients 정보는 제외하고 반환
    const { clients, ...trackData } = track;
    return trackData;
  },
  
  subscribe: (id, ws) => {
    const track = trackers.get(id);
    if (!track) {
      logger.warn(`Cannot subscribe to non-existent tracker: ${id}`);
      return false;
    }
    
    track.clients.push(ws);
    logger.debug(`WebSocket client subscribed to tracker: ${id}`);
    
    // 연결 해제 시 클라이언트 목록에서 제거
    ws.on('close', () => {
      const index = track.clients.indexOf(ws);
      if (index > -1) {
        track.clients.splice(index, 1);
        logger.debug(`WebSocket client unsubscribed from tracker: ${id}`);
      }
    });
    
    return true;
  },
  
  // 모든 tracker 목록 반환 (관리용)
  list: () => {
    const result = [];
    for (const [id, track] of trackers.entries()) {
      const { clients, ...trackData } = track;
      result.push({ id, ...trackData });
    }
    return result;
  }
};
