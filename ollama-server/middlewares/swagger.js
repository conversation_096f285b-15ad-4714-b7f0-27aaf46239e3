const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const swaggerConfig = require('../docs/swagger/swagger.config');
const logger = require('../utils/logger');

/**
 * Swagger 문서화 미들웨어
 * 실시간 API 문서 생성 및 서비스 상태 연동
 */
class SwaggerMiddleware {
  constructor(services = {}) {
    this.services = services;
    this.specs = null;
    this.initializeSpecs();
  }

  /**
   * Swagger 스펙 초기화
   */
  initializeSpecs() {
    try {
      this.specs = swaggerJsdoc(swaggerConfig);
      logger.info('Swagger specs initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Swagger specs:', error);
      this.specs = this.getMinimalSpecs();
    }
  }

  /**
   * 최소한의 스펙 (폴백용)
   */
  getMinimalSpecs() {
    return {
      openapi: '3.0.0',
      info: {
        title: 'Ollama Server API',
        version: '2.0.0',
        description: 'API documentation is being generated...'
      },
      paths: {}
    };
  }

  /**
   * 실시간 서비스 상태를 반영한 스펙 업데이트
   */
  async updateSpecsWithLiveData() {
    if (!this.specs) return this.specs;

    try {
      const liveSpecs = JSON.parse(JSON.stringify(this.specs));
      
      // 서버 상태 정보 업데이트
      if (this.services.embedding || this.services.rerank || this.services.queue) {
        liveSpecs.info.description += await this.generateLiveStatusInfo();
      }

      // 실시간 예시 데이터 추가
      await this.injectLiveExamples(liveSpecs);

      return liveSpecs;
    } catch (error) {
      logger.error('Failed to update specs with live data:', error);
      return this.specs;
    }
  }

  /**
   * 실시간 상태 정보 생성
   */
  async generateLiveStatusInfo() {
    const statusInfo = [];
    
    try {
      if (this.services.embedding) {
        const embeddingStatus = await this.services.embedding.getStatus();
        const status = embeddingStatus.ollama?.connected ? '🟢' : '🔴';
        statusInfo.push(`\n\n**Embedding Service**: ${status} ${embeddingStatus.ollama?.connected ? 'Connected' : 'Disconnected'}`);
      }

      if (this.services.rerank) {
        const rerankStatus = await this.services.rerank.getStatus();
        const status = rerankStatus.ollama?.connected ? '🟢' : '🔴';
        statusInfo.push(`**Rerank Service**: ${status} ${rerankStatus.ollama?.connected ? 'Connected' : 'Disconnected'}`);
      }

      if (this.services.queue) {
        const queueStatus = this.services.queue.getStatus();
        const utilization = ((queueStatus.queueSize / queueStatus.maxSize) * 100).toFixed(1);
        statusInfo.push(`**Queue Status**: ${queueStatus.processing ? '🟢' : '🔴'} ${queueStatus.queueSize}/${queueStatus.maxSize} (${utilization}%)`);
      }

      return statusInfo.length > 0 ? statusInfo.join('  \n') : '';
    } catch (error) {
      logger.warn('Failed to generate live status info:', error);
      return '';
    }
  }

  /**
   * 실시간 예시 데이터 주입
   */
  async injectLiveExamples(specs) {
    try {
      // Embedding 서비스 예시
      if (this.services.embedding && specs.components?.schemas?.EmbeddingTextResponse) {
        const exampleData = await this.generateEmbeddingExample();
        if (exampleData) {
          specs.components.schemas.EmbeddingTextResponse.example = exampleData;
        }
      }

      // Queue 상태 예시
      if (this.services.queue && specs.components?.schemas?.QueueStatusResponse) {
        const queueStatus = this.services.queue.getStatus();
        if (queueStatus) {
          specs.components.schemas.QueueStatusResponse.example = {
            success: true,
            data: queueStatus,
            timestamp: new Date().toISOString()
          };
        }
      }
    } catch (error) {
      logger.warn('Failed to inject live examples:', error);
    }
  }

  /**
   * Embedding 예시 생성
   */
  async generateEmbeddingExample() {
    try {
      // 임베딩 서비스가 준비되지 않았을 수 있으므로 상태 확인 후 실행
      const status = await this.services.embedding.getStatus();
      if (!status.ollama.connected) {
        logger.debug('Ollama not connected, skipping embedding example generation');
        return null;
      }

      const result = await this.services.embedding.embedText(
        "Live example: Swagger documentation with real-time data",
        "nomic-embed-text"
      );
      return {
        success: true,
        data: {
          ...result,
          embedding: result.embedding ? result.embedding.slice(0, 5) : [] // 처음 5개만 표시
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.debug('Failed to generate embedding example:', error.message);
      return null;
    }
  }

  /**
   * Express 앱에 Swagger 설정
   */
  setupSwagger(app) {
    // JSON 스펙 엔드포인트 (실시간 업데이트)
    app.get('/api-docs.json', async (req, res) => {
      try {
        const liveSpecs = await this.updateSpecsWithLiveData();
        res.json(liveSpecs);
      } catch (error) {
        logger.error('Failed to serve Swagger JSON:', error);
        res.status(500).json({ error: 'Failed to generate API documentation' });
      }
    });

    // Swagger UI 설정
    const swaggerOptions = {
      customCss: this.getCustomCSS(),
      customSiteTitle: 'Ollama Server API Documentation',
      customfavIcon: '/favicon.ico',
      swaggerOptions: {
        url: '/api-docs.json',
        requestInterceptor: (req) => {
          // API 키 자동 추가 (미래 확장용)
          req.headers['X-Client'] = 'swagger-ui';
          return req;
        },
        responseInterceptor: (res) => {
          // 응답 시간 표시
          if (res.url && res.body) {
            logger.info(`Swagger API call: ${res.url} - ${res.status}`);
          }
          return res;
        }
      }
    };

    app.use('/api-docs', swaggerUi.serve);
    app.get('/api-docs', swaggerUi.setup(null, swaggerOptions));

    // 추가 문서화 엔드포인트
    this.setupAdditionalDocs(app);

    logger.info('Swagger documentation available at /api-docs');
  }

  /**
   * 추가 문서화 엔드포인트 설정
   */
  setupAdditionalDocs(app) {
    // 큐 실시간 모니터링 페이지
    app.get('/api-docs/queue-monitor', (req, res) => {
      if (!this.services.queue) {
        return res.status(404).json({ error: 'Queue service not available' });
      }

      const queueStatus = this.services.queue.getStatus();
      res.json({
        title: 'Queue Real-time Monitor',
        refreshUrl: '/api/queue/status',
        data: queueStatus,
        timestamp: new Date().toISOString()
      });
    });

    // API 엔드포인트 목록
    app.get('/api-docs/endpoints', (req, res) => {
      const endpoints = this.extractEndpoints();
      res.json({
        title: 'Available API Endpoints',
        totalEndpoints: endpoints.length,
        endpoints,
        timestamp: new Date().toISOString()
      });
    });

    // 서비스 상태 대시보드
    app.get('/api-docs/status', async (req, res) => {
      try {
        const statusInfo = await this.generateCompleteStatus();
        res.json(statusInfo);
      } catch (error) {
        res.status(500).json({ error: 'Failed to get status information' });
      }
    });
  }

  /**
   * 엔드포인트 목록 추출
   */
  extractEndpoints() {
    if (!this.specs?.paths) return [];

    const endpoints = [];
    Object.keys(this.specs.paths).forEach(path => {
      Object.keys(this.specs.paths[path]).forEach(method => {
        const operation = this.specs.paths[path][method];
        endpoints.push({
          method: method.toUpperCase(),
          path,
          summary: operation.summary || 'No summary',
          tags: operation.tags || [],
          operationId: operation.operationId
        });
      });
    });

    return endpoints.sort((a, b) => a.path.localeCompare(b.path));
  }

  /**
   * 완전한 상태 정보 생성
   */
  async generateCompleteStatus() {
    const status = {
      documentation: {
        version: this.specs?.info?.version || 'unknown',
        totalEndpoints: this.extractEndpoints().length,
        lastUpdated: new Date().toISOString()
      },
      services: {}
    };

    // 각 서비스 상태 수집
    if (this.services.embedding) {
      status.services.embedding = await this.services.embedding.getStatus();
    }
    if (this.services.rerank) {
      status.services.rerank = await this.services.rerank.getStatus();
    }
    if (this.services.queue) {
      status.services.queue = this.services.queue.getStatus();
    }

    return status;
  }

  /**
   * 커스텀 CSS 스타일
   */
  getCustomCSS() {
    return `
      .swagger-ui .info {
        margin: 50px 0;
      }
      .swagger-ui .info hgroup.main {
        margin: 0 0 20px 0;
      }
      .swagger-ui .info .title {
        color: #3b82f6;
      }
      .swagger-ui .info .description {
        margin: 20px 0;
        color: #374151;
      }
      .swagger-ui .scheme-container {
        background: #f8fafc;
        padding: 20px;
        border-radius: 8px;
      }
      .swagger-ui .opblock.opblock-post {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }
      .swagger-ui .opblock.opblock-get {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.1);
      }
      .swagger-ui .opblock.opblock-delete {
        border-color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
      .swagger-ui .btn.execute {
        background-color: #10b981;
        border-color: #10b981;
      }
      .swagger-ui .response-col_status {
        font-weight: bold;
      }
    `;
  }
}

module.exports = SwaggerMiddleware;
