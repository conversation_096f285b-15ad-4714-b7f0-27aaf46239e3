{"name": "ollama-middleware", "version": "1.0.0", "description": "Ollama HTTP 서버와 연동하여 클라이언트 요청을 받으면 큐에 저장하고, 처리 상태를 관리하는 지능형 미들웨어", "main": "server.js", "scripts": {"start": "node server.js", "start:terminal": "node terminal-server.js", "start:both": "concurrently \"npm run start\" \"npm run start:terminal\"", "dev": "nodemon server.js", "dev:terminal": "nodemon terminal-server.js", "dev:both": "concurrently \"npm run dev\" \"npm run dev:terminal\""}, "dependencies": {"axios": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.0", "ws": "^8.13.0"}, "devDependencies": {"concurrently": "^8.0.0", "nodemon": "^3.0.0"}, "keywords": ["ollama", "middleware", "queue", "streaming"], "author": "", "license": "MIT"}