const express = require('express');
const router = express.Router();

/**
 * Embedding API 라우터
 * 텍스트 임베딩 관련 엔드포인트
 */
function createEmbeddingRouter(embeddingController) {
    /**
     * @swagger
     * /api/embedding/text:
     *   post:
     *     summary: 단일 텍스트 임베딩
     *     description: 하나의 텍스트를 벡터로 변환합니다. 큐를 통해 순차적으로 처리됩니다.
     *     tags: [Embedding]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/EmbeddingTextRequest'
     *           example:
     *             text: "Machine learning is transforming industries"
     *             model: "nomic-embed-text"
     *     responses:
     *       200:
     *         description: 임베딩 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/EmbeddingTextResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/text', embeddingController.embedText.bind(embeddingController));
    
    /**
     * @swagger
     * /api/embedding/batch:
     *   post:
     *     summary: 배치 텍스트 임베딩
     *     description: 여러 텍스트를 한 번에 임베딩합니다. 최대 50개까지 처리 가능합니다.
     *     tags: [Embedding]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/EmbeddingBatchRequest'
     *     responses:
     *       200:
     *         description: 배치 임베딩 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/EmbeddingBatchResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/batch', embeddingController.embedBatch.bind(embeddingController));
    
    /**
     * @swagger
     * /api/embedding/document:
     *   post:
     *     summary: 문서 임베딩 (청크 분할)
     *     description: 긴 문서를 청크로 분할하여 각각 임베딩합니다. 대용량 문서 처리에 적합합니다.
     *     tags: [Embedding]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/EmbeddingDocumentRequest'
     *     responses:
     *       200:
     *         description: 문서 임베딩 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/EmbeddingDocumentResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/document', embeddingController.embedDocument.bind(embeddingController));
    
    /**
     * @swagger
     * /api/embedding/stream:
     *   post:
     *     summary: 스트리밍 배치 임베딩
     *     description: Server-Sent Events를 통해 실시간으로 임베딩 결과를 스트리밍합니다.
     *     tags: [Embedding]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/EmbeddingStreamRequest'
     *     responses:
     *       200:
     *         description: 스트리밍 시작
     *         content:
     *           text/event-stream:
     *             schema:
     *               type: string
     *               example: |
     *                 data: {"type": "result", "index": 0, "result": {...}}
     *                 data: {"type": "complete", "totalProcessed": 2}
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/stream', embeddingController.embedStream.bind(embeddingController));
    
    /**
     * @swagger
     * /api/embedding/models:
     *   get:
     *     summary: 사용 가능한 임베딩 모델 목록
     *     description: Ollama에서 사용 가능한 임베딩 모델들을 조회합니다.
     *     tags: [Embedding]
     *     responses:
     *       200:
     *         description: 모델 목록 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/EmbeddingModelsResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/models', embeddingController.getModels.bind(embeddingController));
    
    /**
     * @swagger
     * /api/embedding/status:
     *   get:
     *     summary: 임베딩 서비스 상태
     *     description: 임베딩 서비스의 상태와 Ollama 연결 정보를 확인합니다.
     *     tags: [Embedding]
     *     responses:
     *       200:
     *         description: 서비스 상태 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/EmbeddingStatusResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/status', embeddingController.getStatus.bind(embeddingController));
    
    /**
     * @swagger
     * /api/embedding/health:
     *   get:
     *     summary: 임베딩 서비스 헬스체크
     *     description: 임베딩 서비스의 건강 상태를 확인합니다.
     *     tags: [Embedding]
     *     responses:
     *       200:
     *         description: 서비스 정상
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                   example: true
     *                 status:
     *                   type: string
     *                   example: "healthy"
     *                 service:
     *                   type: string
     *                   example: "embedding"
     *                 timestamp:
     *                   type: string
     *                   format: date-time
     *       503:
     *         $ref: '#/components/responses/ServiceUnavailable'
     */
    router.get('/health', embeddingController.healthCheck.bind(embeddingController));
    
    return router;
}

module.exports = createEmbeddingRouter;
