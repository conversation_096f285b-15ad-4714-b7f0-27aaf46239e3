/**
 * 메타데이터 추출 API 라우터
 * - Ultrathink 설계 기반 고급 메타데이터 추출 엔드포인트
 * - HTTP/터미널 방식 모두 지원
 * - 실시간 스트리밍 및 배치 처리
 */

const express = require('express');
const MetadataController = require('../controllers/metadataController');
const logger = require('../utils/logger');

// MetadataController 인스턴스 생성
const metadataController = new MetadataController();

const router = express.Router();

// 요청 로깅 미들웨어
router.use((req, res, next) => {
  logger.debug('Metadata API request', {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// HTTP API 방식 메타데이터 추출
/**
 * @swagger
 * /api/metadata/extract/http:
 *   post:
 *     summary: HTTP 방식 메타데이터 추출
 *     description: HTTP API를 통해 문서에서 메타데이터를 추출합니다. 빠른 응답을 위해 최적화되었습니다.
 *     tags: [Metadata]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MetadataExtractRequest'
 *           example:
 *             text: "이 문서는 인공지능과 머신러닝에 대한 내용을 다루고 있습니다."
 *             options:
 *               language_detection: true
 *               keyword_extraction: true
 *               sentiment_analysis: true
 *     responses:
 *       200:
 *         description: 메타데이터 추출 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataExtractResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/extract/http', metadataController.extractViaHttp);

// 터미널 방식 메타데이터 추출
/**
 * @swagger
 * /api/metadata/extract/terminal:
 *   post:
 *     summary: 터미널 방식 메타데이터 추출
 *     description: 터미널 인터페이스를 통해 메타데이터를 추출합니다. 복잡한 분석이나 디버깅에 적합합니다.
 *     tags: [Metadata]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MetadataExtractRequest'
 *           example:
 *             text: "복잡한 분석이 필요한 긴 문서 내용..."
 *             terminal_options:
 *               verbose: true
 *               debug_mode: true
 *     responses:
 *       200:
 *         description: 터미널 방식 메타데이터 추출 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataExtractResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/extract/terminal', metadataController.extractViaTerminal);

// 자동 방식 선택 메타데이터 추출 (기본)
/**
 * @swagger
 * /api/metadata/extract:
 *   post:
 *     summary: 자동 방식 메타데이터 추출
 *     description: 요청 내용에 따라 최적의 추출 방식(HTTP/터미널)을 자동으로 선택하여 메타데이터를 추출합니다.
 *     tags: [Metadata]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MetadataExtractRequest'
 *           example:
 *             text: "자동으로 최적 방식이 선택될 문서 내용입니다."
 *             auto_select: true
 *     responses:
 *       200:
 *         description: 메타데이터 추출 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataExtractResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/extract', metadataController.extractAuto);

// 실시간 스트리밍 메타데이터 추출
/**
 * @swagger
 * /api/metadata/extract/stream:
 *   get:
 *     summary: 실시간 스트리밍 메타데이터 추출
 *     description: Server-Sent Events(SSE)를 통해 실시간으로 메타데이터 추출 결과를 스트리밍합니다.
 *     tags: [Metadata]
 *     parameters:
 *       - in: query
 *         name: text
 *         required: true
 *         schema:
 *           type: string
 *         description: 분석할 텍스트 (URL 인코딩 필요)
 *         example: "실시간으로 분석할 텍스트"
 *       - in: query
 *         name: stream_interval
 *         schema:
 *           type: integer
 *           minimum: 100
 *           maximum: 5000
 *           default: 1000
 *         description: 스트리밍 간격 (밀리초)
 *     responses:
 *       200:
 *         description: 스트리밍 시작 성공
 *         content:
 *           text/event-stream:
 *             schema:
 *               type: string
 *               description: SSE 형태의 실시간 메타데이터
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/extract/stream', metadataController.extractStream);

// 배치 메타데이터 추출
/**
 * @swagger
 * /api/metadata/extract/batch:
 *   post:
 *     summary: 배치 메타데이터 추출
 *     description: 여러 문서에 대해 일괄적으로 메타데이터를 추출합니다. 대용량 문서 처리에 적합합니다.
 *     tags: [Metadata]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MetadataBatchRequest'
 *           example:
 *             documents:
 *               - text: "첫 번째 문서입니다."
 *                 id: "doc1"
 *               - text: "두 번째 문서입니다."
 *                 id: "doc2"
 *             batch_options:
 *               parallel_processing: true
 *               max_workers: 4
 *     responses:
 *       200:
 *         description: 배치 처리 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataBatchResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/extract/batch', metadataController.extractBatch);

// 요청 상태 조회
/**
 * @swagger
 * /api/metadata/status/{requestId}:
 *   get:
 *     summary: 메타데이터 추출 요청 상태 조회
 *     description: 특정 메타데이터 추출 요청의 진행 상태를 조회합니다.
 *     tags: [Metadata]
 *     parameters:
 *       - in: path
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 조회할 요청의 UUID
 *         example: "550e8400-e29b-41d4-a716-************"
 *     responses:
 *       200:
 *         description: 요청 상태 조회 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataStatusResponse'
 *       404:
 *         description: 요청을 찾을 수 없음
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/status/:requestId', metadataController.getStatus);

// 서비스 헬스 체크
/**
 * @swagger
 * /api/metadata/health:
 *   get:
 *     summary: 메타데이터 서비스 헬스체크
 *     description: 메타데이터 추출 서비스의 상태를 확인합니다. HTTP/터미널 방식 모두의 상태를 포함합니다.
 *     tags: [Metadata]
 *     responses:
 *       200:
 *         description: 메타데이터 서비스 정상
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataHealthResponse'
 *       503:
 *         description: 메타데이터 서비스 비정상
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataHealthResponse'
 */
router.get('/health', metadataController.healthCheck);

// 프롬프트 템플릿 정보 조회
/**
 * @swagger
 * /api/metadata/prompt-info:
 *   get:
 *     summary: 프롬프트 템플릿 정보 조회
 *     description: 메타데이터 추출에 사용되는 프롬프트 템플릿들의 정보를 조회합니다.
 *     tags: [Metadata]
 *     responses:
 *       200:
 *         description: 프롬프트 정보 조회 성공
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MetadataPromptInfoResponse'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/prompt-info', metadataController.getPromptInfo);

// API 문서 정보
router.get('/', (req, res) => {
  res.json({
    name: 'Metadata Extraction API',
    version: '1.0.0',
    description: 'Advanced metadata extraction using Ollama with Few-shot prompting',
    endpoints: {
      'POST /extract': 'Auto-select best method for metadata extraction',
      'POST /extract/http': 'Extract metadata using HTTP API method',
      'POST /extract/terminal': 'Extract metadata using terminal CLI method',
      'GET /extract/stream': 'Real-time streaming metadata extraction',
      'POST /extract/batch': 'Batch processing for multiple texts',
      'GET /status/:requestId': 'Get extraction request status',
      'GET /health': 'Service health check',
      'GET /prompt-info': 'Get prompt template information'
    },
    features: [
      'Few-shot prompting for improved accuracy',
      'JSON structured output enforcement',
      'Multiple extraction methods (HTTP/Terminal)',
      'Real-time streaming with SSE',
      'Batch processing support',
      'Automatic fallback mechanisms',
      'Comprehensive error handling'
    ],
    supportedTextTypes: [
      'plain', 'markdown', 'html', 'code', 'academic'
    ],
    supportedModels: [
      'gemma3:1b', 'gemma3:2b', 'gemma3:7b', 'llama3.1:7b', 'llama3.1:8b'
    ]
  });
});

// 에러 처리 미들웨어
router.use((error, req, res, next) => {
  logger.error('Metadata API error', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
