const express = require('express');
const router = express.Router();
const ollamaController = require('../controllers/ollamaController');

// 모델 실행 요청
/**
 * @swagger
 * /api/ollama/run:
 *   post:
 *     summary: Ollama 모델 실행 요청
 *     description: 지정된 Ollama 모델을 사용하여 텍스트 생성 또는 분석을 수행합니다.
 *     tags: [Ollama]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - model
 *               - prompt
 *             properties:
 *               model:
 *                 type: string
 *                 description: 사용할 Ollama 모델명
 *                 example: "llama2"
 *               prompt:
 *                 type: string
 *                 description: 모델에 전달할 프롬프트
 *                 example: "안녕하세요, 오늘 날씨는 어떤가요?"
 *               options:
 *                 type: object
 *                 description: 모델 실행 옵션
 *                 properties:
 *                   temperature:
 *                     type: number
 *                     minimum: 0
 *                     maximum: 2
 *                     default: 0.7
 *                   max_tokens:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 4096
 *                     default: 1024
 *                   stream:
 *                     type: boolean
 *                     default: false
 *     responses:
 *       200:
 *         description: 모델 실행 성공
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 request_id:
 *                   type: string
 *                   format: uuid
 *                   description: 요청 고유 ID
 *                 status:
 *                   type: string
 *                   enum: ["queued", "processing", "completed"]
 *                 response:
 *                   type: string
 *                   description: 모델 응답 (완료된 경우)
 *                 created_at:
 *                   type: string
 *                   format: date-time
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/run', ollamaController.handleRun);

// 상태 조회 (폴링 방식)
/**
 * @swagger
 * /api/ollama/status/{id}:
 *   get:
 *     summary: Ollama 요청 상태 조회
 *     description: 특정 Ollama 모델 실행 요청의 상태를 조회합니다.
 *     tags: [Ollama]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 조회할 요청의 UUID
 *         example: "550e8400-e29b-41d4-a716-************"
 *     responses:
 *       200:
 *         description: 요청 상태 조회 성공
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 request_id:
 *                   type: string
 *                   format: uuid
 *                 status:
 *                   type: string
 *                   enum: ["queued", "processing", "completed", "failed"]
 *                 progress:
 *                   type: integer
 *                   minimum: 0
 *                   maximum: 100
 *                   description: 진행률 (%)
 *                 response:
 *                   type: string
 *                   description: 모델 응답 (완료된 경우)
 *                 error:
 *                   type: string
 *                   description: 오류 메시지 (실패한 경우)
 *                 created_at:
 *                   type: string
 *                   format: date-time
 *                 completed_at:
 *                   type: string
 *                   format: date-time
 *       404:
 *         description: 요청을 찾을 수 없음
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/status/:id', ollamaController.handleStatus);

// 모든 요청 목록 조회 (관리용)
/**
 * @swagger
 * /api/ollama/list:
 *   get:
 *     summary: Ollama 요청 목록 조회
 *     description: 모든 Ollama 모델 실행 요청의 목록을 조회합니다. 관리자용 엔드포인트입니다.
 *     tags: [Ollama]
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: ["all", "queued", "processing", "completed", "failed"]
 *           default: "all"
 *         description: 필터링할 상태
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 조회할 최대 개수
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: 페이지네이션 오프셋
 *     responses:
 *       200:
 *         description: 요청 목록 조회 성공
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 requests:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       request_id:
 *                         type: string
 *                         format: uuid
 *                       model:
 *                         type: string
 *                       status:
 *                         type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                       completed_at:
 *                         type: string
 *                         format: date-time
 *                 total:
 *                   type: integer
 *                   description: 전체 요청 수
 *                 has_more:
 *                   type: boolean
 *                   description: 더 많은 데이터 존재 여부
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/list', ollamaController.handleList);

// 사용 가능한 모델 목록
/**
 * @swagger
 * /api/ollama/models:
 *   get:
 *     summary: 사용 가능한 Ollama 모델 목록
 *     description: 현재 시스템에서 사용 가능한 모든 Ollama 모델의 목록과 상태를 조회합니다.
 *     tags: [Ollama]
 *     responses:
 *       200:
 *         description: 모델 목록 조회 성공
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 models:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: 모델명
 *                         example: "llama2"
 *                       size:
 *                         type: string
 *                         description: 모델 크기
 *                         example: "7B"
 *                       status:
 *                         type: string
 *                         enum: ["available", "downloading", "unavailable"]
 *                       description:
 *                         type: string
 *                         description: 모델 설명
 *                       last_used:
 *                         type: string
 *                         format: date-time
 *                         description: 마지막 사용 시간
 *                 total_models:
 *                   type: integer
 *                   description: 전체 모델 수
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/models', ollamaController.handleModels);

// Ollama 서버 상태 확인
/**
 * @swagger
 * /api/ollama/health:
 *   get:
 *     summary: Ollama 서버 헬스체크
 *     description: Ollama 서버의 연결 상태와 전반적인 건강성을 확인합니다.
 *     tags: [Ollama]
 *     responses:
 *       200:
 *         description: Ollama 서버 정상
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: ["healthy", "unhealthy"]
 *                 ollama_version:
 *                   type: string
 *                   description: Ollama 버전
 *                 connection:
 *                   type: string
 *                   enum: ["connected", "disconnected"]
 *                 response_time:
 *                   type: number
 *                   description: 응답 시간 (ms)
 *                 last_check:
 *                   type: string
 *                   format: date-time
 *                 active_models:
 *                   type: integer
 *                   description: 현재 로드된 모델 수
 *       503:
 *         description: Ollama 서버 비정상
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   value: "unhealthy"
 *                 error:
 *                   type: string
 *                   description: 오류 메시지
 *                 last_check:
 *                   type: string
 *                   format: date-time
 */
router.get('/health', ollamaController.handleHealth);

module.exports = router;
