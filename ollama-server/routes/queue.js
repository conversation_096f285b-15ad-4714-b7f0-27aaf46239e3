const express = require('express');
const router = express.Router();

/**
 * Queue 관리 API 라우터
 * 큐 상태 모니터링 및 관리 엔드포인트
 */
function createQueueRouter(queueController) {
    /**
     * @swagger
     * /api/queue/status:
     *   get:
     *     summary: 큐 상태 조회
     *     description: 현재 큐의 상태, 처리 중인 작업, 대기 작업 등을 조회합니다.
     *     tags: [Queue]
     *     responses:
     *       200:
     *         description: 큐 상태 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueStatusResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/status', queueController.getStatus.bind(queueController));
    
    /**
     * @swagger
     * /api/queue/stats:
     *   get:
     *     summary: 큐 통계 조회
     *     description: 큐의 성능 통계, 처리 현황, 사용률 등을 조회합니다.
     *     tags: [Queue]
     *     responses:
     *       200:
     *         description: 큐 통계 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueStatsResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/stats', queueController.getStats.bind(queueController));
    
    /**
     * @swagger
     * /api/queue/upcoming:
     *   get:
     *     summary: 예정된 작업 목록
     *     description: 큐에서 대기 중인 작업들의 목록을 조회합니다.
     *     tags: [Queue]
     *     parameters:
     *       - in: query
     *         name: limit
     *         schema:
     *           type: integer
     *           minimum: 1
     *           maximum: 50
     *           default: 10
     *         description: 조회할 작업 수
     *         example: 5
     *     responses:
     *       200:
     *         description: 예정 작업 목록 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueUpcomingResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/upcoming', queueController.getUpcoming.bind(queueController));
    
    /**
     * @swagger
     * /api/queue/job/{jobId}:
     *   delete:
     *     summary: 특정 작업 취소
     *     description: 대기 중인 특정 작업을 취소합니다. 이미 처리 중인 작업은 취소할 수 없습니다.
     *     tags: [Queue]
     *     parameters:
     *       - in: path
     *         name: jobId
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *         description: 취소할 작업의 UUID
     *         example: "550e8400-e29b-41d4-a716-************"
     *     responses:
     *       200:
     *         description: 작업 취소 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueCancelResponse'
     *       404:
     *         description: 작업을 찾을 수 없음
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.delete('/job/:jobId', queueController.cancelJob.bind(queueController));
    
    /**
     * @swagger
     * /api/queue/clear:
     *   delete:
     *     summary: 큐 초기화 (모든 대기 작업 취소)
     *     description: 큐에 있는 모든 대기 작업을 취소합니다. 확인을 위해 요청 본문에 confirm 필드가 필요합니다.
     *     tags: [Queue]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/QueueClearRequest'
     *           example:
     *             confirm: "yes"
     *     responses:
     *       200:
     *         description: 큐 초기화 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueClearResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.delete('/clear', queueController.clearQueue.bind(queueController));
    
    /**
     * @swagger
     * /api/queue/processor:
     *   post:
     *     summary: 큐 프로세서 시작/중지
     *     description: 큐 프로세서를 시작하거나 중지합니다. 중지 시 현재 처리 중인 작업은 완료됩니다.
     *     tags: [Queue]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/QueueProcessorControlRequest'
     *           example:
     *             action: "start"
     *     responses:
     *       200:
     *         description: 프로세서 제어 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueProcessorControlResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/processor', queueController.controlProcessor.bind(queueController));
    
    /**
     * @swagger
     * /api/queue/health:
     *   get:
     *     summary: 큐 헬스체크
     *     description: 큐 시스템의 건강 상태를 확인합니다. 큐 사용률이 90% 이상이거나 프로세서가 중지된 경우 unhealthy로 표시됩니다.
     *     tags: [Queue]
     *     responses:
     *       200:
     *         description: 큐 시스템 정상
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueHealthResponse'
     *       503:
     *         description: 큐 시스템 비정상
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/QueueHealthResponse'
     */
    router.get('/health', queueController.healthCheck.bind(queueController));
    
    return router;
}

module.exports = createQueueRouter;
