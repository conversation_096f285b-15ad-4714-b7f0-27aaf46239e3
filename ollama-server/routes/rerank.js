const express = require('express');
const router = express.Router();

/**
 * Rerank API 라우터
 * 문서 재정렬 관련 엔드포인트
 */
function createRerankRouter(rerankController) {
    // 문서 리랭킹
    /**
     * @swagger
     * /api/rerank:
     *   post:
     *     summary: 문서 재정렬
     *     description: 주어진 쿼리에 대해 문서들을 관련성 순으로 재정렬합니다.
     *     tags: [Rerank]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/RerankRequest'
     *           example:
     *             query: "인공지능 기술 동향"
     *             documents:
     *               - "머신러닝은 인공지능의 한 분야입니다."
     *               - "딥러닝 기술이 빠르게 발전하고 있습니다."
     *               - "오늘 날씨가 좋습니다."
     *             top_k: 2
     *     responses:
     *       200:
     *         description: 문서 재정렬 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/', rerankController.rerank.bind(rerankController));
    
    // 배치 리랭킹 (여러 쿼리)
    /**
     * @swagger
     * /api/rerank/batch:
     *   post:
     *     summary: 배치 문서 재정렬
     *     description: 여러 쿼리에 대해 동시에 문서 재정렬을 수행합니다.
     *     tags: [Rerank]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/RerankBatchRequest'
     *           example:
     *             queries:
     *               - query: "인공지능 기술"
     *                 documents: ["AI 관련 문서1", "AI 관련 문서2"]
     *               - query: "머신러닝 동향"
     *                 documents: ["ML 관련 문서1", "ML 관련 문서2"]
     *             top_k: 3
     *     responses:
     *       200:
     *         description: 배치 재정렬 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankBatchResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/batch', rerankController.rerankBatch.bind(rerankController));
    
    // 스트리밍 리랭킹
    /**
     * @swagger
     * /api/rerank/stream:
     *   post:
     *     summary: 스트리밍 문서 재정렬
     *     description: 문서 재정렬 결과를 실시간으로 스트리밍합니다. 대용량 문서 처리에 적합합니다.
     *     tags: [Rerank]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/RerankStreamRequest'
     *           example:
     *             query: "기술 동향 분석"
     *             documents: ["문서1", "문서2", "문서3"]
     *             stream_options:
     *               chunk_size: 10
     *               delay_ms: 100
     *     responses:
     *       200:
     *         description: 스트리밍 재정렬 시작
     *         content:
     *           text/event-stream:
     *             schema:
     *               type: string
     *               description: SSE 형태의 실시간 재정렬 결과
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/stream', rerankController.rerankStream.bind(rerankController));
    
    // 스코어링 전용 (문서 반환 없이 점수만)
    /**
     * @swagger
     * /api/rerank/score:
     *   post:
     *     summary: 문서 관련성 점수만 계산
     *     description: 문서를 재정렬하지 않고 각 문서의 관련성 점수만 계산합니다.
     *     tags: [Rerank]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/RerankScoreRequest'
     *           example:
     *             query: "인공지능 개발"
     *             documents:
     *               - "AI 기술이 발전하고 있습니다."
     *               - "날씨가 좋습니다."
     *     responses:
     *       200:
     *         description: 점수 계산 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankScoreResponse'
     *       400:
     *         $ref: '#/components/responses/BadRequest'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.post('/score', rerankController.scoreDocuments.bind(rerankController));
    
    // 사용 가능한 모델 목록
    /**
     * @swagger
     * /api/rerank/models:
     *   get:
     *     summary: 사용 가능한 재정렬 모델 목록
     *     description: 현재 시스템에서 사용 가능한 문서 재정렬 모델들의 목록을 조회합니다.
     *     tags: [Rerank]
     *     responses:
     *       200:
     *         description: 모델 목록 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankModelsResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/models', rerankController.getModels.bind(rerankController));
    
    // 서비스 상태
    /**
     * @swagger
     * /api/rerank/status:
     *   get:
     *     summary: 재정렬 서비스 상태 조회
     *     description: 문서 재정렬 서비스의 현재 상태와 성능 지표를 조회합니다.
     *     tags: [Rerank]
     *     responses:
     *       200:
     *         description: 서비스 상태 조회 성공
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankStatusResponse'
     *       500:
     *         $ref: '#/components/responses/InternalError'
     */
    router.get('/status', rerankController.getStatus.bind(rerankController));
    
    // 헬스 체크
    /**
     * @swagger
     * /api/rerank/health:
     *   get:
     *     summary: 재정렬 서비스 헬스체크
     *     description: 문서 재정렬 서비스의 건강 상태를 확인합니다.
     *     tags: [Rerank]
     *     responses:
     *       200:
     *         description: 재정렬 서비스 정상
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankHealthResponse'
     *       503:
     *         description: 재정렬 서비스 비정상
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/RerankHealthResponse'
     */
    router.get('/health', rerankController.healthCheck.bind(rerankController));
    
    return router;
}

module.exports = createRerankRouter;
