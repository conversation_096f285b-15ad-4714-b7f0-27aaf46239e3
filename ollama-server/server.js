const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');

// 기존 라우터
const ollamaRouter = require('./routes/ollama');
const metadataRouter = require('./routes/metadata');

// 새로운 라우터
const createEmbeddingRouter = require('./routes/embedding');
const createRerankRouter = require('./routes/rerank');
const createQueueRouter = require('./routes/queue');

// 미들웨어
const queue = require('./middlewares/queue');
const statusTracker = require('./middlewares/statusTracker');
const SwaggerMiddleware = require('./middlewares/swagger');
const logger = require('./utils/logger');

// 새로운 서비스들
const OllamaQueue = require('./utils/ollamaQueue');
const OllamaService = require('./services/ollamaService');
const EmbeddingService = require('./services/embeddingService');
const RerankService = require('./services/rerankService');

// 컨트롤러들
const EmbeddingController = require('./controllers/embeddingController');
const RerankController = require('./controllers/rerankController');
const QueueController = require('./controllers/queueController');

const app = express();

// 미들웨어 설정
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 요청 로깅 미들웨어
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// 큐 및 서비스 초기화
const ollamaQueue = new OllamaQueue({
  maxSize: parseInt(process.env.QUEUE_SIZE) || 100,
  processInterval: parseInt(process.env.QUEUE_PROCESS_INTERVAL) || 1000
});

const ollamaService = new OllamaService(ollamaQueue);
const embeddingService = new EmbeddingService(ollamaQueue);
const rerankService = new RerankService(ollamaQueue);

// 컨트롤러 초기화
const embeddingController = new EmbeddingController(embeddingService);
const rerankController = new RerankController(rerankService);
const queueController = new QueueController(ollamaQueue);

// Swagger 문서화 설정
const swaggerMiddleware = new SwaggerMiddleware({
  embedding: embeddingService,
  rerank: rerankService,
  queue: ollamaQueue
});

// 기존 미들웨어 초기화
queue.init();
statusTracker.init();

// 라우터 등록
app.use('/api/ollama', ollamaRouter);
app.use('/api/metadata', metadataRouter);

// 새로운 서비스 라우터들
app.use('/api/embedding', createEmbeddingRouter(embeddingController));
app.use('/api/rerank', createRerankRouter(rerankController));
app.use('/api/queue', createQueueRouter(queueController));

// Swagger API 문서화 설정
swaggerMiddleware.setupSwagger(app);

// 기본 라우트
app.get('/', (req, res) => {
  res.json({
    name: 'Ollama Middleware Server',
    version: '1.0.0',
    status: 'running',
    services: ['ollama', 'metadata', 'embedding', 'rerank', 'queue'],
    timestamp: new Date().toISOString()
  });
});

// 통합 헬스 체크
app.get('/api/health', async (req, res) => {
  try {
    const [ollamaHealth, embeddingHealth, rerankHealth, queueHealth] = await Promise.allSettled([
      ollamaService.checkHealth(),
      embeddingService.getStatus(),
      rerankService.getStatus(),
      Promise.resolve(ollamaQueue.getStatus())
    ]);

    const health = {
      status: 'healthy',
      services: {
        ollama: ollamaHealth.status === 'fulfilled' ? ollamaHealth.value : { status: 'error', error: ollamaHealth.reason?.message },
        embedding: embeddingHealth.status === 'fulfilled' ? embeddingHealth.value : { status: 'error', error: embeddingHealth.reason?.message },
        rerank: rerankHealth.status === 'fulfilled' ? rerankHealth.value : { status: 'error', error: rerankHealth.reason?.message },
        queue: queueHealth.status === 'fulfilled' ? queueHealth.value : { status: 'error', error: queueHealth.reason?.message }
      },
      timestamp: new Date().toISOString()
    };

    // 전체 상태 결정
    const hasUnhealthyService = Object.values(health.services).some(service => 
      service.status === 'error' || (service.ollama && !service.ollama.connected)
    );

    if (hasUnhealthyService) {
      health.status = 'degraded';
      res.status(503);
    }

    res.json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 에러 처리 미들웨어
app.use((error, req, res, next) => {
  logger.error('Unhandled error', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// 404 처리
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    path: req.path
  });
});

// HTTP 서버 생성
const server = http.createServer(app);

// WebSocket 서버 설정
const wss = new WebSocket.Server({ 
  server,
  path: '/ws'
});

// WebSocket 연결 처리
wss.on('connection', (ws, req) => {
  logger.info('WebSocket client connected', {
    ip: req.socket.remoteAddress
  });
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      
      if (data.type === 'subscribe' && data.requestId) {
        const success = statusTracker.subscribe(data.requestId, ws);
        
        ws.send(JSON.stringify({
          type: 'subscribe_response',
          success,
          requestId: data.requestId
        }));
        
        // 현재 상태 즉시 전송
        const currentStatus = statusTracker.get(data.requestId);
        if (currentStatus) {
          ws.send(JSON.stringify({
            type: 'status_update',
            id: data.requestId,
            ...currentStatus
          }));
        }
      }
    } catch (error) {
      logger.error('WebSocket message parsing error', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format'
      }));
    }
  });
  
  ws.on('close', () => {
    logger.info('WebSocket client disconnected');
  });
  
  ws.on('error', (error) => {
    logger.error('WebSocket error', error);
  });
  
  // 연결 확인 메시지 전송
  ws.send(JSON.stringify({
    type: 'connected',
    timestamp: new Date().toISOString()
  }));
});

// 서버 시작
const PORT = process.env.PORT || 11501;
server.listen(PORT, () => {
  logger.info(`Ollama HTTP Middleware Server listening on port ${PORT}`);
  logger.info(`WebSocket endpoint: ws://localhost:${PORT}/ws`);
  logger.info(`REST API endpoint: http://localhost:${PORT}/api/ollama`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});
