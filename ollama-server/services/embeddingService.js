const axios = require('axios');

/**
 * Ollama Embedding 서비스
 * 텍스트를 벡터로 변환하는 서비스
 */
class EmbeddingService {
    constructor(ollamaQueue) {
        this.queue = ollamaQueue;
        this.ollamaHost = process.env.OLLAMA_HOST || 'localhost';
        this.ollamaPort = process.env.OLLAMA_PORT || '11434';
        this.defaultModel = process.env.DEFAULT_EMBEDDING_MODEL || 'nomic-embed-text';
        this.baseUrl = `http://${this.ollamaHost}:${this.ollamaPort}`;
    }
    
    /**
     * 단일 텍스트 임베딩
     * @param {string} text - 임베딩할 텍스트
     * @param {string} model - 사용할 모델 (옵션)
     * @returns {Promise<Array>} 임베딩 벡터
     */
    async embedText(text, model = null) {
        if (!text || typeof text !== 'string') {
            throw new Error('Valid text is required');
        }
        
        const embedModel = model || this.defaultModel;
        
        return await this.queue.add({
            type: 'embedding',
            data: { text, model: embedModel },
            processor: this.processEmbedding.bind(this),
            priority: 3
        });
    }
    
    /**
     * 여러 텍스트 배치 임베딩
     * @param {Array<string>} texts - 임베딩할 텍스트 배열
     * @param {string} model - 사용할 모델 (옵션)
     * @returns {Promise<Array<Array>>} 임베딩 벡터 배열
     */
    async embedBatch(texts, model = null) {
        if (!Array.isArray(texts) || texts.length === 0) {
            throw new Error('Valid text array is required');
        }
        
        const embedModel = model || this.defaultModel;
        
        // 배치 크기 제한 (메모리 고려)
        const batchSize = 10;
        const results = [];
        
        for (let i = 0; i < texts.length; i += batchSize) {
            const batch = texts.slice(i, i + batchSize);
            const batchPromises = batch.map(text => 
                this.queue.add({
                    type: 'embedding',
                    data: { text, model: embedModel },
                    processor: this.processEmbedding.bind(this),
                    priority: 3
                })
            );
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }
        
        return results;
    }
    
    /**
     * 임베딩 처리 함수
     * @param {Object} data - 처리할 데이터
     * @returns {Promise<Array>} 임베딩 벡터
     */
    async processEmbedding(data) {
        const { text, model } = data;
        
        try {
            const response = await axios.post(`${this.baseUrl}/api/embeddings`, {
                model,
                prompt: text
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.data || !response.data.embedding) {
                throw new Error('Invalid embedding response from Ollama');
            }
            
            return {
                text,
                model,
                embedding: response.data.embedding,
                dimensions: response.data.embedding.length,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            if (error.response) {
                throw new Error(`Ollama embedding error: ${error.response.data?.error || error.response.statusText}`);
            } else if (error.request) {
                throw new Error('Failed to connect to Ollama server');
            } else {
                throw new Error(`Embedding processing error: ${error.message}`);
            }
        }
    }
    
    /**
     * 문서 임베딩 (긴 텍스트를 청크로 분할)
     * @param {string} text - 긴 텍스트
     * @param {Object} options - 옵션
     * @returns {Promise<Array>} 청크별 임베딩 결과
     */
    async embedDocument(text, options = {}) {
        const {
            chunkSize = 512,
            overlap = 50,
            model = null
        } = options;
        
        if (!text || typeof text !== 'string') {
            throw new Error('Valid text is required');
        }
        
        const chunks = this.splitTextIntoChunks(text, chunkSize, overlap);
        const embedModel = model || this.defaultModel;
        
        const results = await Promise.all(
            chunks.map((chunk, index) => 
                this.queue.add({
                    type: 'embedding',
                    data: { text: chunk, model: embedModel },
                    processor: this.processEmbedding.bind(this),
                    priority: 4
                }).then(result => ({
                    ...result,
                    chunkIndex: index,
                    chunkStart: index * (chunkSize - overlap),
                    chunkEnd: Math.min(index * (chunkSize - overlap) + chunk.length, text.length)
                }))
            )
        );
        
        return {
            originalText: text,
            totalChunks: chunks.length,
            model: embedModel,
            chunks: results,
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * 텍스트를 청크로 분할
     * @param {string} text - 분할할 텍스트
     * @param {number} chunkSize - 청크 크기
     * @param {number} overlap - 중복 크기
     * @returns {Array<string>} 청크 배열
     */
    splitTextIntoChunks(text, chunkSize, overlap) {
        const chunks = [];
        let start = 0;
        
        while (start < text.length) {
            const end = Math.min(start + chunkSize, text.length);
            chunks.push(text.slice(start, end));
            
            if (end === text.length) break;
            start = end - overlap;
        }
        
        return chunks;
    }
    
    /**
     * 사용 가능한 임베딩 모델 목록 조회
     * @returns {Promise<Array>} 모델 목록
     */
    async getAvailableModels() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            
            // 임베딩 모델 필터링 (일반적인 임베딩 모델 패턴)
            const embeddingModels = response.data.models?.filter(model => 
                model.name.includes('embed') || 
                model.name.includes('embedding') ||
                model.name.includes('nomic') ||
                model.name.includes('bge')
            ) || [];
            
            return embeddingModels.map(model => ({
                name: model.name,
                size: model.size,
                modified: model.modified_at,
                isDefault: model.name === this.defaultModel
            }));
            
        } catch (error) {
            throw new Error(`Failed to get available models: ${error.message}`);
        }
    }
    
    /**
     * 서비스 상태 확인
     * @returns {Promise<Object>} 상태 정보
     */
    async getStatus() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            const queueStatus = this.queue.getStatus();
            
            return {
                service: 'embedding',
                ollama: {
                    connected: true,
                    host: this.ollamaHost,
                    port: this.ollamaPort,
                    modelsAvailable: response.data.models?.length || 0
                },
                defaultModel: this.defaultModel,
                queue: {
                    size: queueStatus.queueSize,
                    processing: queueStatus.processing,
                    stats: queueStatus.stats
                },
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            return {
                service: 'embedding',
                ollama: {
                    connected: false,
                    host: this.ollamaHost,
                    port: this.ollamaPort,
                    error: error.message
                },
                defaultModel: this.defaultModel,
                queue: this.queue.getStatus(),
                timestamp: new Date().toISOString()
            };
        }
    }
}

module.exports = EmbeddingService;
