/**
 * 고급 메타데이터 추출 서비스
 * - Ultrathink 설계 기반 JSON 구조화 출력
 * - HTTP/터미널 방식 모두 지원
 * - Few-shot prompting으로 정확도 향상
 */

const axios = require('axios');
const { spawn } = require('child_process');
const MetadataPromptTemplate = require('../utils/promptTemplate');
const logger = require('../utils/logger');
const config = require('../config/ollama');

class MetadataExtractionService {
  constructor() {
    this.promptTemplate = new MetadataPromptTemplate();
    this.defaultModel = config.model || 'gemma3:1b';
    this.httpUrl = `http://${config.host}:${config.port}`;
  }

  /**
   * HTTP API 방식으로 메타데이터 추출
   * @param {string} text - 분석할 텍스트
   * @param {Object} options - 추출 옵션
   * @returns {Promise<Object>} 추출된 메타데이터
   */
  async extractViaHttp(text, options = {}) {
    const {
      model = this.defaultModel,
      textType = 'plain',
      temperature = 0,
      maxRetries = 3
    } = options;

    logger.info('Starting HTTP metadata extraction', {
      textLength: text.length,
      model,
      textType
    });

    const prompt = this.promptTemplate.generateCustomPrompt(text, textType);
    const validation = this.promptTemplate.validatePrompt(prompt);
    
    if (!validation.isValid) {
      throw new Error(`Invalid prompt: ${validation.issues.join(', ')}`);
    }

    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.debug(`HTTP extraction attempt ${attempt}/${maxRetries}`);
        
        const response = await axios.post(`${this.httpUrl}/api/generate`, {
          model,
          prompt,
          temperature,
          stop: ['\n\n', 'Text:', 'END'],
          options: {
            num_predict: 200, // JSON 응답에 충분한 토큰
            top_p: 0.9,
            repeat_penalty: 1.1
          }
        }, {
          timeout: 60000 // 60초 타임아웃
        });

        if (response.data && response.data.response) {
          const result = this.parseJsonResponse(response.data.response, text);
          
          logger.info('HTTP metadata extraction successful', {
            attempt,
            title: result.title,
            category: result.category,
            keywordCount: result.keywords?.length
          });
          
          return result;
        }
        
      } catch (error) {
        lastError = error;
        logger.warn(`HTTP extraction attempt ${attempt} failed`, {
          error: error.message,
          response: error.response?.data
        });
        
        if (attempt < maxRetries) {
          await this.delay(1000 * attempt); // 재시도 딜레이
        }
      }
    }
    
    logger.error('HTTP metadata extraction failed after all retries', lastError);
    throw new Error(`HTTP extraction failed: ${lastError.message}`);
  }

  /**
   * 터미널 방식으로 메타데이터 추출
   * @param {string} text - 분석할 텍스트
   * @param {Object} options - 추출 옵션
   * @returns {Promise<Object>} 추출된 메타데이터
   */
  async extractViaTerminal(text, options = {}) {
    const {
      model = this.defaultModel,
      textType = 'plain',
      timeout = 60000
    } = options;

    logger.info('Starting terminal metadata extraction', {
      textLength: text.length,
      model,
      textType
    });

    const prompt = this.promptTemplate.generateCustomPrompt(text, textType);
    
    return new Promise((resolve, reject) => {
      const ollama = spawn('ollama', ['run', model], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let result = '';
      let error = '';
      let timeoutId;

      // 타임아웃 설정
      timeoutId = setTimeout(() => {
        ollama.kill('SIGTERM');
        reject(new Error(`Terminal extraction timeout after ${timeout}ms`));
      }, timeout);

      // 프롬프트 전송
      ollama.stdin.write(prompt);
      ollama.stdin.end();

      ollama.stdout.on('data', (data) => {
        result += data.toString();
      });

      ollama.stderr.on('data', (data) => {
        error += data.toString();
      });

      ollama.on('close', (code) => {
        clearTimeout(timeoutId);
        
        logger.debug('Terminal process closed', { code, resultLength: result.length });

        if (code === 0 && result.trim()) {
          try {
            const metadata = this.parseJsonResponse(result, text);
            
            logger.info('Terminal metadata extraction successful', {
              title: metadata.title,
              category: metadata.category,
              keywordCount: metadata.keywords?.length
            });
            
            resolve(metadata);
          } catch (parseError) {
            logger.error('Failed to parse terminal response', {
              error: parseError.message,
              response: result.substring(0, 500)
            });
            reject(parseError);
          }
        } else {
          const errorMsg = error || `Process exited with code ${code}`;
          logger.error('Terminal extraction failed', { code, error: errorMsg });
          reject(new Error(`Terminal extraction failed: ${errorMsg}`));
        }
      });

      ollama.on('error', (err) => {
        clearTimeout(timeoutId);
        logger.error('Terminal process error', err);
        reject(err);
      });
    });
  }

  /**
   * 자동 방식 선택 (터미널 우선, 실패시 HTTP)
   * @param {string} text - 분석할 텍스트
   * @param {Object} options - 추출 옵션
   * @returns {Promise<Object>} 추출된 메타데이터
   */
  async extractAuto(text, options = {}) {
    const { preferHttp = false } = options;
    
    logger.info('Starting auto metadata extraction', {
      textLength: text.length,
      preferHttp
    });

    try {
      if (preferHttp) {
        return await this.extractViaHttp(text, options);
      } else {
        return await this.extractViaTerminal(text, options);
      }
    } catch (primaryError) {
      logger.warn('Primary method failed, trying fallback', {
        primaryMethod: preferHttp ? 'HTTP' : 'Terminal',
        error: primaryError.message
      });

      try {
        if (preferHttp) {
          return await this.extractViaTerminal(text, options);
        } else {
          return await this.extractViaHttp(text, options);
        }
      } catch (fallbackError) {
        logger.error('Both extraction methods failed', {
          primaryError: primaryError.message,
          fallbackError: fallbackError.message
        });
        throw new Error(`All extraction methods failed. Primary: ${primaryError.message}, Fallback: ${fallbackError.message}`);
      }
    }
  }

  /**
   * JSON 응답 파싱 및 검증
   * @param {string} response - LLM 응답
   * @param {string} originalText - 원본 텍스트 (fallback용)
   * @returns {Object} 파싱된 메타데이터
   */
  parseJsonResponse(response, originalText) {
    logger.debug('Parsing JSON response', { responseLength: response.length });

    // JSON 추출 시도
    let jsonStr = response.trim();
    
    // JSON 블록 찾기
    const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonStr = jsonMatch[0];
    }

    try {
      const parsed = JSON.parse(jsonStr);
      
      // 필수 필드 검증 및 보정
      const result = {
        title: this.validateTitle(parsed.title, originalText),
        category: this.validateCategory(parsed.category),
        keywords: this.validateKeywords(parsed.keywords)
      };

      logger.debug('JSON parsing successful', result);
      return result;

    } catch (parseError) {
      logger.warn('JSON parsing failed, using fallback extraction', {
        error: parseError.message,
        response: response.substring(0, 200)
      });
      
      return this.extractFallbackMetadata(response, originalText);
    }
  }

  /**
   * 제목 검증 및 보정
   * @param {string} title - 추출된 제목
   * @param {string} originalText - 원본 텍스트
   * @returns {string} 검증된 제목
   */
  validateTitle(title, originalText) {
    if (typeof title === 'string' && title.trim().length > 0) {
      return title.trim().substring(0, 100); // 최대 100자
    }
    
    // Fallback: 첫 문장에서 제목 생성
    const firstSentence = originalText.split(/[.!?]/)[0];
    return firstSentence.substring(0, 50) + (firstSentence.length > 50 ? '...' : '');
  }

  /**
   * 카테고리 검증 및 보정
   * @param {string} category - 추출된 카테고리
   * @returns {string} 검증된 카테고리
   */
  validateCategory(category) {
    if (typeof category === 'string' && category.trim().length > 0) {
      const normalized = category.trim();
      
      // 정의된 카테고리 목록에서 매칭 시도
      const match = this.promptTemplate.categories.find(cat => 
        cat.toLowerCase() === normalized.toLowerCase()
      );
      
      return match || normalized;
    }
    
    return 'Other';
  }

  /**
   * 키워드 검증 및 보정
   * @param {Array} keywords - 추출된 키워드들
   * @returns {Array} 검증된 키워드 배열
   */
  validateKeywords(keywords) {
    if (!Array.isArray(keywords)) {
      return ['keyword1', 'keyword2', 'keyword3', 'keyword4', 'keyword5'];
    }
    
    const validKeywords = keywords
      .filter(k => typeof k === 'string' && k.trim().length > 0)
      .map(k => k.trim().toLowerCase())
      .slice(0, 5); // 최대 5개
    
    // 5개 미만이면 채우기
    while (validKeywords.length < 5) {
      validKeywords.push(`keyword${validKeywords.length + 1}`);
    }
    
    return validKeywords;
  }

  /**
   * Fallback 메타데이터 추출
   * @param {string} response - LLM 응답
   * @param {string} originalText - 원본 텍스트
   * @returns {Object} Fallback 메타데이터
   */
  extractFallbackMetadata(response, originalText) {
    logger.info('Using fallback metadata extraction');
    
    // 간단한 규칙 기반 추출
    const lines = response.split('\n').filter(line => line.trim());
    
    let title = 'Untitled Document';
    let category = 'Other';
    let keywords = ['text', 'content', 'document', 'information', 'data'];
    
    // 제목 찾기
    for (const line of lines) {
      if (line.toLowerCase().includes('title') && line.includes(':')) {
        const titleMatch = line.split(':')[1];
        if (titleMatch) {
          title = titleMatch.trim().replace(/['"]/g, '');
          break;
        }
      }
    }
    
    return { title, category, keywords };
  }

  /**
   * 딜레이 유틸리티
   * @param {number} ms - 딜레이 시간 (밀리초)
   * @returns {Promise} 딜레이 Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 서비스 헬스 체크
   * @returns {Promise<Object>} 헬스 상태
   */
  async healthCheck() {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      methods: {
        http: false,
        terminal: false
      }
    };

    // HTTP API 체크
    try {
      await axios.get(`${this.httpUrl}/api/tags`, { timeout: 5000 });
      health.methods.http = true;
    } catch (error) {
      logger.debug('HTTP API health check failed', error.message);
    }

    // 터미널 CLI 체크
    try {
      await new Promise((resolve, reject) => {
        const ollama = spawn('ollama', ['list'], { stdio: 'pipe' });
        ollama.on('close', (code) => {
          code === 0 ? resolve() : reject(new Error(`Exit code: ${code}`));
        });
        ollama.on('error', reject);
        setTimeout(() => reject(new Error('Timeout')), 5000);
      });
      health.methods.terminal = true;
    } catch (error) {
      logger.debug('Terminal CLI health check failed', error.message);
    }

    if (!health.methods.http && !health.methods.terminal) {
      health.status = 'unhealthy';
    }

    return health;
  }
}

module.exports = MetadataExtractionService;
