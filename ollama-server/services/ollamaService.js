const axios = require('axios');
const config = require('../config/ollama');
const logger = require('../utils/logger');

class OllamaService {
  constructor(ollamaQueue) {
    this.queue = ollamaQueue;
    this.host = config.host;
    this.port = config.port;
    this.basePath = config.basePath;
    this.baseUrl = `http://${this.host}:${this.port}${this.basePath}`;
  }

  async runModel(requestId, prompt, onProgress) {
    return await this.queue.add({
      type: 'chat',
      data: { requestId, prompt, onProgress },
      processor: this.processModel.bind(this),
      priority: 2
    });
  }

  async processModel(data) {
    const { requestId, prompt, onProgress } = data;
    const url = `${this.baseUrl}/generate`;
    
    logger.info(`Calling Ollama API: ${url}`, { requestId, model: prompt.model });
    
    try {
      const response = await axios.post(url, {
        model: prompt.model,
        prompt: prompt.text,
        stream: true
      }, {
        responseType: 'stream',
        timeout: 300000 // 5분 타임아웃
      });
      
      let fullResponse = '';
      
      response.data.on('data', (chunk) => {
        const chunkStr = chunk.toString();
        
        // Ollama는 각 줄이 JSON 객체로 구성됨
        const lines = chunkStr.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
          try {
            const data = JSON.parse(line);
            if (data.response) {
              fullResponse += data.response;
              onProgress(data.response);
            }
            
            // 완료 체크
            if (data.done) {
              logger.info(`Model generation completed: ${requestId}`);
              return fullResponse;
            }
          } catch (parseError) {
            logger.warn(`Failed to parse JSON line: ${line}`, parseError);
          }
        }
      });
      
      return new Promise((resolve, reject) => {
        response.data.on('end', () => {
          logger.info(`Stream ended for request: ${requestId}`);
          resolve(fullResponse);
        });
        
        response.data.on('error', (error) => {
          logger.error(`Stream error for request: ${requestId}`, error);
          reject(error);
        });
      });
      
    } catch (error) {
      logger.error(`Ollama API call failed: ${requestId}`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      });
      throw error;
    }
  }

  async listModels() {
    const url = `${this.baseUrl}/tags`;
    
    try {
      const response = await axios.get(url);
      return response.data.models || [];
    } catch (error) {
      logger.error('Failed to list models', error);
      throw error;
    }
  }

  async checkHealth() {
    const url = `${this.baseUrl}/tags`;
    
    try {
      await axios.get(url, { timeout: 5000 });
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error.message, 
        timestamp: new Date().toISOString() 
      };
    }
  }
}

module.exports = OllamaService;
