const axios = require('axios');

/**
 * Ollama Rerank 서비스
 * 검색 결과의 관련성을 재정렬하는 서비스
 */
class RerankService {
    constructor(ollamaQueue) {
        this.queue = ollamaQueue;
        this.ollamaHost = process.env.OLLAMA_HOST || 'localhost';
        this.ollamaPort = process.env.OLLAMA_PORT || '11434';
        this.defaultModel = process.env.DEFAULT_RERANK_MODEL || 'bge-reranker-base';
        this.baseUrl = `http://${this.ollamaHost}:${this.ollamaPort}`;
    }
    
    /**
     * 문서 리스트를 쿼리와의 관련성에 따라 재정렬
     * @param {string} query - 검색 쿼리
     * @param {Array<string|Object>} documents - 재정렬할 문서들
     * @param {Object} options - 옵션
     * @returns {Promise<Array>} 관련성 점수와 함께 정렬된 문서들
     */
    async rerank(query, documents, options = {}) {
        if (!query || typeof query !== 'string') {
            throw new Error('Valid query is required');
        }
        
        if (!Array.isArray(documents) || documents.length === 0) {
            throw new Error('Valid documents array is required');
        }
        
        const {
            model = this.defaultModel,
            topK = documents.length,
            returnDocuments = true,
            threshold = 0.0
        } = options;
        
        return await this.queue.add({
            type: 'rerank',
            data: { query, documents, model, topK, returnDocuments, threshold },
            processor: this.processRerank.bind(this),
            priority: 3
        });
    }
    
    /**
     * 배치 리랭킹 (여러 쿼리에 대해)
     * @param {Array<string>} queries - 검색 쿼리들
     * @param {Array<string|Object>} documents - 재정렬할 문서들
     * @param {Object} options - 옵션
     * @returns {Promise<Array>} 각 쿼리별 리랭킹 결과
     */
    async rerankBatch(queries, documents, options = {}) {
        if (!Array.isArray(queries) || queries.length === 0) {
            throw new Error('Valid queries array is required');
        }
        
        const results = await Promise.all(
            queries.map(query => this.rerank(query, documents, options))
        );
        
        return results.map((result, index) => ({
            query: queries[index],
            ...result
        }));
    }
    
    /**
     * 리랭킹 처리 함수
     * @param {Object} data - 처리할 데이터
     * @returns {Promise<Object>} 리랭킹 결과
     */
    async processRerank(data) {
        const { query, documents, model, topK, returnDocuments, threshold } = data;
        
        try {
            // 문서를 텍스트로 정규화
            const normalizedDocs = documents.map((doc, index) => ({
                index,
                text: typeof doc === 'string' ? doc : (doc.text || doc.content || String(doc)),
                original: doc
            }));
            
            // Ollama를 이용한 리랭킹 (generate API 사용)
            const prompt = this.createRerankPrompt(query, normalizedDocs.map(d => d.text));
            
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model,
                prompt,
                stream: false,
                format: 'json'
            }, {
                timeout: 60000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.data || !response.data.response) {
                throw new Error('Invalid rerank response from Ollama');
            }
            
            // JSON 응답 파싱
            let rankingResult;
            try {
                rankingResult = JSON.parse(response.data.response);
            } catch (parseError) {
                // Fallback: 간단한 점수 계산
                rankingResult = this.fallbackRanking(query, normalizedDocs);
            }
            
            // 결과 처리
            const rankedResults = this.processRankingResult(
                rankingResult, 
                normalizedDocs, 
                topK, 
                returnDocuments, 
                threshold
            );
            
            return {
                query,
                model,
                totalDocuments: documents.length,
                returnedDocuments: rankedResults.length,
                topK,
                threshold,
                results: rankedResults,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            if (error.response) {
                throw new Error(`Ollama rerank error: ${error.response.data?.error || error.response.statusText}`);
            } else if (error.request) {
                throw new Error('Failed to connect to Ollama server');
            } else {
                throw new Error(`Rerank processing error: ${error.message}`);
            }
        }
    }
    
    /**
     * 리랭킹 프롬프트 생성
     * @param {string} query - 검색 쿼리
     * @param {Array<string>} documents - 문서들
     * @returns {string} 프롬프트
     */
    createRerankPrompt(query, documents) {
        return `You are a document relevance ranker. Given a query and a list of documents, rank the documents by their relevance to the query.

Query: "${query}"

Documents:
${documents.map((doc, index) => `${index}: ${doc.substring(0, 500)}${doc.length > 500 ? '...' : ''}`).join('\n\n')}

Please provide a JSON response with the following format:
{
  "rankings": [
    {"index": 0, "score": 0.95, "reason": "Highly relevant because..."},
    {"index": 2, "score": 0.82, "reason": "Relevant because..."},
    ...
  ]
}

Rules:
- Score should be between 0.0 and 1.0
- Only include documents with score > 0.1
- Order by relevance score (highest first)
- Provide brief reason for each ranking
- Focus on semantic relevance to the query`;
    }
    
    /**
     * 리랭킹 결과 처리
     * @param {Object} rankingResult - Ollama 응답 결과
     * @param {Array} normalizedDocs - 정규화된 문서들
     * @param {number} topK - 반환할 최대 개수
     * @param {boolean} returnDocuments - 문서 내용 포함 여부
     * @param {number} threshold - 최소 점수 임계값
     * @returns {Array} 처리된 결과
     */
    processRankingResult(rankingResult, normalizedDocs, topK, returnDocuments, threshold) {
        if (!rankingResult.rankings || !Array.isArray(rankingResult.rankings)) {
            return this.fallbackRanking(null, normalizedDocs)
                .slice(0, topK)
                .filter(item => item.relevanceScore >= threshold);
        }
        
        return rankingResult.rankings
            .filter(item => item.score >= threshold)
            .slice(0, topK)
            .map(item => {
                const doc = normalizedDocs[item.index];
                if (!doc) return null;
                
                const result = {
                    index: item.index,
                    relevanceScore: item.score,
                    reason: item.reason || 'No reason provided'
                };
                
                if (returnDocuments) {
                    result.document = doc.original;
                }
                
                return result;
            })
            .filter(item => item !== null);
    }
    
    /**
     * 폴백 리랭킹 (단순 텍스트 매칭 기반)
     * @param {string} query - 검색 쿼리
     * @param {Array} normalizedDocs - 정규화된 문서들
     * @returns {Array} 기본 랭킹 결과
     */
    fallbackRanking(query, normalizedDocs) {
        if (!query) {
            // 쿼리가 없으면 원래 순서 유지
            return normalizedDocs.map((doc, index) => ({
                index,
                relevanceScore: 1.0 - (index * 0.01), // 약간씩 감소
                reason: 'Original order maintained',
                document: doc.original
            }));
        }
        
        const queryTerms = query.toLowerCase().split(/\s+/);
        
        return normalizedDocs
            .map((doc, index) => {
                const text = doc.text.toLowerCase();
                let score = 0;
                
                // 단순 키워드 매칭 점수 계산
                queryTerms.forEach(term => {
                    const matches = (text.match(new RegExp(term, 'g')) || []).length;
                    score += matches * 0.1;
                });
                
                // 텍스트 길이 고려 (너무 짧거나 긴 것 페널티)
                const lengthPenalty = Math.abs(text.length - 500) / 1000;
                score = Math.max(0, score - lengthPenalty);
                
                return {
                    index,
                    relevanceScore: Math.min(1.0, score),
                    reason: 'Simple keyword matching',
                    document: doc.original
                };
            })
            .sort((a, b) => b.relevanceScore - a.relevanceScore);
    }
    
    /**
     * 사용 가능한 리랭크 모델 목록 조회
     * @returns {Promise<Array>} 모델 목록
     */
    async getAvailableModels() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            
            // 리랭크 모델 필터링
            const rerankModels = response.data.models?.filter(model => 
                model.name.includes('rerank') || 
                model.name.includes('bge') ||
                model.name.includes('cross-encoder')
            ) || [];
            
            return rerankModels.map(model => ({
                name: model.name,
                size: model.size,
                modified: model.modified_at,
                isDefault: model.name === this.defaultModel
            }));
            
        } catch (error) {
            throw new Error(`Failed to get available models: ${error.message}`);
        }
    }
    
    /**
     * 서비스 상태 확인
     * @returns {Promise<Object>} 상태 정보
     */
    async getStatus() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            const queueStatus = this.queue.getStatus();
            
            return {
                service: 'rerank',
                ollama: {
                    connected: true,
                    host: this.ollamaHost,
                    port: this.ollamaPort,
                    modelsAvailable: response.data.models?.length || 0
                },
                defaultModel: this.defaultModel,
                queue: {
                    size: queueStatus.queueSize,
                    processing: queueStatus.processing,
                    stats: queueStatus.stats
                },
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            return {
                service: 'rerank',
                ollama: {
                    connected: false,
                    host: this.ollamaHost,
                    port: this.ollamaPort,
                    error: error.message
                },
                defaultModel: this.defaultModel,
                queue: this.queue.getStatus(),
                timestamp: new Date().toISOString()
            };
        }
    }
}

module.exports = RerankService;
