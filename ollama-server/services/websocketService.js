// WebSocket 서비스
// - 실시간 상태 브로드캐스트
// - 클라이언트 연결 관리
// - 큐 상태 및 처리 진행률 전송

const WebSocket = require('ws');
const logger = require('../utils/logger');

class WebSocketService {
  constructor(port = 7220) {
    this.port = port;
    this.server = null;
    this.clients = new Set();
    this.isRunning = false;
  }

  /**
   * WebSocket 서버 시작
   */
  start() {
    try {
      this.server = new WebSocket.Server({ 
        port: this.port,
        perMessageDeflate: false
      });

      this.server.on('connection', (ws, req) => {
        this.handleConnection(ws, req);
      });

      this.server.on('error', (error) => {
        logger.error('WebSocket server error:', error);
      });

      this.isRunning = true;
      logger.info(`WebSocket server started on port ${this.port}`);
      
      // 주기적으로 상태 브로드캐스트 (30초마다)
      this.startStatusBroadcast();
      
    } catch (error) {
      logger.error('Failed to start WebSocket server:', error);
      throw error;
    }
  }

  /**
   * WebSocket 서버 중지
   */
  stop() {
    this.isRunning = false;
    
    // 모든 클라이언트 연결 종료
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });
    this.clients.clear();

    // 서버 종료
    if (this.server) {
      this.server.close();
      this.server = null;
    }

    logger.info('WebSocket server stopped');
  }

  /**
   * 클라이언트 연결 처리
   */
  handleConnection(ws, req) {
    const clientInfo = `${req.socket.remoteAddress}:${req.socket.remotePort}`;
    logger.info(`WebSocket client connected: ${clientInfo}`);

    this.clients.add(ws);

    // 연결 확인 메시지 전송
    this.sendToClient(ws, {
      type: 'connection',
      status: 'connected',
      message: 'Connected to Ollama Server WebSocket',
      timestamp: Date.now()
    });

    // 메시지 수신 처리
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleClientMessage(ws, message);
      } catch (error) {
        logger.error(`Invalid JSON from client ${clientInfo}:`, error);
      }
    });

    // 연결 종료 처리
    ws.on('close', () => {
      this.clients.delete(ws);
      logger.info(`WebSocket client disconnected: ${clientInfo}`);
    });

    // 에러 처리
    ws.on('error', (error) => {
      logger.error(`WebSocket client error ${clientInfo}:`, error);
      this.clients.delete(ws);
    });
  }

  /**
   * 클라이언트 메시지 처리
   */
  handleClientMessage(ws, message) {
    const { type, data } = message;

    switch (type) {
      case 'ping':
        this.sendToClient(ws, {
          type: 'pong',
          timestamp: Date.now()
        });
        break;

      case 'subscribe':
        // 이벤트 구독 (향후 확장용)
        logger.debug('Client subscribed to events:', data?.events || []);
        break;

      case 'request_status':
        // 현재 상태 요청
        this.sendCurrentStatus(ws);
        break;

      default:
        logger.warning(`Unknown message type: ${type}`);
    }
  }

  /**
   * 클라이언트에 메시지 전송
   */
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error('Failed to send message to client:', error);
      }
    }
  }

  /**
   * 모든 클라이언트에 브로드캐스트
   */
  broadcast(message) {
    if (this.clients.size === 0) {
      return;
    }

    const messageStr = JSON.stringify({
      ...message,
      timestamp: Date.now()
    });

    const disconnectedClients = new Set();

    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(messageStr);
        } catch (error) {
          logger.error('Failed to broadcast message:', error);
          disconnectedClients.add(client);
        }
      } else {
        disconnectedClients.add(client);
      }
    });

    // 연결이 끊어진 클라이언트 제거
    disconnectedClients.forEach(client => {
      this.clients.delete(client);
    });

    if (disconnectedClients.size > 0) {
      logger.debug(`Removed ${disconnectedClients.size} disconnected clients`);
    }
  }

  /**
   * 큐 상태 브로드캐스트
   */
  broadcastQueueStatus(queueStatus) {
    this.broadcast({
      type: 'queue_status',
      data: queueStatus
    });
  }

  /**
   * 작업 진행률 브로드캐스트
   */
  broadcastProgress(requestId, progress) {
    this.broadcast({
      type: 'progress',
      data: {
        requestId,
        progress
      }
    });
  }

  /**
   * 작업 완료 브로드캐스트
   */
  broadcastCompletion(requestId, result) {
    this.broadcast({
      type: 'completion',
      data: {
        requestId,
        result
      }
    });
  }

  /**
   * 에러 브로드캐스트
   */
  broadcastError(requestId, error) {
    this.broadcast({
      type: 'error',
      data: {
        requestId,
        error: error.message || error
      }
    });
  }

  /**
   * 현재 상태 전송
   */
  async sendCurrentStatus(ws) {
    try {
      // 큐 상태 조회 (전역 큐 인스턴스 사용)
      const queueStatus = global.ollamaQueue ? global.ollamaQueue.getStatus() : null;
      
      const status = {
        type: 'status',
        data: {
          server: {
            running: this.isRunning,
            clients: this.clients.size,
            port: this.port
          },
          queue: queueStatus
        }
      };

      this.sendToClient(ws, status);
    } catch (error) {
      logger.error('Failed to send current status:', error);
    }
  }

  /**
   * 주기적 상태 브로드캐스트 시작
   */
  startStatusBroadcast() {
    const broadcastInterval = setInterval(() => {
      if (!this.isRunning) {
        clearInterval(broadcastInterval);
        return;
      }

      try {
        const queueStatus = global.ollamaQueue ? global.ollamaQueue.getStatus() : null;
        
        if (queueStatus) {
          this.broadcastQueueStatus(queueStatus);
        }
      } catch (error) {
        logger.error('Error in status broadcast:', error);
      }
    }, 30000); // 30초마다
  }

  /**
   * 서비스 통계
   */
  getStats() {
    return {
      running: this.isRunning,
      port: this.port,
      connectedClients: this.clients.size,
      serverActive: this.server !== null
    };
  }
}

module.exports = WebSocketService;
