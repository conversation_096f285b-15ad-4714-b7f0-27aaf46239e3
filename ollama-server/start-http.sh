#!/bin/bash

# HTTP API 서버만 시작하는 스크립트 (11501 포트)

echo "🚀 Starting Ollama HTTP API Server..."

# 환경 변수 로드
if [ -f .env ]; then
    source .env
fi

echo "📡 HTTP API Server will run on port ${PORT:-11501}"

# Node.js 확인
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

# 의존성 확인
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Ollama HTTP 서버 확인
echo "🔍 Checking Ollama HTTP server..."
ollama_health=$(curl -s -o /dev/null -w "%{http_code}" http://${OLLAMA_HOST:-localhost}:${OLLAMA_PORT:-11434}/api/tags 2>/dev/null)

if [ "$ollama_health" = "200" ]; then
    echo "✅ Ollama HTTP server is running"
else
    echo "⚠️  Ollama HTTP server is not responding"
fi

# HTTP API 서버 시작
if [ "$NODE_ENV" = "production" ]; then
    echo "🏭 Starting HTTP API server in production mode..."
    npm start
else
    echo "🔧 Starting HTTP API server in development mode..."
    npm run dev
fi
