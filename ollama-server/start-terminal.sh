#!/bin/bash

# 터미널 서버만 시작하는 스크립트 (11502 포트)

echo "💻 Starting Ollama Terminal Server..."

# 환경 변수 로드
if [ -f .env ]; then
    source .env
fi

echo "💻 Terminal Server will run on port ${TERMINAL_PORT:-11502}"

# Node.js 확인
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

# Ollama CLI 확인
if ! command -v ollama &> /dev/null; then
    echo "❌ Ollama CLI is not installed or not in PATH"
    echo "   Please install Ollama from https://ollama.ai"
    exit 1
fi

echo "✅ Ollama CLI is available"

# 의존성 확인
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# 필요한 모델 확인
REQUIRED_MODEL=${OLLAMA_MODEL:-"gemma3:1b"}
if ollama list | grep -q "$REQUIRED_MODEL"; then
    echo "✅ Required model $REQUIRED_MODEL is available"
else
    echo "📦 Required model $REQUIRED_MODEL not found. Pulling..."
    ollama pull "$REQUIRED_MODEL"
fi

# 터미널 서버 시작
if [ "$NODE_ENV" = "production" ]; then
    echo "🏭 Starting Terminal server in production mode..."
    npm run start:terminal
else
    echo "🔧 Starting Terminal server in development mode..."
    npm run dev:terminal
fi
