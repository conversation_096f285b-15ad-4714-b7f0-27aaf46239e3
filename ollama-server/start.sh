#!/bin/bash

# Ollama Middleware Server 시작 스크립트
# HTTP API 서버 (11501) + 터미널 서버 (11502) 동시 실행

echo "🚀 Starting Ollama Middleware Servers..."

# 환경 변수 확인
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one from .env.example"
    exit 1
fi

# 환경 변수 로드
source .env

echo "📡 HTTP API Server will run on port ${PORT:-11501}"
echo "💻 Terminal Server will run on port ${TERMINAL_PORT:-11502}"

# Node.js 버전 확인
node_version=$(node --version 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ Node.js is not installed"
    exit 1
fi

echo "✅ Node.js version: $node_version"

# 의존성 설치 확인
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Ollama CLI 설치 확인
if ! command -v ollama &> /dev/null; then
    echo "❌ Ollama CLI is not installed or not in PATH"
    echo "   Please install Ollama from https://ollama.ai"
    exit 1
fi

# Ollama 서버 상태 확인
echo "🔍 Checking Ollama server..."
ollama_health=$(curl -s -o /dev/null -w "%{http_code}" http://${OLLAMA_HOST:-localhost}:${OLLAMA_PORT:-11434}/api/tags 2>/dev/null)

if [ "$ollama_health" = "200" ]; then
    echo "✅ Ollama HTTP server is running"
else
    echo "⚠️  Ollama HTTP server is not responding (HTTP $ollama_health)"
    echo "   Make sure Ollama is running on ${OLLAMA_HOST:-localhost}:${OLLAMA_PORT:-11434}"
fi

# 필요한 모델 확인
REQUIRED_MODEL=${OLLAMA_MODEL:-"gemma3:1b"}
if ollama list | grep -q "$REQUIRED_MODEL"; then
    echo "✅ Required model $REQUIRED_MODEL is available"
else
    echo "📦 Required model $REQUIRED_MODEL not found. Pulling..."
    ollama pull "$REQUIRED_MODEL"
fi

# 서버 시작
if [ "$NODE_ENV" = "production" ]; then
    echo "� Starting in production mode..."
    npm run start:both
else
    echo "🔧 Starting in development mode..."
    npm run dev:both
fi
