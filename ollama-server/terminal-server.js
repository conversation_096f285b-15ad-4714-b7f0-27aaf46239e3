const express = require('express');
const { spawn } = require('child_process');
const cors = require('cors');

// 기존 라우터
const metadataRouter = require('./routes/metadata');

// 새로운 라우터
const createEmbeddingRouter = require('./routes/embedding');
const createRerankRouter = require('./routes/rerank');
const createQueueRouter = require('./routes/queue');

const logger = require('./utils/logger');

// 새로운 서비스들
const OllamaQueue = require('./utils/ollamaQueue');
const EmbeddingService = require('./services/embeddingService');
const RerankService = require('./services/rerankService');

// 컨트롤러들
const EmbeddingController = require('./controllers/embeddingController');
const RerankController = require('./controllers/rerankController');
const QueueController = require('./controllers/queueController');

const app = express();

// 미들웨어 설정
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 요청 로깅 미들웨어
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// 큐 및 서비스 초기화 (터미널 서버용)
const ollamaQueue = new OllamaQueue({
  maxSize: parseInt(process.env.QUEUE_SIZE) || 100,
  processInterval: parseInt(process.env.QUEUE_PROCESS_INTERVAL) || 1000
});

const embeddingService = new EmbeddingService(ollamaQueue);
const rerankService = new RerankService(ollamaQueue);

// 컨트롤러 초기화
const embeddingController = new EmbeddingController(embeddingService);
const rerankController = new RerankController(rerankService);
const queueController = new QueueController(ollamaQueue);

// 터미널 라우터
const terminalRouter = express.Router();

// 즉시 응답 (한 번에 결과 반환)
terminalRouter.post('/run', (req, res) => {
  const { model = 'gemma3:1b', prompt } = req.body;
  
  if (!prompt) {
    return res.status(400).json({
      error: 'Prompt is required',
      success: false
    });
  }

  logger.info(`Running Ollama terminal command: model=${model}, prompt_length=${prompt.length}`);
  
  const ollama = spawn('ollama', ['run', model], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let result = '';
  let error = '';

  // 프롬프트를 stdin으로 전송
  ollama.stdin.write(prompt);
  ollama.stdin.end();

  ollama.stdout.on('data', (data) => {
    result += data.toString();
  });

  ollama.stderr.on('data', (data) => {
    error += data.toString();
    logger.error(`Ollama stderr: ${data}`);
  });

  ollama.on('close', (code) => {
    logger.info(`Ollama process closed with code ${code}`);
    
    if (code === 0) {
      res.json({
        success: true,
        result: result.trim(),
        model,
        code
      });
    } else {
      res.status(500).json({
        success: false,
        error: error || 'Ollama process failed',
        code
      });
    }
  });

  ollama.on('error', (err) => {
    logger.error('Ollama process error', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  });

  // 클라이언트 연결 종료 시 프로세스 종료
  req.on('close', () => {
    if (!ollama.killed) {
      ollama.kill('SIGTERM');
      logger.info('Ollama process killed due to client disconnect');
    }
  });
});

// 실시간 스트리밍 (SSE 기반)
terminalRouter.get('/stream', (req, res) => {
  const { model = 'gemma3:1b', prompt = '' } = req.query;

  if (!prompt) {
    return res.status(400).json({
      error: 'Prompt parameter is required',
      success: false
    });
  }

  logger.info(`Starting Ollama terminal stream: model=${model}, prompt_length=${prompt.length}`);

  // SSE 헤더 설정
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');
  res.flushHeaders();

  // 연결 확인 메시지
  res.write(`event: connected\ndata: {"status":"connected","model":"${model}"}\n\n`);

  const ollama = spawn('ollama', ['run', model], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  // 프롬프트를 stdin으로 전송
  ollama.stdin.write(prompt);
  ollama.stdin.end();

  ollama.stdout.on('data', (data) => {
    const chunk = data.toString();
    res.write(`event: data\ndata: ${JSON.stringify({ chunk })}\n\n`);
  });

  ollama.stderr.on('data', (data) => {
    const error = data.toString();
    logger.error(`Ollama stderr: ${error}`);
    res.write(`event: error\ndata: ${JSON.stringify({ error })}\n\n`);
  });

  ollama.on('close', (code) => {
    logger.info(`Ollama stream process closed with code ${code}`);
    res.write(`event: end\ndata: ${JSON.stringify({ code, status: 'completed' })}\n\n`);
    res.end();
  });

  ollama.on('error', (err) => {
    logger.error('Ollama stream process error', err);
    res.write(`event: error\ndata: ${JSON.stringify({ error: err.message })}\n\n`);
    res.end();
  });

  // 클라이언트 연결 종료 시 프로세스 종료
  req.on('close', () => {
    if (!ollama.killed) {
      ollama.kill('SIGTERM');
      logger.info('Ollama stream process killed due to client disconnect');
    }
  });
});

// 헬스 체크
terminalRouter.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    type: 'terminal',
    timestamp: new Date().toISOString()
  });
});

// 사용 가능한 모델 목록 (터미널 명령으로 조회)
terminalRouter.get('/models', (req, res) => {
  const ollama = spawn('ollama', ['list']);
  
  let result = '';
  let error = '';

  ollama.stdout.on('data', (data) => {
    result += data.toString();
  });

  ollama.stderr.on('data', (data) => {
    error += data.toString();
  });

  ollama.on('close', (code) => {
    if (code === 0) {
      // 모델 목록을 파싱하여 JSON으로 반환
      const lines = result.trim().split('\n');
      const models = lines.slice(1).map(line => {
        const parts = line.trim().split(/\s+/);
        return {
          name: parts[0],
          id: parts[1] || '',
          size: parts[2] || '',
          modified: parts.slice(3).join(' ') || ''
        };
      }).filter(model => model.name);

      res.json({
        success: true,
        models,
        count: models.length
      });
    } else {
      res.status(500).json({
        success: false,
        error: error || 'Failed to list models'
      });
    }
  });
});

app.use('/api/ollama', terminalRouter);
app.use('/api/metadata', metadataRouter);

// 새로운 서비스 라우터들
app.use('/api/embedding', createEmbeddingRouter(embeddingController));
app.use('/api/rerank', createRerankRouter(rerankController));
app.use('/api/queue', createQueueRouter(queueController));

// 기본 라우트
app.get('/', (req, res) => {
  res.json({
    name: 'Ollama Terminal Server',
    version: '1.0.0',
    type: 'terminal',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      run: 'POST /api/ollama/run',
      stream: 'GET /api/ollama/stream',
      models: 'GET /api/ollama/models',
      health: 'GET /api/ollama/health'
    }
  });
});

// 에러 처리 미들웨어
app.use((error, req, res, next) => {
  logger.error('Unhandled error', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// 서버 시작
const PORT = process.env.TERMINAL_PORT || 11502;
app.listen(PORT, () => {
  logger.info(`Ollama Terminal Server listening on port ${PORT}`);
  logger.info(`Terminal API endpoint: http://localhost:${PORT}/api/ollama`);
  logger.info(`Streaming endpoint: http://localhost:${PORT}/api/ollama/stream`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down terminal server gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down terminal server gracefully');
  process.exit(0);
});

module.exports = app;
