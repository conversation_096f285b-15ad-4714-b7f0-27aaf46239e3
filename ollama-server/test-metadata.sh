#!/bin/bash

# Ollama 메타데이터 서버 테스트 스크립트
# - HTTP/터미널 서버 기능 테스트
# - Ultrathink 설계 검증

echo "🧪 Testing Ollama Metadata Servers..."

# 색상 정의
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 테스트 결과 카운터
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 테스트 함수
test_endpoint() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing $name... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url")
    else
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✓ PASSED${NC} (HTTP $http_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # JSON 응답인 경우 간단한 검증
        if echo "$body" | jq . >/dev/null 2>&1; then
            success=$(echo "$body" | jq -r '.success // false')
            if [ "$success" = "true" ]; then
                echo "  ↳ Response: success=true"
            else
                echo "  ↳ Response: success=false or missing"
            fi
        fi
    else
        echo -e "${RED}✗ FAILED${NC} (HTTP $http_code, expected $expected_status)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "  ↳ Response: $body"
    fi
    
    echo
}

# 서버 가용성 체크
check_server() {
    local name="$1"
    local url="$2"
    
    echo -n "Checking $name availability... "
    
    if curl -s --max-time 5 "$url" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Available${NC}"
        return 0
    else
        echo -e "${RED}✗ Not available${NC}"
        return 1
    fi
}

# 테스트 텍스트 샘플들
SAMPLE_TEXT_1='{"text": "New study reveals how sleep improves memory retention in elderly adults. Researchers found that quality sleep helps consolidate memories.", "textType": "plain", "model": "gemma3:1b"}'

SAMPLE_TEXT_2='{"text": "# AI Development Guide\n\n## Machine Learning Basics\n\n- Neural networks\n- Deep learning\n- Computer vision\n\n```python\nprint(\"Hello AI\")\n```", "textType": "markdown", "model": "gemma3:1b"}'

SAMPLE_TEXT_3='{"text": "Climate change impacts on global food security require immediate policy action to prevent worldwide hunger.", "textType": "plain"}'

BATCH_TEXT='{"texts": ["AI and machine learning trends", "Climate change research"], "method": "auto"}'

# 서버 상태 확인
echo "=== Server Availability Check ==="
HTTP_SERVER_AVAILABLE=false
TERMINAL_SERVER_AVAILABLE=false

if check_server "HTTP Server (11501)" "http://localhost:11501"; then
    HTTP_SERVER_AVAILABLE=true
fi

if check_server "Terminal Server (11502)" "http://localhost:11502"; then
    TERMINAL_SERVER_AVAILABLE=true
fi

echo

# HTTP 서버 테스트 (11501)
if [ "$HTTP_SERVER_AVAILABLE" = true ]; then
    echo "=== HTTP Server Tests (11501) ==="
    
    # 기본 정보
    test_endpoint "HTTP Server Info" "GET" "http://localhost:11501" "" 200
    
    # 메타데이터 API 정보
    test_endpoint "Metadata API Info" "GET" "http://localhost:11501/api/metadata" "" 200
    
    # 헬스 체크
    test_endpoint "Health Check" "GET" "http://localhost:11501/api/metadata/health" "" 200
    
    # 프롬프트 정보
    test_endpoint "Prompt Info" "GET" "http://localhost:11501/api/metadata/prompt-info" "" 200
    
    # HTTP 방식 메타데이터 추출
    test_endpoint "HTTP Metadata Extraction" "POST" "http://localhost:11501/api/metadata/extract/http" "$SAMPLE_TEXT_1" 200
    
    # 자동 방식 메타데이터 추출
    test_endpoint "Auto Metadata Extraction" "POST" "http://localhost:11501/api/metadata/extract" "$SAMPLE_TEXT_2" 200
    
    echo
else
    echo -e "${YELLOW}⚠️  HTTP Server tests skipped (server not available)${NC}"
    echo
fi

# 터미널 서버 테스트 (11502)
if [ "$TERMINAL_SERVER_AVAILABLE" = true ]; then
    echo "=== Terminal Server Tests (11502) ==="
    
    # 기본 정보
    test_endpoint "Terminal Server Info" "GET" "http://localhost:11502" "" 200
    
    # 메타데이터 API 정보  
    test_endpoint "Metadata API Info" "GET" "http://localhost:11502/api/metadata" "" 200
    
    # 헬스 체크
    test_endpoint "Health Check" "GET" "http://localhost:11502/api/metadata/health" "" 200
    
    # 터미널 방식 메타데이터 추출
    test_endpoint "Terminal Metadata Extraction" "POST" "http://localhost:11502/api/metadata/extract/terminal" "$SAMPLE_TEXT_1" 200
    
    # 자동 방식 메타데이터 추출
    test_endpoint "Auto Metadata Extraction" "POST" "http://localhost:11502/api/metadata/extract" "$SAMPLE_TEXT_3" 200
    
    # 배치 처리
    test_endpoint "Batch Metadata Extraction" "POST" "http://localhost:11502/api/metadata/extract/batch" "$BATCH_TEXT" 200
    
    # Ollama 기본 기능들
    test_endpoint "Ollama Models List" "GET" "http://localhost:11502/api/ollama/models" "" 200
    
    test_endpoint "Ollama Health" "GET" "http://localhost:11502/api/ollama/health" "" 200
    
    echo
else
    echo -e "${YELLOW}⚠️  Terminal Server tests skipped (server not available)${NC}"
    echo
fi

# 스트리밍 테스트 (별도 실행)
if [ "$TERMINAL_SERVER_AVAILABLE" = true ]; then
    echo "=== Streaming Test ==="
    echo -n "Testing SSE Streaming... "
    
    # SSE 스트림 테스트 (5초 타임아웃)
    stream_response=$(timeout 10s curl -s "http://localhost:11502/api/metadata/extract/stream?text=AI%20technology%20trends&model=gemma3:1b" | head -10)
    
    if echo "$stream_response" | grep -q "event:"; then
        echo -e "${GREEN}✓ PASSED${NC} (SSE events received)"
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAILED${NC} (No SSE events)"
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo
fi

# 성능 테스트
if [ "$TERMINAL_SERVER_AVAILABLE" = true ]; then
    echo "=== Performance Test ==="
    echo -n "Testing response time... "
    
    start_time=$(date +%s%N)
    curl -s -X POST -H "Content-Type: application/json" -d "$SAMPLE_TEXT_1" "http://localhost:11502/api/metadata/extract" >/dev/null
    end_time=$(date +%s%N)
    
    duration=$(((end_time - start_time) / 1000000)) # 밀리초 변환
    
    if [ "$duration" -lt 30000 ]; then # 30초 미만
        echo -e "${GREEN}✓ PASSED${NC} (${duration}ms)"
    else
        echo -e "${YELLOW}⚠ SLOW${NC} (${duration}ms)"
    fi
    
    echo
fi

# 결과 요약
echo "=== Test Results Summary ==="
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ "$FAILED_TESTS" -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}All tests passed!${NC}"
    exit 0
else
    echo -e "\n❌ ${RED}Some tests failed.${NC}"
    exit 1
fi
