#!/bin/bash

# Ollama 서버 embedding, rerank, queue 테스트 스크립트

BASE_URL="http://localhost:7218"

echo "🚀 Ollama 서버 새로운 서비스 테스트 시작"
echo "==================================================="

# 색상 정의
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 함수: API 테스트
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    
    echo -e "${BLUE}테스트: $name${NC}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
        echo -e "${GREEN}✅ 성공 (HTTP $http_code)${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ 실패 (HTTP $http_code)${NC}"
        echo "$body"
    fi
    echo ""
}

# 1. 전체 헬스 체크
echo -e "${YELLOW}1. 전체 서비스 헬스 체크${NC}"
test_api "전체 헬스 체크" "GET" "/api/health"

# 2. 큐 상태 확인
echo -e "${YELLOW}2. 큐 상태 및 관리${NC}"
test_api "큐 상태" "GET" "/api/queue/status"
test_api "큐 통계" "GET" "/api/queue/stats"
test_api "큐 헬스 체크" "GET" "/api/queue/health"

# 3. Embedding 서비스 테스트
echo -e "${YELLOW}3. Embedding 서비스 테스트${NC}"
test_api "Embedding 헬스 체크" "GET" "/api/embedding/health"
test_api "사용 가능한 Embedding 모델" "GET" "/api/embedding/models"

# 단일 텍스트 임베딩
test_api "단일 텍스트 임베딩" "POST" "/api/embedding/text" '{
    "text": "This is a test document for embedding",
    "model": "nomic-embed-text"
}'

# 배치 임베딩
test_api "배치 텍스트 임베딩" "POST" "/api/embedding/batch" '{
    "texts": [
        "First document for embedding",
        "Second document with different content",
        "Third document about technology"
    ],
    "model": "nomic-embed-text"
}'

# 문서 임베딩 (청크 분할)
test_api "문서 임베딩 (청크 분할)" "POST" "/api/embedding/document" '{
    "text": "This is a very long document that will be split into chunks for processing. It contains multiple sentences and paragraphs that need to be processed separately. The embedding service will automatically split this into manageable chunks and process each one individually. This approach is useful for large documents that exceed the model'\''s context window.",
    "chunkSize": 100,
    "overlap": 20,
    "model": "nomic-embed-text"
}'

# 4. Rerank 서비스 테스트
echo -e "${YELLOW}4. Rerank 서비스 테스트${NC}"
test_api "Rerank 헬스 체크" "GET" "/api/rerank/health"
test_api "사용 가능한 Rerank 모델" "GET" "/api/rerank/models"

# 문서 리랭킹
test_api "문서 리랭킹" "POST" "/api/rerank" '{
    "query": "machine learning algorithms",
    "documents": [
        "Deep learning is a subset of machine learning",
        "Cats are domestic animals that like to sleep",
        "Neural networks are used in artificial intelligence",
        "Cooking recipes for pasta dishes",
        "Supervised learning algorithms require labeled data"
    ],
    "model": "bge-reranker-base",
    "topK": 3,
    "threshold": 0.1
}'

# 점수만 반환하는 리랭킹
test_api "문서 점수 계산" "POST" "/api/rerank/score" '{
    "query": "programming languages",
    "documents": [
        "Python is a popular programming language",
        "JavaScript is used for web development",
        "Elephants are large mammals",
        "Java is an object-oriented language"
    ],
    "model": "bge-reranker-base"
}'

# 5. 큐 관리 테스트
echo -e "${YELLOW}5. 큐 관리 테스트${NC}"
test_api "대기 중인 작업 목록" "GET" "/api/queue/upcoming?limit=5"

# 큐에 여러 작업 추가 (동시에)
echo -e "${BLUE}여러 임베딩 작업을 동시에 실행하여 큐 테스트${NC}"

# 백그라운드에서 여러 요청 실행
for i in {1..5}; do
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"text\": \"Background test document $i for queue testing\"}" \
        "$BASE_URL/api/embedding/text" > /dev/null &
done

sleep 2

# 큐 상태 다시 확인
test_api "큐 상태 (작업 처리 중)" "GET" "/api/queue/status"

# 작업이 완료될 때까지 대기
echo -e "${BLUE}큐 작업 완료 대기 중...${NC}"
sleep 5

test_api "큐 상태 (작업 완료 후)" "GET" "/api/queue/stats"

# 6. 스트리밍 테스트 (간단한 확인)
echo -e "${YELLOW}6. 스트리밍 엔드포인트 확인${NC}"
echo -e "${BLUE}스트리밍 임베딩 엔드포인트 응답 확인${NC}"

timeout 10s curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"texts": ["Stream test 1", "Stream test 2"]}' \
    "$BASE_URL/api/embedding/stream" | head -n 5

echo ""
echo -e "${GREEN}🎉 모든 테스트 완료!${NC}"
echo "==================================================="
