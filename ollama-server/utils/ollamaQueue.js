const EventEmitter = require('events');
const { v4: uuidv4 } = require('uuid');

/**
 * Ollama 요청을 위한 큐 시스템
 * 순차적 처리를 통해 Ollama 서버의 부하를 관리
 */
class OllamaQueue extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.maxSize = options.maxSize || parseInt(process.env.QUEUE_SIZE) || 100;
        this.processInterval = options.processInterval || parseInt(process.env.QUEUE_PROCESS_INTERVAL) || 1000;
        
        this.queue = [];
        this.processing = false;
        this.currentJob = null;
        
        // 통계
        this.stats = {
            totalProcessed: 0,
            totalFailed: 0,
            averageProcessTime: 0,
            queueHighWaterMark: 0
        };
        
        this.startProcessor();
    }
    
    /**
     * 작업을 큐에 추가
     * @param {Object} job - 처리할 작업
     * @param {string} job.type - 작업 타입 (chat, embedding, rerank, metadata)
     * @param {Object} job.data - 작업 데이터
     * @param {Function} job.processor - 작업 처리 함수
     * @param {number} job.priority - 작업 우선순위 (낮을수록 높은 우선순위)
     * @returns {Promise} 작업 결과를 반환하는 Promise
     */
    async add(job) {
        return new Promise((resolve, reject) => {
            if (this.queue.length >= this.maxSize) {
                reject(new Error(`Queue is full (max: ${this.maxSize})`));
                return;
            }
            
            const queueItem = {
                id: uuidv4(),
                type: job.type,
                data: job.data,
                processor: job.processor,
                priority: job.priority || 5,
                createdAt: Date.now(),
                resolve,
                reject,
                retries: 0,
                maxRetries: job.maxRetries || 3
            };
            
            // 우선순위에 따라 삽입 위치 결정
            let insertIndex = this.queue.length;
            for (let i = 0; i < this.queue.length; i++) {
                if (this.queue[i].priority > queueItem.priority) {
                    insertIndex = i;
                    break;
                }
            }
            
            this.queue.splice(insertIndex, 0, queueItem);
            
            // 통계 업데이트
            if (this.queue.length > this.stats.queueHighWaterMark) {
                this.stats.queueHighWaterMark = this.queue.length;
            }
            
            this.emit('jobAdded', {
                id: queueItem.id,
                type: queueItem.type,
                queueSize: this.queue.length
            });
        });
    }
    
    /**
     * 큐 프로세서 시작
     */
    startProcessor() {
        if (this.processing) return;
        
        this.processing = true;
        this.processNext();
    }
    
    /**
     * 큐 프로세서 중지
     */
    stopProcessor() {
        this.processing = false;
        if (this.processingTimeout) {
            clearTimeout(this.processingTimeout);
        }
    }
    
    /**
     * 다음 작업 처리
     */
    async processNext() {
        if (!this.processing || this.queue.length === 0) {
            this.scheduleNext();
            return;
        }
        
        const job = this.queue.shift();
        this.currentJob = job;
        
        const startTime = Date.now();
        
        try {
            this.emit('jobStarted', {
                id: job.id,
                type: job.type,
                queueSize: this.queue.length
            });
            
            const result = await job.processor(job.data);
            
            const processingTime = Date.now() - startTime;
            this.updateAverageProcessTime(processingTime);
            
            job.resolve(result);
            this.stats.totalProcessed++;
            
            this.emit('jobCompleted', {
                id: job.id,
                type: job.type,
                processingTime,
                queueSize: this.queue.length
            });
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            
            // 재시도 로직
            if (job.retries < job.maxRetries) {
                job.retries++;
                this.queue.unshift(job); // 큐 앞쪽에 다시 추가
                
                this.emit('jobRetry', {
                    id: job.id,
                    type: job.type,
                    retries: job.retries,
                    maxRetries: job.maxRetries,
                    error: error.message
                });
            } else {
                job.reject(error);
                this.stats.totalFailed++;
                
                this.emit('jobFailed', {
                    id: job.id,
                    type: job.type,
                    processingTime,
                    error: error.message,
                    queueSize: this.queue.length
                });
            }
        } finally {
            this.currentJob = null;
        }
        
        this.scheduleNext();
    }
    
    /**
     * 다음 처리 스케줄링
     */
    scheduleNext() {
        if (this.processing) {
            this.processingTimeout = setTimeout(() => {
                this.processNext();
            }, this.processInterval);
        }
    }
    
    /**
     * 평균 처리 시간 업데이트
     */
    updateAverageProcessTime(processingTime) {
        const totalJobs = this.stats.totalProcessed + this.stats.totalFailed;
        if (totalJobs === 0) {
            this.stats.averageProcessTime = processingTime;
        } else {
            this.stats.averageProcessTime = 
                (this.stats.averageProcessTime * (totalJobs - 1) + processingTime) / totalJobs;
        }
    }
    
    /**
     * 큐 상태 조회
     */
    getStatus() {
        return {
            queueSize: this.queue.length,
            maxSize: this.maxSize,
            processing: this.processing,
            currentJob: this.currentJob ? {
                id: this.currentJob.id,
                type: this.currentJob.type,
                startedAt: this.currentJob.startedAt
            } : null,
            stats: { ...this.stats },
            upcomingJobs: this.queue.slice(0, 5).map(job => ({
                id: job.id,
                type: job.type,
                priority: job.priority,
                createdAt: job.createdAt
            }))
        };
    }
    
    /**
     * 특정 작업 취소
     */
    cancelJob(jobId) {
        const index = this.queue.findIndex(job => job.id === jobId);
        if (index !== -1) {
            const job = this.queue.splice(index, 1)[0];
            job.reject(new Error('Job cancelled'));
            
            this.emit('jobCancelled', {
                id: jobId,
                type: job.type,
                queueSize: this.queue.length
            });
            
            return true;
        }
        return false;
    }
    
    /**
     * 큐 초기화
     */
    clear() {
        const cancelledJobs = this.queue.length;
        
        this.queue.forEach(job => {
            job.reject(new Error('Queue cleared'));
        });
        
        this.queue = [];
        
        this.emit('queueCleared', {
            cancelledJobs
        });
        
        return cancelledJobs;
    }
}

module.exports = OllamaQueue;
