/**
 * Ollama 메타데이터 추출을 위한 고급 프롬프트 템플릿
 * - Few-shot prompting으로 정확도 향상
 * - JSON 구조화 출력 강제
 * - 다양한 카테고리 예시 제공
 */

class MetadataPromptTemplate {
  constructor() {
    this.systemPrompt = `You are an AI assistant that extracts structured metadata from text.

Output MUST be valid JSON with keys:
- "title": String (concise, descriptive title)
- "category": String (single category classification)
- "keywords": [String] (exactly 5 items, most relevant keywords)

Only output the JSON—nothing else.`;

    this.examples = [
      {
        text: "New study reveals how sleep improves memory retention in elderly adults.",
        result: {
          title: "Sleep Boosts Memory in Elderly",
          category: "Health",
          keywords: ["sleep", "memory", "elderly", "study", "cognitive health"]
        }
      },
      {
        text: "Top 10 programming languages in 2025: trends, salaries, and community support.",
        result: {
          title: "Top Programming Languages of 2025",
          category: "Technology",
          keywords: ["programming", "languages", "2025", "trends", "community"]
        }
      },
      {
        text: "Climate change impacts on global food security require immediate policy action.",
        result: {
          title: "Climate Change and Food Security",
          category: "Environment",
          keywords: ["climate change", "food security", "policy", "global", "environment"]
        }
      },
      {
        text: "Machine learning algorithms optimize financial trading strategies for maximum profit.",
        result: {
          title: "ML Algorithms in Financial Trading",
          category: "Finance",
          keywords: ["machine learning", "algorithms", "trading", "finance", "optimization"]
        }
      },
      {
        text: "Remote work productivity tips: managing time, communication, and work-life balance.",
        result: {
          title: "Remote Work Productivity Guide",
          category: "Business",
          keywords: ["remote work", "productivity", "time management", "communication", "work-life balance"]
        }
      }
    ];

    this.categories = [
      "Technology", "Health", "Business", "Science", "Education",
      "Finance", "Environment", "Politics", "Sports", "Entertainment",
      "Travel", "Food", "Lifestyle", "Art", "History", "Philosophy",
      "Psychology", "Medicine", "Engineering", "Marketing", "Other"
    ];
  }

  /**
   * 메타데이터 추출용 완전한 프롬프트 생성
   * @param {string} userText - 분석할 텍스트
   * @param {number} exampleCount - 사용할 예시 개수 (기본 3개)
   * @returns {string} 완성된 프롬프트
   */
  generatePrompt(userText, exampleCount = 3) {
    if (!userText || typeof userText !== 'string') {
      throw new Error('Valid text input is required');
    }

    // 텍스트 길이에 따라 예시 개수 조정
    const textLength = userText.length;
    if (textLength > 5000) {
      exampleCount = 2; // 긴 텍스트는 예시 축소
    } else if (textLength < 500) {
      exampleCount = 4; // 짧은 텍스트는 예시 증가
    }

    // 랜덤하게 예시 선택 (다양성 확보)
    const selectedExamples = this.getRandomExamples(exampleCount);
    
    let prompt = `${this.systemPrompt}\n\nEXAMPLES:\n`;
    
    selectedExamples.forEach(example => {
      prompt += `Text: "${example.text}"\nResult:\n${JSON.stringify(example.result, null, 2)}\n\n`;
    });
    
    prompt += `END EXAMPLES\n\n`;
    prompt += `Categories to choose from: ${this.categories.join(', ')}\n\n`;
    prompt += `Now process the text below and return JSON in the required format.\n\n`;
    prompt += `Text: "${userText.trim()}"\nResult:`;
    
    return prompt;
  }

  /**
   * 간단한 제목/카테고리만 추출하는 프롬프트
   * @param {string} userText - 분석할 텍스트
   * @returns {string} 간단한 프롬프트
   */
  generateSimplePrompt(userText) {
    return `Extract title and category from this text. Respond only with JSON:

Text: "${userText.trim()}"

Format:
{
  "title": "descriptive title",
  "category": "single category"
}

Result:`;
  }

  /**
   * 키워드만 추출하는 프롬프트
   * @param {string} userText - 분석할 텍스트
   * @param {number} keywordCount - 추출할 키워드 개수
   * @returns {string} 키워드 추출 프롬프트
   */
  generateKeywordPrompt(userText, keywordCount = 5) {
    return `Extract exactly ${keywordCount} most relevant keywords from this text. Respond only with JSON array:

Text: "${userText.trim()}"

Format:
{
  "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
}

Result:`;
  }

  /**
   * 랜덤 예시 선택
   * @param {number} count - 선택할 예시 개수
   * @returns {Array} 선택된 예시들
   */
  getRandomExamples(count) {
    const shuffled = [...this.examples].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, this.examples.length));
  }

  /**
   * 텍스트 타입에 따른 맞춤 프롬프트 생성
   * @param {string} userText - 분석할 텍스트
   * @param {string} textType - 텍스트 타입 (markdown, html, plain, etc.)
   * @returns {string} 맞춤 프롬프트
   */
  generateCustomPrompt(userText, textType = 'plain') {
    let customInstruction = '';
    
    switch (textType) {
      case 'markdown':
        customInstruction = 'This is markdown formatted text. Focus on the actual content, ignoring markdown syntax.';
        break;
      case 'html':
        customInstruction = 'This is HTML formatted text. Extract content meaning, ignoring HTML tags.';
        break;
      case 'code':
        customInstruction = 'This is code/technical content. Focus on the programming concepts and technologies.';
        break;
      case 'academic':
        customInstruction = 'This is academic/research content. Focus on the research topic and methodology.';
        break;
      default:
        customInstruction = 'This is general text content.';
    }

    const basePrompt = this.generatePrompt(userText, 2);
    return basePrompt.replace(this.systemPrompt, 
      `${this.systemPrompt}\n\nSpecial instruction: ${customInstruction}`);
  }

  /**
   * 프롬프트 검증
   * @param {string} prompt - 검증할 프롬프트
   * @returns {Object} 검증 결과
   */
  validatePrompt(prompt) {
    const validation = {
      isValid: true,
      issues: [],
      stats: {
        length: prompt.length,
        hasExamples: prompt.includes('EXAMPLES:'),
        hasSystemPrompt: prompt.includes('You are an AI assistant'),
        hasUserText: prompt.includes('Text:')
      }
    };

    if (prompt.length > 8000) {
      validation.issues.push('Prompt too long (>8000 chars)');
      validation.isValid = false;
    }

    if (!validation.stats.hasSystemPrompt) {
      validation.issues.push('Missing system prompt');
      validation.isValid = false;
    }

    if (!validation.stats.hasUserText) {
      validation.issues.push('Missing user text');
      validation.isValid = false;
    }

    return validation;
  }
}

module.exports = MetadataPromptTemplate;
