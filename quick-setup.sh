#!/bin/bash

# Vector DB Manager 프로젝트 빠른 설정 스크립트

set -e

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 함수 정의
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 프로젝트 이름 입력받기
if [ $# -eq 0 ]; then
    echo -e "${BLUE}🚀 Vector DB Manager 프로젝트 빠른 설정${NC}"
    echo ""
    read -p "프로젝트 이름을 입력하세요 (기본값: vector-db-manager): " PROJECT_NAME
    PROJECT_NAME=${PROJECT_NAME:-vector-db-manager}
else
    PROJECT_NAME=$1
fi

print_info "프로젝트 생성 중: $PROJECT_NAME"

# Node.js 확인
if ! command -v node &> /dev/null; then
    print_error "Node.js가 설치되어 있지 않습니다. Node.js를 먼저 설치해주세요."
    exit 1
fi

# Python 확인
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python이 설치되어 있지 않습니다. Python 3.11 이상을 설치해주세요."
    exit 1
fi

# 프로젝트 구조 생성
print_info "프로젝트 구조 생성 중..."
node create-project-structure.js ./$PROJECT_NAME

if [ $? -eq 0 ]; then
    print_success "프로젝트 구조 생성 완료"
else
    print_error "프로젝트 구조 생성 실패"
    exit 1
fi

# 프로젝트 디렉토리로 이동
cd $PROJECT_NAME

# Python 가상환경 생성
print_info "Python 가상환경 생성 중..."
if command -v python3 &> /dev/null; then
    python3 -m venv venv
else
    python -m venv venv
fi

# 가상환경 활성화
print_info "가상환경 활성화..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

# 패키지 설치
print_info "Python 패키지 설치 중..."
pip install --upgrade pip
pip install -r requirements.txt

# 환경 변수 파일 생성
print_info "환경 설정 파일 생성..."
cp .env.example .env

print_success "프로젝트 설정 완료!"
echo ""
echo -e "${BLUE}📋 다음 단계:${NC}"
echo "1. 프로젝트 디렉토리로 이동:"
echo -e "   ${GREEN}cd $PROJECT_NAME${NC}"
echo ""
echo "2. 가상환경 활성화:"
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo -e "   ${GREEN}venv\\Scripts\\activate${NC}"
else
    echo -e "   ${GREEN}source venv/bin/activate${NC}"
fi
echo ""
echo "3. 환경 변수 설정:"
echo -e "   ${GREEN}nano .env${NC}  # 또는 원하는 에디터 사용"
echo ""
echo "4. 데이터베이스 서비스 시작:"
echo -e "   ${GREEN}docker-compose up -d${NC}  # 기존 docker-compose.yml 사용"
echo ""
echo "5. 개발 서버 실행:"
echo -e "   ${GREEN}uvicorn app.main:app --reload${NC}"
echo -e "   또는 ${GREEN}./scripts/run_dev.sh${NC}"
echo ""
echo "6. API 문서 확인:"
echo -e "   ${GREEN}http://localhost:8000/docs${NC}"
echo ""
print_warning "각 파일의 TODO 주석을 확인하여 필요한 기능을 구현하세요!"
echo ""
print_info "Happy coding! 🎉"