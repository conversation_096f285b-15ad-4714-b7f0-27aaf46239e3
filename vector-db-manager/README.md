# Vector Database Manager

다양한 벡터 데이터베이스와 전통적인 데이터베이스를 통합 관리하는 FastAPI 기반 백엔드 시스템입니다.

## 지원하는 데이터베이스

### Vector Databases
- **Weaviate** - 오픈소스 벡터 데이터베이스
- **Qdrant** - 고성능 벡터 유사도 검색 엔진
- **Chroma** - AI 네이티브 오픈소스 임베딩 데이터베이스

### Traditional Databases
- **Elasticsearch** - 분산 검색 및 분석 엔진
- **Meilisearch** - 빠르고 관련성 높은 검색 엔진
- **MongoDB** - NoSQL 문서 데이터베이스

## 주요 기능

- 🔍 **통합 검색**: 모든 데이터베이스에서 통합 검색 기능
- 🎯 **벡터 유사도 검색**: 각 벡터 DB의 특화된 검색 기능
- 📊 **통합 대시보드**: 모든 DB의 상태 및 성능 모니터링
- 🔄 **데이터 동기화**: 데이터베이스 간 데이터 동기화
- 🚀 **비동기 처리**: 고성능 비동기 I/O 처리
- 🔐 **보안**: JWT 기반 인증 및 권한 관리

## 프로젝트 구조

```
vector-db-manager/
├── app/
│   ├── main.py                    # FastAPI 앱 진입점
│   ├── config.py                  # 설정 관리
│   ├── dependencies.py            # 의존성 주입
│   ├── models/                    # Pydantic 모델
│   │   ├── base.py               # 기본 모델 클래스
│   │   ├── request.py            # 요청 모델들
│   │   └── response.py           # 응답 모델들
│   ├── services/                  # 비즈니스 로직
│   │   ├── database_manager.py   # 통합 DB 관리
│   │   ├── vector_service.py     # 벡터 관련 서비스
│   │   └── search_service.py     # 통합 검색 서비스
│   ├── clients/                   # DB 클라이언트들
│   │   ├── base_client.py        # 기본 클라이언트 인터페이스
│   │   ├── weaviate_client.py    # Weaviate 클라이언트
│   │   ├── qdrant_client.py      # Qdrant 클라이언트
│   │   ├── chroma_client.py      # Chroma 클라이언트
│   │   ├── elasticsearch_client.py # Elasticsearch 클라이언트
│   │   ├── meilisearch_client.py # Meilisearch 클라이언트
│   │   └── mongodb_client.py     # MongoDB 클라이언트
│   ├── api/                       # API 라우터
│   │   ├── v1/
│   │   │   ├── endpoints/        # 개별 DB 엔드포인트들
│   │   │   │   ├── weaviate.py
│   │   │   │   ├── qdrant.py
│   │   │   │   ├── chroma.py
│   │   │   │   ├── elasticsearch.py
│   │   │   │   ├── meilisearch.py
│   │   │   │   ├── mongodb.py
│   │   │   │   └── unified.py    # 통합 검색 API
│   │   │   └── router.py
│   │   └── middleware.py
│   ├── core/                      # 핵심 기능
│   │   ├── exceptions.py         # 커스텀 예외 처리
│   │   ├── security.py           # 보안 관련 기능
│   │   └── logging.py            # 로깅 설정
│   └── utils/                     # 유틸리티
│       ├── helpers.py            # 도우미 함수들
│       └── validators.py         # 유효성 검사 함수들
├── tests/                         # 테스트 코드
├── docker/                        # Docker 설정
├── scripts/                       # 스크립트들
└── requirements.txt               # 의존성 목록
```

## 설치 및 실행

### 1. 프로젝트 구조 생성

```bash
# Node.js 스크립트로 프로젝트 구조 자동 생성
node create-project-structure.js <project-path>

# 예시
node create-project-structure.js ./vector-db-manager
```

### 2. 의존성 설치

```bash
cd vector-db-manager
pip install -r requirements.txt
```

### 3. 환경 설정

```bash
# .env 파일 생성
cp .env.example .env

# 환경 변수 설정
vim .env
```

### 4. 데이터베이스 실행

```bash
# Docker Compose로 모든 데이터베이스 실행
docker-compose up -d
```

### 5. 애플리케이션 실행

```bash
# 개발 모드 실행
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 또는 스크립트 사용
chmod +x scripts/run_dev.sh
./scripts/run_dev.sh
```

## API 문서

서버 실행 후 다음 URL에서 API 문서를 확인할 수 있습니다:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 환경 변수

```env
# 애플리케이션 설정
APP_NAME=Vector DB Manager
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# 데이터베이스 연결 정보
WEAVIATE_URL=http://localhost:7210
QDRANT_URL=http://localhost:7211
CHROMA_URL=http://localhost:7212
ELASTICSEARCH_URL=http://localhost:7213
MEILISEARCH_URL=http://localhost:7214
MONGODB_URL=mongodb://localhost:7215

# 보안 설정
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 개발 가이드

### 새로운 데이터베이스 클라이언트 추가

1. `app/clients/` 디렉토리에 새 클라이언트 파일 생성
2. `BaseClient` 클래스를 상속받아 구현
3. `app/services/database_manager.py`에 클라이언트 등록
4. API 엔드포인트 추가

### 테스트 실행

```bash
# 전체 테스트 실행
pytest

# 특정 테스트 실행
pytest tests/test_clients/test_weaviate_client.py

# 커버리지 리포트와 함께 실행
pytest --cov=app --cov-report=html
```

## 배포

### Docker를 사용한 전체 시스템 실행

#### 1. 데이터베이스 서비스들 시작
```bash
# 모든 데이터베이스 컨테이너 시작
cd docker/
docker-compose up -d

# 서비스 상태 확인
docker-compose ps
```

#### 2. FastAPI 애플리케이션 시작
```bash
# 프로젝트 루트에서 애플리케이션 시작
./start-app.sh

# 또는 수동으로
docker-compose up --build -d

# 로그 확인
docker-compose logs -f vectordb-manager
```

#### 3. 애플리케이션 중지
```bash
# 애플리케이션만 중지
./stop-app.sh

# 모든 서비스 중지
cd docker/
docker-compose down
```

### 개발 모드 실행

```bash
# 가상환경 활성화
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 또는 venv\Scripts\activate  # Windows

# 의존성 설치
pip install -r requirements-dev.txt

# 개발 서버 시작
./scripts/run_dev.sh
```

### 서비스 포트 정보

- **FastAPI App**: http://localhost:7200
- **Weaviate**: http://localhost:7210
- **Qdrant**: http://localhost:7211
- **ChromaDB**: http://localhost:7212
- **Elasticsearch**: http://localhost:7213
- **Meilisearch**: http://localhost:7214
- **MongoDB**: localhost:7215

### Docker 배포

```bash
# Docker 이미지 빌드
docker build -t vector-db-manager .

# 컨테이너 실행 (단독)
docker run -p 7200:8000 vector-db-manager
```

### 프로덕션 배포

```bash
# Gunicorn으로 실행
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 기여하기

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다. 자세한 내용은 `LICENSE` 파일을 참조하세요.

## 지원

문제가 발생하거나 질문이 있으시면 [Issues](https://github.com/your-username/vector-db-manager/issues)를 통해 문의해주세요.