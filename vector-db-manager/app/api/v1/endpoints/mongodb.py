# Notion 마크다운 문서 관리 API
# - Notion 문서 CRUD API
# - 자동 메타데이터 추출
# - 검색 및 필터링
# - 배치 처리

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from typing import List, Optional
from ....services.notion_service import NotionDocumentService
from ....services.database_manager import DatabaseManager
from ....dependencies import get_database_manager
from ....models.notion import (
    NotionDocument, NotionDocumentCreate, NotionDocumentUpdate,
    NotionSearchRequest, CategoryStats, KeywordStats,
    NotionTextCreate, TextConvertRequest, TextConvertResponse
)
from ....models.base import BaseResponse
from ....core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/notion", tags=["notion-documents"])

async def get_notion_service(
    db_manager: DatabaseManager = Depends(get_database_manager)
) -> NotionDocumentService:
    """Notion 서비스 의존성 주입"""
    mongodb_client = db_manager.get_client('mongodb')
    if not mongodb_client:
        raise HTTPException(status_code=503, detail="MongoDB client not available")
    
    service = NotionDocumentService(mongodb_client)
    await service.ensure_collection_setup()
    return service

@router.post("/documents", response_model=BaseResponse)
async def create_document(
    request: NotionDocumentCreate,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """새 Notion 문서 생성"""
    try:
        document = await service.create_document(request)
        if not document:
            raise HTTPException(status_code=400, detail="Failed to create document")
        
        return BaseResponse(
            success=True,
            message="Document created successfully",
            data=document.model_dump()
        )
    except Exception as e:
        logger.error("Failed to create document", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload", response_model=BaseResponse)
async def upload_markdown_file(
    file: UploadFile = File(...),
    auto_process: bool = Form(True),
    service: NotionDocumentService = Depends(get_notion_service)
):
    """마크다운 파일 업로드"""
    try:
        # 파일 검증
        if not file.filename.endswith('.md'):
            raise HTTPException(status_code=400, detail="Only markdown files are allowed")
        
        # 파일 내용 읽기
        content = await file.read()
        content_str = content.decode('utf-8')
        
        # 문서 생성 요청 구성
        request = NotionDocumentCreate(
            content=content_str,
            original_filename=file.filename,
            auto_process=auto_process
        )
        
        document = await service.create_document(request)
        if not document:
            raise HTTPException(status_code=400, detail="Failed to process uploaded file")
        
        return BaseResponse(
            success=True,
            message="File uploaded and processed successfully",
            data=document.model_dump()
        )
        
    except Exception as e:
        logger.error("Failed to upload file", filename=file.filename, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create-from-text", response_model=BaseResponse)
async def create_document_from_text(
    request: NotionTextCreate,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """텍스트로부터 Notion 문서 생성"""
    try:
        document = await service.create_document_from_text(request)
        if not document:
            raise HTTPException(status_code=400, detail="Failed to create document from text")
        
        return BaseResponse(
            success=True,
            message="Document created from text successfully",
            data=document.model_dump()
        )
    except Exception as e:
        logger.error("Failed to create document from text", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/convert-text", response_model=BaseResponse)
async def convert_text(
    request: TextConvertRequest
):
    """텍스트 형식 변환 (미리보기용)"""
    try:
        from ....utils.text_converter import smart_text_processor
        
        processing_result = smart_text_processor.process_text(
            request.text,
            force_conversion=request.force_conversion
        )
        
        response = TextConvertResponse(**processing_result)
        
        return BaseResponse(
            success=True,
            message="Text converted successfully",
            data=response.model_dump()
        )
        
    except Exception as e:
        logger.error("Failed to convert text", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}", response_model=BaseResponse)
async def get_document(
    document_id: str,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """문서 조회"""
    try:
        document = await service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return BaseResponse(
            success=True,
            data=document.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get document", document_id=document_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/documents/{document_id}", response_model=BaseResponse)
async def update_document(
    document_id: str,
    request: NotionDocumentUpdate,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """문서 수정"""
    try:
        document = await service.update_document(document_id, request)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return BaseResponse(
            success=True,
            message="Document updated successfully",
            data=document.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update document", document_id=document_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/documents/{document_id}", response_model=BaseResponse)
async def delete_document(
    document_id: str,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """문서 삭제"""
    try:
        success = await service.delete_document(document_id)
        if not success:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return BaseResponse(
            success=True,
            message="Document deleted successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete document", document_id=document_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search", response_model=BaseResponse)
async def search_documents(
    request: NotionSearchRequest,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """문서 검색"""
    try:
        documents, total = await service.search_documents(request)
        
        return BaseResponse(
            success=True,
            message=f"Found {total} documents",
            data={
                "documents": [doc.model_dump() for doc in documents],
                "total": total,
                "limit": request.limit,
                "offset": request.offset
            }
        )
    except Exception as e:
        logger.error("Failed to search documents", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/categories", response_model=BaseResponse)
async def get_categories(
    service: NotionDocumentService = Depends(get_notion_service)
):
    """카테고리 목록 조회"""
    try:
        categories = await service.get_categories()
        
        return BaseResponse(
            success=True,
            data={
                "categories": [cat.model_dump() for cat in categories],
                "total": len(categories)
            }
        )
    except Exception as e:
        logger.error("Failed to get categories", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/keywords", response_model=BaseResponse)
async def get_keywords(
    limit: int = 50,
    service: NotionDocumentService = Depends(get_notion_service)
):
    """키워드 목록 조회"""
    try:
        keywords = await service.get_keywords(limit)
        
        return BaseResponse(
            success=True,
            data={
                "keywords": [kw.model_dump() for kw in keywords],
                "total": len(keywords)
            }
        )
    except Exception as e:
        logger.error("Failed to get keywords", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-process", response_model=BaseResponse)
async def batch_process_folder(
    folder_path: str = Form(...),
    ollama_model: str = Form("gemma3:1b"),
    overwrite_existing: bool = Form(False),
    recursive: bool = Form(True),
    service: NotionDocumentService = Depends(get_notion_service)
):
    """폴더의 마크다운 파일들을 배치 처리"""
    try:
        from ....utils.markdown_utils import batch_processor
        from ....services.ollama_service import get_ollama_service
        import asyncio
        
        # 폴더 스캔
        file_paths = await batch_processor.scan_directory(folder_path, recursive)
        if not file_paths:
            raise HTTPException(status_code=400, detail="No markdown files found in the specified folder")
        
        # 처리 시간 추정
        estimation = await batch_processor.estimate_processing_time(len(file_paths))
        
        # 파일 읽기
        file_data_list = await batch_processor.process_files_batch(file_paths)
        
        # 중복 제거
        unique_files = batch_processor.filter_duplicates([f for f in file_data_list if f is not None])
        
        # 배치 처리 시작
        results = {
            "total_files": len(file_paths),
            "valid_files": len(unique_files),
            "processed": 0,
            "success": 0,
            "errors": [],
            "estimation": estimation
        }
        
        # 각 파일을 문서로 생성
        for file_data in unique_files:
            try:
                # 문서 생성 요청 구성
                request = NotionDocumentCreate(
                    content=file_data['content'],
                    original_filename=file_data['filename'],
                    auto_process=True
                )
                
                # 기존 제목이 있으면 사용
                if file_data.get('extracted_title'):
                    request.title = file_data['extracted_title']
                
                # 문서 생성
                document = await service.create_document(request)
                
                if document:
                    results["success"] += 1
                    logger.info("File processed successfully", filename=file_data['filename'])
                else:
                    results["errors"].append(f"Failed to create document for {file_data['filename']}")
                
                results["processed"] += 1
                
                # 시스템 부하 방지를 위한 짧은 대기
                await asyncio.sleep(0.5)
                
            except Exception as e:
                error_msg = f"Error processing {file_data['filename']}: {str(e)}"
                results["errors"].append(error_msg)
                logger.error("File processing error", filename=file_data['filename'], error=str(e))
                results["processed"] += 1
        
        return BaseResponse(
            success=True,
            message=f"Batch processing completed. {results['success']}/{results['processed']} files processed successfully.",
            data=results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Batch processing failed", folder_path=folder_path, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/processing-status", response_model=BaseResponse)
async def get_processing_status(
    service: NotionDocumentService = Depends(get_notion_service)
):
    """전체 문서 처리 상태 조회"""
    try:
        from ....models.notion import NotionSearchRequest, ProcessingStatus
        
        # 각 상태별 문서 수 조회
        status_counts = {}
        
        for status in ProcessingStatus:
            request = NotionSearchRequest(
                processing_status=status,
                limit=0  # 개수만 필요
            )
            _, count = await service.search_documents(request)
            status_counts[status.value] = count
        
        return BaseResponse(
            success=True,
            data={
                "status_counts": status_counts,
                "total_documents": sum(status_counts.values())
            }
        )
        
    except Exception as e:
        logger.error("Failed to get processing status", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
