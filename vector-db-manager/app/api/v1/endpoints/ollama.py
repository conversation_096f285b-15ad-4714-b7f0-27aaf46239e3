# Ollama 통합 API 엔드포인트
# - ollama-server의 모든 기능을 통합
# - frontend에서 단일 API로 접근
# - WebSocket을 통한 실시간 상태 전달

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import StreamingResponse
from typing import Dict, Any, List, Optional
import json
import asyncio
from ....services.ollama_service import get_ollama_service, OllamaService
from ....services.websocket_service import get_websocket_service, WebSocketService
from ....models.notion import OllamaExtractRequest, OllamaExtractResponse
from ....core.logging import get_logger
from ....models.base import BaseResponse

logger = get_logger(__name__)

router = APIRouter(prefix="/ollama", tags=["ollama"])

@router.get("/health")
async def health_check(
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """Ollama 서비스 전체 헬스 체크"""
    try:
        health_status = await ollama_service.health_check()
        return {
            "success": True,
            "data": health_status,
            "timestamp": health_status.get("timestamp")
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/models")
async def list_models(
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """사용 가능한 모델 목록 조회"""
    try:
        models = await ollama_service.list_models()
        return {
            "success": True,
            "data": {"models": models},
            "count": len(models)
        }
    except Exception as e:
        logger.error("Failed to list models", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to list models: {str(e)}")

@router.post("/extract", response_model=BaseResponse)
async def extract_metadata(
    request: OllamaExtractRequest,
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """메타데이터 추출"""
    try:
        result = await ollama_service.extract_metadata(request)
        
        if result is None:
            raise HTTPException(status_code=500, detail="Metadata extraction failed")
        
        return BaseResponse(
            success=True,
            message="Metadata extracted successfully",
            data=result.dict() if hasattr(result, 'dict') else result
        )
    except Exception as e:
        logger.error("Metadata extraction failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Metadata extraction failed: {str(e)}")

@router.post("/extract/batch")
async def extract_metadata_batch(
    requests: List[OllamaExtractRequest],
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """배치 메타데이터 추출"""
    try:
        results = []
        for request in requests:
            result = await ollama_service.extract_metadata(request)
            results.append({
                "content_preview": request.content[:100] + "..." if len(request.content) > 100 else request.content,
                "result": result.dict() if hasattr(result, 'dict') else result,
                "success": result is not None
            })
        
        successful_count = sum(1 for r in results if r["success"])
        
        return {
            "success": True,
            "message": f"Batch extraction completed: {successful_count}/{len(requests)} successful",
            "data": {
                "results": results,
                "total": len(requests),
                "successful": successful_count,
                "failed": len(requests) - successful_count
            }
        }
    except Exception as e:
        logger.error("Batch metadata extraction failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Batch extraction failed: {str(e)}")

@router.post("/embedding")
async def create_embedding(
    text: str,
    model: str = "nomic-embed-text",
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """텍스트 임베딩 생성"""
    try:
        result = await ollama_service.create_embedding(text, model)
        
        if result is None:
            raise HTTPException(status_code=500, detail="Embedding creation failed")
        
        return {
            "success": True,
            "message": "Embedding created successfully",
            "data": result
        }
    except Exception as e:
        logger.error("Embedding creation failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Embedding creation failed: {str(e)}")

@router.post("/rerank")
async def rerank_documents(
    query: str,
    documents: List[str],
    model: str = "bge-reranker-base",
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """문서 재정렬"""
    try:
        result = await ollama_service.rerank_documents(query, documents, model)
        
        if result is None:
            raise HTTPException(status_code=500, detail="Document reranking failed")
        
        return {
            "success": True,
            "message": "Documents reranked successfully",
            "data": result
        }
    except Exception as e:
        logger.error("Document reranking failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Document reranking failed: {str(e)}")

@router.get("/queue/status")
async def get_queue_status(
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """큐 상태 조회"""
    try:
        status = await ollama_service.get_queue_status()
        
        if status is None:
            raise HTTPException(status_code=500, detail="Failed to get queue status")
        
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error("Failed to get queue status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get queue status: {str(e)}")

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    websocket_service: WebSocketService = Depends(get_websocket_service)
):
    """WebSocket 엔드포인트 - 실시간 상태 업데이트"""
    await websocket.accept()
    
    # 클라이언트를 WebSocket 서비스에 등록
    websocket_service.clients.add(websocket)
    
    try:
        # 연결 확인 메시지 전송
        await websocket.send_text(json.dumps({
            "type": "connection",
            "status": "connected",
            "message": "Connected to Vector DB Manager WebSocket",
            "timestamp": asyncio.get_event_loop().time()
        }))
        
        # 현재 상태 전송
        await websocket_service.send_current_status(websocket)
        
        # 메시지 수신 대기
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 메시지 타입별 처리
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": asyncio.get_event_loop().time()
                    }))
                elif message.get("type") == "request_status":
                    await websocket_service.send_current_status(websocket)
                else:
                    logger.warning(f"Unknown WebSocket message type: {message.get('type')}")
                    
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error("WebSocket message processing error", error=str(e))
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Message processing error: {str(e)}"
                }))
                
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error("WebSocket connection error", error=str(e))
    finally:
        # 클라이언트 제거
        websocket_service.clients.discard(websocket)

@router.get("/websocket/stats")
async def get_websocket_stats(
    websocket_service: WebSocketService = Depends(get_websocket_service)
):
    """WebSocket 서비스 통계"""
    try:
        stats = websocket_service.get_stats()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error("Failed to get WebSocket stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get WebSocket stats: {str(e)}")

@router.post("/test/connection")
async def test_connection(
    ollama_service: OllamaService = Depends(get_ollama_service)
):
    """연결 테스트"""
    try:
        # 헬스 체크
        health = await ollama_service.health_check()
        
        # 모델 목록 조회
        models = await ollama_service.list_models()
        
        # 큐 상태 조회
        queue_status = await ollama_service.get_queue_status()
        
        return {
            "success": True,
            "message": "Connection test completed",
            "data": {
                "health": health,
                "models": models,
                "queue": queue_status,
                "test_timestamp": asyncio.get_event_loop().time()
            }
        }
    except Exception as e:
        logger.error("Connection test failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Connection test failed: {str(e)}")

@router.get("/")
async def ollama_info():
    """Ollama 통합 API 정보"""
    return {
        "success": True,
        "message": "Ollama Integration API",
        "description": "Unified API for all Ollama services",
        "endpoints": {
            "health": "GET /health - Service health check",
            "models": "GET /models - List available models",
            "extract": "POST /extract - Extract metadata",
            "extract_batch": "POST /extract/batch - Batch metadata extraction",
            "embedding": "POST /embedding - Create text embedding",
            "rerank": "POST /rerank - Rerank documents",
            "queue": "GET /queue/status - Queue status",
            "websocket": "WS /ws - Real-time updates",
            "test": "POST /test/connection - Connection test"
        },
        "version": "1.0.0"
    }
