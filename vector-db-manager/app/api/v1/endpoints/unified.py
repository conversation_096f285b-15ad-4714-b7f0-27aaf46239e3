# 통합 검색 API 엔드포인트
# - 모든 데이터베이스 통합 검색
# - 결과 통합 및 랭킹
# - 필터링 및 정렬

from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from ....models.request import UnifiedSearchRequest, VectorSearchRequest
from ....models.response import UnifiedSearchResponse, DatabaseStatusResponse, VectorSearchResponse
from ....models.base import BaseResponse
from ....services.database_manager import DatabaseManager
from ....services.search_service import SearchService
from ....dependencies import get_database_manager, get_search_service
from ....core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/unified", tags=["unified"])

@router.get("/health", response_model=DatabaseStatusResponse)
async def health_check_all(
    db_manager: DatabaseManager = Depends(get_database_manager)
):
    """모든 데이터베이스의 헬스체크를 수행합니다."""
    try:
        health_data = await db_manager.health_check()
        
        return DatabaseStatusResponse(
            success=True,
            message="Health check completed",
            data=health_data,
            databases=health_data['databases'],
            healthy_count=health_data['healthy_count'],
            total_count=health_data['total_count']
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search", response_model=UnifiedSearchResponse)
async def unified_search(
    request: UnifiedSearchRequest,
    search_service: SearchService = Depends(get_search_service)
):
    """통합 검색을 수행합니다."""
    try:
        logger.info("Unified search request", query=request.query, databases=request.databases)
        
        # 검색 타입에 따른 분기
        if request.search_type == "vector" and hasattr(request, 'vector'):
            # 벡터 검색 (벡터가 제공된 경우)
            search_result = await search_service.vector_search(
                vector=request.vector,
                databases=request.databases,
                limit=request.limit
            )
        else:
            # 텍스트 검색
            search_result = await search_service.unified_search(
                query=request.query,
                databases=request.databases,
                limit=request.limit
            )
        
        return UnifiedSearchResponse(
            success=True,
            message="Search completed successfully",
            results=search_result['results'],
            total=search_result['total'],
            query=request.query,
            search_time=search_result['search_time'],
            databases_searched=search_result['databases_searched'],
            database_results=search_result['database_results']
        )
    except Exception as e:
        logger.error("Unified search failed", query=request.query, error=str(e))
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.post("/vector-search", response_model=VectorSearchResponse)
async def vector_search(
    request: VectorSearchRequest,
    search_service: SearchService = Depends(get_search_service)
):
    """벡터 검색을 수행합니다."""
    try:
        logger.info("Vector search request", vector_dim=len(request.vector))
        
        search_result = await search_service.vector_search(
            vector=request.vector,
            limit=request.limit
        )
        
        return VectorSearchResponse(
            success=True,
            message="Vector search completed successfully",
            results=search_result['results'],
            total=search_result['total'],
            search_time=search_result['search_time']
        )
    except Exception as e:
        logger.error("Vector search failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Vector search failed: {str(e)}")

@router.get("/databases", response_model=BaseResponse)
async def list_databases(
    db_manager: DatabaseManager = Depends(get_database_manager)
):
    """사용 가능한 모든 데이터베이스 목록을 반환합니다."""
    try:
        clients = db_manager.get_all_clients()
        database_info = []
        
        for name, client in clients.items():
            stats = await client.get_stats()
            database_info.append({
                'name': name,
                'type': 'vector' if name in ['weaviate', 'qdrant', 'chroma'] else 'traditional',
                'connected': stats.get('connected', False),
                'url': stats.get('url', '')
            })
        
        return BaseResponse(
            success=True,
            message="Database list retrieved successfully",
            data={
                'databases': database_info,
                'total_count': len(database_info)
            }
        )
    except Exception as e:
        logger.error("Failed to list databases", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
