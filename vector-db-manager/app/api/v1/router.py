# v1 API 라우터 설정
# - 모든 엔드포인트 라우터 통합
# - 버전별 라우팅 관리
# - 공통 응답 처리

from fastapi import APIRouter
from .endpoints import unified, mongodb

router = APIRouter()  # prefix 제거 (main.py에서 이미 /api/v1 설정됨)

# 통합 검색 엔드포인트 포함
router.include_router(unified.router)

# Notion 문서 관리 엔드포인트
router.include_router(mongodb.router)

# 추가 개별 데이터베이스 엔드포인트들 (향후 확장용)
# router.include_router(weaviate.router)
# router.include_router(qdrant.router)
# router.include_router(elasticsearch.router)
