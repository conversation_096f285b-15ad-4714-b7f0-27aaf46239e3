# 기본 클라이언트 인터페이스
# - 추상 기본 클래스 정의
# - 공통 메서드 인터페이스
# - 에러 처리 기본 구조

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import time
from ..core.logging import get_logger

logger = get_logger(__name__)

class BaseClient(ABC):
    """모든 데이터베이스 클라이언트의 기본 인터페이스"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__.replace('Client', '').lower()
        self.url = config.get('url', '')
        self.client = None
        self._connected = False
        
    @abstractmethod
    async def connect(self) -> bool:
        """데이터베이스에 연결합니다."""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """데이터베이스 연결을 해제합니다."""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """헬스체크를 수행합니다."""
        pass
    
    @abstractmethod
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다."""
        pass
    
    async def vector_search(self, vector: List[float], limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """벡터 검색을 수행합니다. (벡터 DB에서만 구현)"""
        raise NotImplementedError("Vector search not supported for this database type")
    
    async def insert(self, documents: List[Dict[str, Any]]) -> bool:
        """문서를 삽입합니다."""
        raise NotImplementedError("Insert not implemented")
    
    async def delete(self, document_id: str) -> bool:
        """문서를 삭제합니다."""
        raise NotImplementedError("Delete not implemented")
    
    async def get_stats(self) -> Dict[str, Any]:
        """데이터베이스 통계를 반환합니다."""
        return {
            "name": self.name,
            "connected": self._connected,
            "url": self.url
        }
    
    def _measure_time(self, start_time: float) -> float:
        """실행 시간을 측정합니다 (밀리초)."""
        return (time.time() - start_time) * 1000
