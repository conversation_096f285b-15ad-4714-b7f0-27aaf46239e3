# Chroma 클라이언트 구현
# - Chroma 연결 및 설정
# - 컬렉션 관리
# - 벡터 검색 구현
# - 임베딩 처리

from .base_client import BaseClient
from typing import Dict, Any, List
import time
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..core.logging import get_logger

logger = get_logger(__name__)

# ChromaDB import 시도 및 에러 처리
try:
    import chromadb
    CHROMADB_AVAILABLE = True
except ImportError as e:
    logger.warning("ChromaDB not available", error=str(e))
    CHROMADB_AVAILABLE = False
    chromadb = None

class ChromaClient(BaseClient):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.collection_name = config.get('collection_name', 'default_collection')
        
    async def connect(self) -> bool:
        """Chroma에 연결합니다."""
        if not CHROMADB_AVAILABLE:
            raise DatabaseConnectionError("Chroma", "ChromaDB package not available")
            
        try:
            # HTTP 클라이언트 설정
            url_parts = self.url.replace('http://', '').replace('https://', '').split(':')
            host = url_parts[0]
            port = int(url_parts[1]) if len(url_parts) > 1 else 8000
            
            # ChromaDB 클라이언트 생성
            try:
                # 단순 연결 시도 (멀티테넌시 비활성화 상태)
                self.client = chromadb.HttpClient(host=host, port=port)
                
                # 연결 테스트 - 컬렉션 리스트 조회로 연결 확인
                collections = self.client.list_collections()
                self._connected = True
                logger.info("Connected to ChromaDB successfully", 
                           host=host, port=port, collections_count=len(collections))
                return True
                
            except Exception as http_error:
                logger.debug("Standard HTTP connection failed, trying with settings", error=str(http_error))
                
                # 설정을 명시적으로 지정하여 연결 시도
                try:
                    settings = chromadb.config.Settings(
                        allow_reset=True,
                        anonymized_telemetry=False
                    )
                    self.client = chromadb.HttpClient(
                        host=host, 
                        port=port,
                        settings=settings
                    )
                    
                    # 연결 테스트
                    collections = self.client.list_collections()
                    self._connected = True
                    logger.info("Connected to ChromaDB with explicit settings", 
                               host=host, port=port, collections_count=len(collections))
                    return True
                    
                except Exception as settings_error:
                    logger.debug("Settings-based connection failed", error=str(settings_error))
                    
                    # 최후 수단: EphemeralClient 사용 (메모리 내 전용)
                    try:
                        logger.warning("Falling back to EphemeralClient (in-memory only)")
                        self.client = chromadb.EphemeralClient()
                        self._connected = True
                        logger.info("Using ChromaDB EphemeralClient (data will not persist)")
                        return True
                        
                    except Exception as ephemeral_error:
                        error_msg = (
                            f"All ChromaDB connection methods failed. "
                            f"HTTP error: {str(http_error)}, "
                            f"Settings error: {str(settings_error)}, "
                            f"Ephemeral error: {str(ephemeral_error)}"
                        )
                        logger.error("ChromaDB connection completely failed", error=error_msg)
                        raise DatabaseConnectionError("Chroma", error_msg)
                        
        except Exception as e:
            logger.error("Failed to connect to Chroma", url=self.url, error=str(e))
            raise DatabaseConnectionError("Chroma", str(e))
    
    async def disconnect(self) -> None:
        """연결을 해제합니다."""
        if self.client:
            self.client = None
            self._connected = False
    
    async def health_check(self) -> Dict[str, Any]:
        """Chroma 헬스체크를 수행합니다."""
        start_time = time.time()
        
        if not CHROMADB_AVAILABLE:
            response_time = self._measure_time(start_time)
            return {
                "name": "chroma",
                "type": "vector",
                "status": "unavailable",
                "url": self.url,
                "response_time": response_time,
                "error": "ChromaDB package not available"
            }
        
        try:
            if not self.client:
                await self.connect()
            
            # 1. v2 heartbeat API 시도 (최신 방식)
            try:
                import requests
                url_parts = self.url.replace('http://', '').replace('https://', '').split(':')
                host = url_parts[0]
                port = int(url_parts[1]) if len(url_parts) > 1 else 8000
                
                v2_url = f"http://{host}:{port}/api/v2/heartbeat"
                response = requests.get(v2_url, timeout=5)
                
                if response.status_code == 200:
                    response_time = self._measure_time(start_time)
                    heartbeat_data = response.json()
                    
                    return {
                        "name": "chroma",
                        "type": "vector",
                        "status": "healthy",
                        "url": self.url,
                        "response_time": response_time,
                        "api_version": "v2",
                        "heartbeat": heartbeat_data
                    }
            except Exception as v2_error:
                logger.debug("v2 heartbeat failed, trying fallback", error=str(v2_error))
            
            # 2. Client heartbeat 시도 (중간 방식)
            try:
                heartbeat_result = self.client.heartbeat()
                response_time = self._measure_time(start_time)
                
                return {
                    "name": "chroma",
                    "type": "vector",
                    "status": "healthy",
                    "url": self.url,
                    "response_time": response_time,
                    "api_version": "client_heartbeat",
                    "heartbeat": heartbeat_result
                }
            except (AttributeError, Exception) as client_error:
                logger.debug("Client heartbeat failed, trying collection list", error=str(client_error))
            
            # 3. Collection 리스트 조회 (fallback 방식)
            collections = self.client.list_collections()
            response_time = self._measure_time(start_time)
            
            return {
                "name": "chroma",
                "type": "vector",
                "status": "healthy",
                "url": self.url,
                "response_time": response_time,
                "api_version": "collection_list",
                "collections_count": len(collections)
            }
                
        except Exception as e:
            response_time = self._measure_time(start_time)
            logger.error("Chroma health check failed", error=str(e))
            return {
                "name": "chroma",
                "type": "vector",
                "status": "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "error": str(e)
            }
    
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다."""
        if not CHROMADB_AVAILABLE:
            raise SearchError("ChromaDB package not available")
            
        try:
            collection = self.client.get_collection(self.collection_name)
            
            # Chroma에서 텍스트 검색
            results = collection.query(
                query_texts=[query],
                n_results=limit
            )
            
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    search_results.append({
                        "id": results['ids'][0][i] if results['ids'] else str(i),
                        "content": doc,
                        "score": 1.0 - results['distances'][0][i] if results['distances'] else 0.0,
                        "metadata": results['metadatas'][0][i] if results['metadatas'] and results['metadatas'][0] else {},
                        "source": "chroma"
                    })
            
            return search_results
        except Exception as e:
            logger.error("Chroma search failed", query=query, error=str(e))
            raise SearchError(f"Chroma search failed: {str(e)}")
    
    async def vector_search(self, vector: List[float], limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """벡터 검색을 수행합니다."""
        if not CHROMADB_AVAILABLE:
            raise SearchError("ChromaDB package not available")
            
        try:
            collection = self.client.get_collection(self.collection_name)
            
            results = collection.query(
                query_embeddings=[vector],
                n_results=limit
            )
            
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    search_results.append({
                        "id": results['ids'][0][i] if results['ids'] else str(i),
                        "content": doc,
                        "similarity": 1.0 - results['distances'][0][i] if results['distances'] else 0.0,
                        "metadata": results['metadatas'][0][i] if results['metadatas'] and results['metadatas'][0] else {},
                        "source": "chroma"
                    })
            
            return search_results
        except Exception as e:
            logger.error("Chroma vector search failed", error=str(e))
            raise SearchError(f"Chroma vector search failed: {str(e)}")
