# Elasticsearch 클라이언트 구현
# - Elasticsearch 연결 및 설정
# - 인덱스 관리
# - 전문 검색 구현
# - 집계 쿼리 처리

from .base_client import BaseClient
from elasticsearch import AsyncElasticsearch
from typing import Dict, Any, List
import time
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..core.logging import get_logger

logger = get_logger(__name__)

class ElasticsearchClient(BaseClient):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.index_name = config.get('index_name', 'documents')
        
    async def connect(self) -> bool:
        """Elasticsearch에 연결합니다."""
        try:
            self.client = AsyncElasticsearch([self.url])
            self._connected = True
            return True
        except Exception as e:
            logger.error("Failed to connect to Elasticsearch", url=self.url, error=str(e))
            raise DatabaseConnectionError("Elasticsearch", str(e))
    
    async def disconnect(self) -> None:
        """연결을 해제합니다."""
        if self.client:
            await self.client.close()
            self.client = None
            self._connected = False
    
    async def health_check(self) -> Dict[str, Any]:
        """Elasticsearch 헬스체크를 수행합니다."""
        start_time = time.time()
        try:
            if not self.client:
                await self.connect()
            
            # Elasticsearch 클러스터 상태 확인 (최신 방식)
            health_response = await self.client.cluster.health(wait_for_status="yellow", timeout="5s")
            response_time = self._measure_time(start_time)
            
            # 상태 판별
            cluster_status = health_response.get("status", "red")
            is_healthy = cluster_status in ["green", "yellow"]
            
            return {
                "name": "elasticsearch",
                "type": "traditional",
                "status": "healthy" if is_healthy else "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "cluster_status": cluster_status,
                "nodes": health_response.get("number_of_nodes", 0)
            }
        except Exception as e:
            response_time = self._measure_time(start_time)
            logger.error("Elasticsearch health check failed", error=str(e))
            return {
                "name": "elasticsearch",
                "type": "traditional",
                "status": "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "error": str(e)
            }
    
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다."""
        try:
            search_body = {
                "query": {
                    "multi_match": {
                        "query": query,
                        "fields": ["content", "title", "description"]
                    }
                },
                "size": limit
            }
            
            # 필터가 있다면 추가
            filters = kwargs.get('filters')
            if filters:
                search_body["query"] = {
                    "bool": {
                        "must": [search_body["query"]],
                        "filter": [{"term": {k: v}} for k, v in filters.items()]
                    }
                }
            
            response = await self.client.search(index=self.index_name, body=search_body)
            
            search_results = []
            for hit in response['hits']['hits']:
                search_results.append({
                    "id": hit['_id'],
                    "content": hit['_source'].get('content', ''),
                    "score": hit['_score'],
                    "metadata": {k: v for k, v in hit['_source'].items() if k != 'content'},
                    "source": "elasticsearch"
                })
            
            return search_results
        except Exception as e:
            logger.error("Elasticsearch search failed", query=query, error=str(e))
            raise SearchError(f"Elasticsearch search failed: {str(e)}")
    
    async def insert(self, documents: List[Dict[str, Any]]) -> bool:
        """문서를 삽입합니다."""
        try:
            for doc in documents:
                doc_id = doc.get('id', None)
                if doc_id:
                    await self.client.index(index=self.index_name, id=doc_id, body=doc)
                else:
                    await self.client.index(index=self.index_name, body=doc)
            
            # 인덱스 새로고침
            await self.client.indices.refresh(index=self.index_name)
            return True
        except Exception as e:
            logger.error("Elasticsearch insert failed", error=str(e))
            return False
