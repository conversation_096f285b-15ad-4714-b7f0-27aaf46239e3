# Meilisearch 클라이언트 구현
# - Meilisearch 연결 및 설정
# - 인덱스 관리
# - 전문 검색 구현
# - 패싯 검색 지원

from .base_client import BaseClient
import meilisearch
from typing import Dict, Any, List
import time
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..core.logging import get_logger

logger = get_logger(__name__)

class MeilisearchClient(BaseClient):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.index_name = config.get('index_name', 'documents')
        self.api_key = config.get('api_key', None)
        
    async def connect(self) -> bool:
        """Meilisearch에 연결합니다."""
        try:
            self.client = meilisearch.Client(self.url, self.api_key)
            self._connected = True
            return True
        except Exception as e:
            logger.error("Failed to connect to Meilisearch", url=self.url, error=str(e))
            raise DatabaseConnectionError("<PERSON>lisearch", str(e))
    
    async def disconnect(self) -> None:
        """연결을 해제합니다."""
        if self.client:
            self.client = None
            self._connected = False
    
    async def health_check(self) -> Dict[str, Any]:
        """Meilisearch 헬스체크를 수행합니다."""
        start_time = time.time()
        try:
            if not self.client:
                await self.connect()
            
            # Meilisearch 최신 방식 상태 확인
            health_info = self.client.health()
            response_time = self._measure_time(start_time)
            
            # status가 'available'이면 healthy
            is_healthy = health_info.get('status') == 'available'
            
            return {
                "name": "meilisearch",
                "type": "traditional",
                "status": "healthy" if is_healthy else "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "health_info": health_info
            }
        except Exception as e:
            response_time = self._measure_time(start_time)
            logger.error("Meilisearch health check failed", error=str(e))
            return {
                "name": "meilisearch",
                "type": "traditional",
                "status": "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "error": str(e)
            }
    
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다."""
        try:
            index = self.client.index(self.index_name)
            
            search_params = {
                'limit': limit,
                'attributesToRetrieve': ['*'],
                'attributesToHighlight': ['content', 'title']
            }
            
            # 필터가 있다면 추가
            filters = kwargs.get('filters')
            if filters:
                search_params['filter'] = filters
            
            results = index.search(query, search_params)
            
            search_results = []
            for hit in results['hits']:
                search_results.append({
                    "id": str(hit.get('id', '')),
                    "content": hit.get('content', ''),
                    "score": hit.get('_rankingScore', 0.0),
                    "metadata": {k: v for k, v in hit.items() if k not in ['id', 'content', '_rankingScore', '_formatted']},
                    "source": "meilisearch"
                })
            
            return search_results
        except Exception as e:
            logger.error("Meilisearch search failed", query=query, error=str(e))
            raise SearchError(f"Meilisearch search failed: {str(e)}")
    
    async def insert(self, documents: List[Dict[str, Any]]) -> bool:
        """문서를 삽입합니다."""
        try:
            index = self.client.index(self.index_name)
            
            # 문서 ID가 없는 경우 생성
            for i, doc in enumerate(documents):
                if 'id' not in doc:
                    doc['id'] = f"doc_{i}_{int(time.time())}"
            
            task = index.add_documents(documents)
            return True
        except Exception as e:
            logger.error("Meilisearch insert failed", error=str(e))
            return False
