# MongoDB 클라이언트 구현
# - MongoDB 연결 및 설정
# - 컬렉션 관리
# - 문서 검색 구현
# - 집계 파이프라인

from .base_client import BaseClient
from motor.motor_asyncio import AsyncIOMotorClient
from typing import Dict, Any, List
import time
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..core.logging import get_logger

logger = get_logger(__name__)

class MongoDBClient(BaseClient):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.database_name = config.get('database_name', 'vector_db')
        self.collection_name = config.get('collection_name', 'documents')
        
    async def connect(self) -> bool:
        """MongoDB에 연결합니다."""
        try:
            # URL에서 인증 정보를 제거하고 연결 시도
            import re
            from urllib.parse import urlparse, urlunparse
            
            parsed = urlparse(self.url)
            # 인증 정보가 있다면 제거하고 다시 시도
            if '@' in parsed.netloc:
                # 인증 정보 제거
                host_port = parsed.netloc.split('@')[-1]
                clean_url = urlunparse((
                    parsed.scheme,
                    host_port,
                    parsed.path,
                    parsed.params,
                    parsed.query,
                    parsed.fragment
                ))
            else:
                clean_url = self.url
            
            # MongoDB 인증 없이 연결 시도
            self.client = AsyncIOMotorClient(
                clean_url, 
                serverSelectionTimeoutMS=5000  # 5초 타임아웃
            )
            self._connected = True
            return True
        except Exception as e:
            logger.error("Failed to connect to MongoDB", url=self.url, error=str(e))
            raise DatabaseConnectionError("MongoDB", str(e))
    
    async def disconnect(self) -> None:
        """연결을 해제합니다."""
        if self.client:
            self.client.close()
            self.client = None
            self._connected = False
    
    async def health_check(self) -> Dict[str, Any]:
        """MongoDB 헬스체크를 수행합니다."""
        start_time = time.time()
        try:
            if not self.client:
                await self.connect()
            
            # MongoDB 최신 방식: ping 명령 사용
            await self.client.admin.command('ping')
            response_time = self._measure_time(start_time)
            
            # 서버 정보도 함께 가져오기
            server_info = await self.client.server_info()
            
            return {
                "name": "mongodb",
                "type": "traditional",
                "status": "healthy",
                "url": self.url,
                "response_time": response_time,
                "version": server_info.get("version", "unknown")
            }
        except Exception as e:
            response_time = self._measure_time(start_time)
            logger.error("MongoDB health check failed", error=str(e))
            return {
                "name": "mongodb",
                "type": "traditional",
                "status": "unhealthy", 
                "url": self.url,
                "response_time": response_time,
                "error": str(e)
            }
    
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다."""
        try:
            db = self.client[self.database_name]
            collection = db[self.collection_name]
            
            # 텍스트 검색 쿼리
            cursor = collection.find(
                {"$text": {"$search": query}},
                {"score": {"$meta": "textScore"}}
            ).sort([("score", {"$meta": "textScore"})]).limit(limit)
            
            results = []
            async for doc in cursor:
                results.append({
                    "id": str(doc["_id"]),
                    "content": doc.get("content", ""),
                    "score": doc.get("score", 0.0),
                    "metadata": doc.get("metadata", {}),
                    "source": "mongodb"
                })
            
            return results
        except Exception as e:
            logger.error("MongoDB search failed", query=query, error=str(e))
            raise SearchError(f"MongoDB search failed: {str(e)}")

    async def insert(self, documents: List[Dict[str, Any]]) -> bool:
        """문서를 삽입합니다."""
        try:
            if not self.client:
                await self.connect()
            
            db = self.client[self.database_name]
            collection = db[self.collection_name]
            
            if len(documents) == 1:
                result = await collection.insert_one(documents[0])
                return bool(result.inserted_id)
            else:
                result = await collection.insert_many(documents)
                return len(result.inserted_ids) == len(documents)
                
        except Exception as e:
            logger.error("MongoDB insert failed", error=str(e))
            return False
    
    async def delete(self, document_id: str) -> bool:
        """문서를 삭제합니다."""
        try:
            if not self.client:
                await self.connect()
            
            db = self.client[self.database_name]
            collection = db[self.collection_name]
            
            from bson import ObjectId
            result = await collection.delete_one({"_id": ObjectId(document_id)})
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error("MongoDB delete failed", document_id=document_id, error=str(e))
            return False
