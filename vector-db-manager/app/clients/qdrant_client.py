# Qdrant 클라이언트 구현
# - Qdrant 연결 및 설정
# - 컬렉션 관리
# - 벡터 검색 구현
# - 포인트 관리

from .base_client import BaseClient
from qdrant_client import QdrantClient
from qdrant_client.http import models
from typing import Dict, Any, List
import time
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..core.logging import get_logger

logger = get_logger(__name__)

class QdrantClientWrapper(BaseClient):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.collection_name = config.get('collection_name', 'default_collection')
        
    async def connect(self) -> bool:
        """Qdrant에 연결합니다."""
        try:
            self.client = QdrantClient(url=self.url)
            self._connected = True
            return True
        except Exception as e:
            logger.error("Failed to connect to Qdrant", url=self.url, error=str(e))
            raise DatabaseConnectionError("Qdrant", str(e))
    
    async def disconnect(self) -> None:
        """연결을 해제합니다."""
        if self.client:
            self.client.close()
            self.client = None
            self._connected = False
    
    async def health_check(self) -> Dict[str, Any]:
        """Qdrant 헬스체크를 수행합니다."""
        start_time = time.time()
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                # Qdrant 최신 헬스체크 엔드포인트 사용
                health_url = f"{self.url}/livez"
                async with session.get(health_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        response_time = self._measure_time(start_time)
                        
                        return {
                            "name": "qdrant",
                            "type": "vector", 
                            "status": "healthy",
                            "url": self.url,
                            "response_time": response_time,
                            "ready": True
                        }
                    else:
                        raise Exception(f"Health check failed with status {response.status}")
        except Exception as e:
            response_time = self._measure_time(start_time)
            logger.error("Qdrant health check failed", error=str(e))
            return {
                "name": "qdrant",
                "type": "vector",
                "status": "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "error": str(e)
            }
    
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다. (Qdrant는 주로 벡터 검색용)"""
        # Qdrant는 기본적으로 벡터 검색 엔진이므로 텍스트 검색은 제한적
        logger.warning("Text search not optimal for Qdrant, consider using vector_search")
        return []
    
    async def vector_search(self, vector: List[float], limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """벡터 검색을 수행합니다."""
        try:
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=vector,
                limit=limit
            )
            
            return [
                {
                    "id": str(hit.id),
                    "content": hit.payload.get("content", ""),
                    "similarity": hit.score,
                    "metadata": hit.payload.get("metadata", {}),
                    "source": "qdrant"
                }
                for hit in search_result
            ]
        except Exception as e:
            logger.error("Qdrant vector search failed", error=str(e))
            raise SearchError(f"Qdrant vector search failed: {str(e)}")
    
    async def insert(self, documents: List[Dict[str, Any]]) -> bool:
        """문서를 삽입합니다."""
        try:
            points = [
                models.PointStruct(
                    id=i,
                    vector=doc["vector"],
                    payload={
                        "content": doc.get("content", ""),
                        "metadata": doc.get("metadata", {})
                    }
                )
                for i, doc in enumerate(documents)
                if "vector" in doc
            ]
            
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            return True
        except Exception as e:
            logger.error("Qdrant insert failed", error=str(e))
            return False
