# Weaviate 클라이언트 구현
# - Weaviate 연결 및 설정
# - 스키마 관리
# - 벡터 검색 구현
# - 데이터 CRUD 작업

from .base_client import BaseClient
import weaviate
from typing import Dict, Any, List
import time
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..core.logging import get_logger

logger = get_logger(__name__)

class WeaviateClient(BaseClient):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.class_name = config.get('class_name', 'Document')
        
    async def connect(self) -> bool:
        """Weaviate에 연결합니다."""
        try:
            # Weaviate v3 방식으로 연결
            self.client = weaviate.Client(url=self.url)
            
            # 연결 상태 확인
            result = await self.health_check()
            self._connected = result.get('ready', False)
            return self._connected
        except Exception as e:
            logger.error("Failed to connect to Weaviate", url=self.url, error=str(e))
            self._connected = False
            return False
    
    async def disconnect(self) -> None:
        """연결을 해제합니다."""
        if self.client:
            # Weaviate v3에서는 별도 close 메서드가 없음
            self.client = None
            self._connected = False
    
    async def health_check(self) -> Dict[str, Any]:
        """Weaviate 헬스체크를 수행합니다."""
        start_time = time.time()
        try:
            if not self.client:
                return {
                    'name': 'weaviate',
                    'type': 'vector',
                    'status': 'unhealthy',
                    'url': self.url,
                    'error': 'Not connected',
                    'ready': False
                }
            
            # Weaviate v3 방식으로 상태 확인
            ready = self.client.is_ready()
            response_time = self._measure_time(start_time)
            
            status = "healthy" if ready else "unhealthy"
            
            return {
                'name': 'weaviate',
                'type': 'vector',
                'status': status,
                'url': self.url,
                'response_time': response_time,
                'ready': ready
            }
        except Exception as e:
            response_time = self._measure_time(start_time)
            logger.error("Weaviate health check failed", error=str(e))
            return {
                "name": "weaviate",
                "type": "vector",
                "status": "unhealthy",
                "url": self.url,
                "response_time": response_time,
                "error": str(e)
            }
    
    async def search(self, query: str, limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """텍스트 검색을 수행합니다."""
        try:
            result = (
                self.client.query
                .get(self.class_name, ["content", "metadata"])
                .with_bm25(query=query)
                .with_limit(limit)
                .do()
            )
            
            documents = result.get("data", {}).get("Get", {}).get(self.class_name, [])
            return [
                {
                    "id": doc.get("_additional", {}).get("id", ""),
                    "content": doc.get("content", ""),
                    "metadata": doc.get("metadata", {}),
                    "source": "weaviate"
                }
                for doc in documents
            ]
        except Exception as e:
            logger.error("Weaviate search failed", query=query, error=str(e))
            raise SearchError(f"Weaviate search failed: {str(e)}")
    
    async def vector_search(self, vector: List[float], limit: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """벡터 검색을 수행합니다."""
        try:
            result = (
                self.client.query
                .get(self.class_name, ["content", "metadata"])
                .with_near_vector({"vector": vector})
                .with_limit(limit)
                .do()
            )
            
            documents = result.get("data", {}).get("Get", {}).get(self.class_name, [])
            return [
                {
                    "id": doc.get("_additional", {}).get("id", ""),
                    "content": doc.get("content", ""),
                    "similarity": doc.get("_additional", {}).get("distance", 0.0),
                    "metadata": doc.get("metadata", {}),
                    "source": "weaviate"
                }
                for doc in documents
            ]
        except Exception as e:
            logger.error("Weaviate vector search failed", error=str(e))
            raise SearchError(f"Weaviate vector search failed: {str(e)}")
