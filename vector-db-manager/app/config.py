# 애플리케이션 설정 관리
# - 환경 변수 처리
# - 데이터베이스 연결 설정
# - 보안 설정
# - 로깅 설정
# - Pydantic Settings 사용

from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional

class Settings(BaseSettings):
    # 애플리케이션 설정
    app_name: str = "Vector DB Manager"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"
    
    # 보안 설정
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    
    # Vector Databases
    weaviate_url: str = "http://localhost:7210"
    qdrant_url: str = "http://localhost:7211" 
    chroma_url: str = "http://localhost:7212"
    
    # Traditional Databases
    elasticsearch_url: str = "http://localhost:7213"
    meilisearch_url: str = "http://localhost:7214"
    mongodb_url: str = "***************************************/"
    
    # AI/ML Services
    ollama_url: str = "http://localhost:11434"  # 기본 Ollama 서버
    ollama_http_server_url: str = "http://localhost:7218"  # HTTP API 서버 (업데이트된 포트)
    ollama_terminal_server_url: str = "http://localhost:7219"  # 터미널 서버 (업데이트된 포트)
    ollama_default_model: str = "gemma3:1b"

    # WebSocket 설정
    websocket_host: str = "localhost"
    websocket_port: int = 8765
    ollama_websocket_url: str = "ws://localhost:7220"  # ollama-server WebSocket (예정)
    
    # API 설정
    api_v1_prefix: str = "/api/v1"
    api_v1_str: str = "/api/v1"  # 호환성을 위해 추가
    cors_origins: list[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    model_config = SettingsConfigDict(
        env_file=".env",
        extra="ignore"  # 추가 필드 무시
    )

settings = Settings()