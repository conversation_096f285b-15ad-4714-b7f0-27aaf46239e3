# 커스텀 예외 클래스들
# - 데이터베이스 연결 예외
# - 검색 관련 예외
# - 인증 관련 예외
# - 유효성 검사 예외

from fastapi import HTTPException
from typing import Optional, Dict, Any

class DatabaseConnectionError(HTTPException):
    def __init__(self, database_name: str, details: Optional[str] = None):
        detail = f"Failed to connect to {database_name}"
        if details:
            detail += f": {details}"
        super().__init__(status_code=503, detail=detail)

class SearchError(HTTPException):
    def __init__(self, message: str):
        super().__init__(
            status_code=400,
            detail=f"Search error: {message}"
        )

class VectorDimensionError(HTTPException):
    def __init__(self, expected: int, actual: int):
        super().__init__(
            status_code=400,
            detail=f"Vector dimension mismatch. Expected: {expected}, Actual: {actual}"
        )

class DatabaseNotFoundError(HTTPException):
    def __init__(self, database_name: str):
        super().__init__(
            status_code=404,
            detail=f"Database '{database_name}' not found or not configured"
        )

class AuthenticationError(HTTPException):
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(status_code=401, detail=message)

class AuthorizationError(HTTPException):
    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(status_code=403, detail=message)

class ValidationError(HTTPException):
    def __init__(self, field: str, message: str):
        super().__init__(
            status_code=422,
            detail=f"Validation error for field '{field}': {message}"
        )

class RateLimitError(HTTPException):
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(status_code=429, detail=message)

class InternalServerError(HTTPException):
    def __init__(self, message: str = "Internal server error"):
        super().__init__(status_code=500, detail=message)
