# 로깅 설정
# - 구조화된 로깅 설정
# - 로그 레벨 관리
# - 로그 포맷터
# - 로그 핸들러

import logging
import structlog
import sys
from typing import Any
from ..config import settings

def setup_logging() -> None:
    """로깅 설정을 초기화합니다."""
    
    # 표준 로깅 설정
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level.upper())
    )
    
    # structlog 설정
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if not settings.debug 
            else structlog.dev.Console<PERSON><PERSON><PERSON>()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

def get_logger(name: str = None) -> Any:
    """구조화된 로거를 반환합니다."""
    return structlog.get_logger(name)

# 기본 로거 인스턴스
logger = get_logger(__name__)
