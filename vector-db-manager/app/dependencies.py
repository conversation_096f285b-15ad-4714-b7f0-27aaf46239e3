# FastAPI 의존성 주입
# - 데이터베이스 클라이언트 의존성
# - 인증 의존성
# - 공통 의존성들

from fastapi import Depends
from typing import Dict, Any
from .services.database_manager import DatabaseManager
from .services.search_service import SearchService
from .services.vector_service import VectorService
from .core.logging import get_logger

logger = get_logger(__name__)

# 전역 인스턴스들
_database_manager: DatabaseManager = None
_search_service: SearchService = None
_vector_service: VectorService = None

def get_database_manager() -> DatabaseManager:
    """데이터베이스 매니저 의존성을 반환합니다."""
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager

def get_search_service(
    db_manager: DatabaseManager = Depends(get_database_manager)
) -> SearchService:
    """검색 서비스 의존성을 반환합니다."""
    global _search_service
    if _search_service is None:
        _search_service = SearchService(db_manager)
    return _search_service

def get_vector_service(
    db_manager: DatabaseManager = Depends(get_database_manager)
) -> VectorService:
    """벡터 서비스 의존성을 반환합니다."""
    global _vector_service
    if _vector_service is None:
        _vector_service = VectorService(db_manager)
    return _vector_service

async def startup_dependencies():
    """애플리케이션 시작 시 의존성 초기화."""
    logger.info("Initializing dependencies...")
    
    try:
        # 데이터베이스 매니저 초기화
        db_manager = get_database_manager()
        
        # 데이터베이스 연결 시도 (실패해도 앱은 시작)
        try:
            connection_results = await db_manager.connect_all()
            logger.info("Database connection results", results=connection_results)
        except Exception as e:
            logger.warning("Some database connections failed during startup", error=str(e))
        
        # 검색 서비스 초기화
        get_search_service(db_manager)
        
        # 벡터 서비스 초기화
        get_vector_service(db_manager)
        
        logger.info("Dependencies initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize dependencies", error=str(e))
        # 의존성 초기화 실패해도 앱은 계속 시작

async def shutdown_dependencies():
    """애플리케이션 종료 시 정리 작업."""
    logger.info("Shutting down dependencies...")
    
    global _database_manager
    if _database_manager:
        await _database_manager.disconnect_all()
    
    logger.info("Dependencies shut down successfully")
