# FastAPI 애플리케이션 진입점
# - FastAPI 앱 인스턴스 생성
# - CORS 설정
# - 미들웨어 설정
# - 라우터 등록
# - 애플리케이션 라이프사이클 이벤트 처리

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from .api.v1.router import router as v1_router
from .config import settings
from .core.logging import setup_logging, get_logger
from .dependencies import startup_dependencies, shutdown_dependencies
from .core.exceptions import (
    DatabaseConnectionError, 
    SearchError, 
    ValidationError,
    AuthenticationError,
    AuthorizationError
)

# 로깅 설정 초기화
setup_logging()
logger = get_logger(__name__)

# 애플리케이션 라이프사이클 이벤트
@asynccontextmanager
async def lifespan(app: FastAPI):
    """애플리케이션 라이프사이클 관리"""
    # 시작 시
    logger.info("Starting Vector DB Manager application", version=settings.app_version)
    try:
        await startup_dependencies()
        logger.info("Application startup completed")
    except Exception as e:
        logger.error("Application startup failed", error=str(e))
    
    yield
    
    # 종료 시
    logger.info("Shutting down Vector DB Manager application")
    try:
        await shutdown_dependencies()
        logger.info("Application shutdown completed")
    except Exception as e:
        logger.error("Application shutdown failed", error=str(e))

# FastAPI 앱 인스턴스 생성
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="다양한 벡터 데이터베이스와 전통적인 데이터베이스를 통합 관리하는 백엔드 시스템",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API 라우터 등록
app.include_router(v1_router, prefix=settings.api_v1_prefix)

# 예외 핸들러 등록
@app.exception_handler(DatabaseConnectionError)
async def database_connection_exception_handler(request, exc):
    logger.error("Database connection error", error=str(exc))
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_type": "DatabaseConnectionError"
        }
    )

@app.exception_handler(SearchError)
async def search_exception_handler(request, exc):
    logger.error("Search error", error=str(exc))
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_type": "SearchError"
        }
    )

@app.exception_handler(ValidationError)
async def validation_exception_handler(request, exc):
    logger.error("Validation error", error=str(exc))
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_type": "ValidationError"
        }
    )

# 기본 엔드포인트
@app.get("/")
async def root():
    """기본 루트 엔드포인트"""
    return {
        "success": True,
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """기본 헬스체크 엔드포인트"""
    return {
        "success": True,
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.app_version
    }
