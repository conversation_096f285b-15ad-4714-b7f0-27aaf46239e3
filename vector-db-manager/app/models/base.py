# 기본 Pydantic 모델 클래스
# - 공통 필드 정의
# - 기본 설정 클래스
# - 유틸리티 메서드

from pydantic import BaseModel, ConfigDict
from typing import Optional, Any, Dict
from datetime import datetime
from uuid import uuid4

class BaseResponse(BaseModel):
    """기본 응답 모델"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Any] = None
    timestamp: datetime = datetime.now()
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="ignore"
    )

class HealthCheckResponse(BaseResponse):
    """헬스체크 응답 모델"""
    status: str = "healthy"
    version: str = "1.0.0"
    uptime: Optional[float] = None

class ErrorResponse(BaseResponse):
    """에러 응답 모델"""
    success: bool = False
    error_code: Optional[str] = None
    error_type: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class DatabaseStatus(BaseModel):
    """데이터베이스 상태 모델"""
    name: str
    type: str
    status: str
    url: str
    response_time: Optional[float] = None
    error: Optional[str] = None
