# Notion 마크다운 문서 관련 모델
# - Notion 문서 요청/응답 모델
# - 메타데이터 추출 관련 모델
# - Ollama 통신 모델

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ProcessingStatus(str, Enum):
    """문서 처리 상태"""
    PENDING = "pending"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"

class NotionDocumentCreate(BaseModel):
    """Notion 문서 생성 요청 모델"""
    title: Optional[str] = Field(default=None, description="문서 제목 (자동 추출 가능)")
    category: Optional[str] = Field(default=None, description="카테고리 (자동 추출 가능)")
    keywords: Optional[List[str]] = Field(default=None, description="키워드 목록 (자동 추출 가능)")
    content: str = Field(..., description="마크다운 내용")
    original_filename: Optional[str] = Field(default=None, description="원본 파일명")
    auto_process: bool = Field(default=True, description="Ollama 자동 처리 여부")

class NotionDocumentUpdate(BaseModel):
    """Notion 문서 수정 요청 모델"""
    title: Optional[str] = Field(default=None, description="문서 제목")
    category: Optional[str] = Field(default=None, description="카테고리")
    keywords: Optional[List[str]] = Field(default=None, description="키워드 목록")
    content: Optional[str] = Field(default=None, description="마크다운 내용")
    reprocess: bool = Field(default=False, description="Ollama 재처리 여부")

class OllamaMetadata(BaseModel):
    """Ollama 처리 메타데이터"""
    confidence_score: Optional[float] = Field(default=None, description="신뢰도 점수")
    processing_time: Optional[float] = Field(default=None, description="처리 시간 (초)")
    model_version: Optional[str] = Field(default=None, description="사용된 모델 버전")

class NotionDocumentMetadata(BaseModel):
    """Notion 문서 메타데이터"""
    source: str = Field(default="notion-export", description="문서 출처")
    original_filename: Optional[str] = Field(default=None, description="원본 파일명")
    file_path: Optional[str] = Field(default=None, description="파일 경로")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="생성 시간")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="수정 시간")
    processed_at: Optional[datetime] = Field(default=None, description="처리 완료 시간")
    embedding_created: bool = Field(default=False, description="임베딩 생성 여부")
    ollama_model_used: Optional[str] = Field(default=None, description="사용된 Ollama 모델")
    processing_status: ProcessingStatus = Field(default=ProcessingStatus.PENDING, description="처리 상태")

class NotionDocument(BaseModel):
    """Notion 문서 응답 모델"""
    id: str = Field(..., description="문서 ID")
    title: str = Field(..., description="문서 제목")
    category: str = Field(..., description="카테고리")
    keywords: List[str] = Field(default=[], description="키워드 목록")
    content: str = Field(..., description="마크다운 내용")
    summary: Optional[str] = Field(default=None, description="문서 요약")
    metadata: NotionDocumentMetadata = Field(..., description="메타데이터")
    ollama_metadata: Optional[OllamaMetadata] = Field(default=None, description="Ollama 처리 메타데이터")

class OllamaExtractRequest(BaseModel):
    """Ollama 메타데이터 추출 요청"""
    content: str = Field(..., description="마크다운 내용")
    model: str = Field(default="gemma3:1b", description="사용할 Ollama 모델")
    extract_summary: bool = Field(default=True, description="요약도 함께 추출할지 여부")

class OllamaExtractResponse(BaseModel):
    """Ollama 메타데이터 추출 응답"""
    title: str = Field(..., description="추출된 제목")
    category: str = Field(..., description="추출된 카테고리")
    keywords: List[str] = Field(..., description="추출된 키워드")
    summary: Optional[str] = Field(default=None, description="생성된 요약")
    confidence_score: Optional[float] = Field(default=None, description="신뢰도 점수")

class BatchProcessRequest(BaseModel):
    """배치 처리 요청 모델"""
    folder_path: Optional[str] = Field(default=None, description="처리할 폴더 경로")
    file_pattern: str = Field(default="*.md", description="파일 패턴")
    ollama_model: str = Field(default="gemma3:1b", description="사용할 Ollama 모델")
    overwrite_existing: bool = Field(default=False, description="기존 문서 덮어쓰기 여부")

class BatchProcessStatus(BaseModel):
    """배치 처리 상태 모델"""
    task_id: str = Field(..., description="작업 ID")
    status: ProcessingStatus = Field(..., description="처리 상태")
    total_files: int = Field(..., description="전체 파일 수")
    processed_files: int = Field(..., description="처리된 파일 수")
    success_count: int = Field(..., description="성공한 파일 수")
    error_count: int = Field(..., description="실패한 파일 수")
    started_at: datetime = Field(..., description="시작 시간")
    completed_at: Optional[datetime] = Field(default=None, description="완료 시간")
    errors: List[str] = Field(default=[], description="에러 목록")

class NotionSearchRequest(BaseModel):
    """Notion 문서 검색 요청 모델"""
    query: Optional[str] = Field(default=None, description="검색 쿼리")
    category: Optional[str] = Field(default=None, description="카테고리 필터")
    keywords: Optional[List[str]] = Field(default=None, description="키워드 필터")
    date_from: Optional[datetime] = Field(default=None, description="시작 날짜")
    date_to: Optional[datetime] = Field(default=None, description="종료 날짜")
    processing_status: Optional[ProcessingStatus] = Field(default=None, description="처리 상태 필터")
    limit: int = Field(default=20, ge=1, le=100, description="결과 개수")
    offset: int = Field(default=0, ge=0, description="오프셋")
    sort_by: str = Field(default="created_at", description="정렬 기준")
    sort_order: str = Field(default="desc", description="정렬 순서 (asc/desc)")

class CategoryStats(BaseModel):
    """카테고리 통계"""
    category: str = Field(..., description="카테고리명")
    count: int = Field(..., description="문서 수")
    last_updated: datetime = Field(..., description="마지막 업데이트")

class KeywordStats(BaseModel):
    """키워드 통계"""
    keyword: str = Field(..., description="키워드")
    count: int = Field(..., description="사용 횟수")
    weight: float = Field(..., description="가중치")

class NotionTextCreate(BaseModel):
    """텍스트로 Notion 문서 생성 요청 모델"""
    text: str = Field(..., description="입력 텍스트 (마크다운 또는 일반 텍스트)")
    title: Optional[str] = Field(default=None, description="문서 제목 (선택사항)")
    category: Optional[str] = Field(default=None, description="카테고리 (선택사항)")
    keywords: Optional[List[str]] = Field(default=None, description="키워드 목록 (선택사항)")
    auto_process: bool = Field(default=True, description="Ollama 자동 처리 여부")
    convert_to_markdown: bool = Field(default=True, description="마크다운 변환 여부")
    source_description: Optional[str] = Field(default=None, description="텍스트 출처 설명")

class TextConvertRequest(BaseModel):
    """텍스트 변환 요청 모델"""
    text: str = Field(..., description="변환할 텍스트")
    target_format: str = Field(default="markdown", description="대상 형식")
    source_type: Optional[str] = Field(default=None, description="소스 타입 힌트")
    force_conversion: bool = Field(default=False, description="강제 변환 여부")

class TextConvertResponse(BaseModel):
    """텍스트 변환 응답 모델"""
    original_text: str = Field(..., description="원본 텍스트")
    converted_text: str = Field(..., description="변환된 텍스트")
    detected_type: str = Field(..., description="감지된 텍스트 타입")
    was_converted: bool = Field(..., description="변환 여부")
    basic_metadata: Dict[str, Any] = Field(..., description="기본 메타데이터")
    processing_successful: bool = Field(..., description="처리 성공 여부")
    error: Optional[str] = Field(default=None, description="에러 메시지")
