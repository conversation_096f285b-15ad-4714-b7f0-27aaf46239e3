# API 요청 모델들
# - 검색 요청 모델
# - 데이터 생성/수정 요청 모델
# - 벡터 검색 요청 모델

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

class SearchRequest(BaseModel):
    """기본 검색 요청 모델"""
    query: str = Field(..., description="검색 쿼리")
    limit: int = Field(default=10, ge=1, le=100, description="결과 개수")
    offset: int = Field(default=0, ge=0, description="오프셋")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="검색 필터")

class VectorSearchRequest(BaseModel):
    """벡터 검색 요청 모델"""
    vector: List[float] = Field(..., description="검색할 벡터")
    limit: int = Field(default=10, ge=1, le=100, description="결과 개수") 
    threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="유사도 임계값")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="검색 필터")

class TextVectorSearchRequest(BaseModel):
    """텍스트 기반 벡터 검색 요청 모델"""
    text: str = Field(..., description="검색할 텍스트")
    limit: int = Field(default=10, ge=1, le=100, description="결과 개수")
    threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="유사도 임계값")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="검색 필터")

class DocumentRequest(BaseModel):
    """문서 생성/수정 요청 모델"""
    content: str = Field(..., description="문서 내용")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="메타데이터")
    vector: Optional[List[float]] = Field(default=None, description="문서 벡터")

class UnifiedSearchRequest(BaseModel):
    """통합 검색 요청 모델"""
    query: str = Field(..., description="검색 쿼리")
    databases: Optional[List[str]] = Field(default=None, description="검색할 데이터베이스 목록")
    search_type: str = Field(default="text", description="검색 타입 (text, vector, hybrid)")
    limit: int = Field(default=10, ge=1, le=100, description="결과 개수")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="검색 필터")
