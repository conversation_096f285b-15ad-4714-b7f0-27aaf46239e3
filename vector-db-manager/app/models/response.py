# API 응답 모델들
# - 검색 결과 응답 모델
# - 데이터베이스 상태 응답 모델
# - 에러 응답 모델

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from .base import BaseResponse, DatabaseStatus

class SearchResult(BaseModel):
    """검색 결과 단일 항목"""
    id: str
    content: str
    score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    source: str  # 데이터베이스 이름

class SearchResponse(BaseResponse):
    """검색 응답 모델"""
    results: List[SearchResult] = []
    total: int = 0
    query: str
    search_time: float  # 검색 시간 (ms)

class VectorSearchResult(BaseModel):
    """벡터 검색 결과 단일 항목"""
    id: str
    content: str
    similarity: float
    vector: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None
    source: str

class VectorSearchResponse(BaseResponse):
    """벡터 검색 응답 모델"""
    results: List[VectorSearchResult] = []
    total: int = 0
    search_time: float

class DatabaseStatusResponse(BaseResponse):
    """데이터베이스 상태 응답 모델"""
    databases: List[DatabaseStatus] = []
    healthy_count: int = 0
    total_count: int = 0

class UnifiedSearchResponse(BaseResponse):
    """통합 검색 응답 모델"""
    results: List[SearchResult] = []
    total: int = 0
    query: str
    search_time: float
    databases_searched: List[str] = []
    database_results: Dict[str, int] = {}  # 각 DB별 결과 수
