# 통합 데이터베이스 관리 서비스
# - 모든 데이터베이스 클라이언트 관리
# - 연결 상태 확인
# - 헬스체크 기능
# - 통합 작업 조율

from typing import Dict, Any, List
import asyncio
from ..clients.weaviate_client import WeaviateClient
from ..clients.qdrant_client import QdrantClientWrapper
from ..clients.chroma_client import ChromaClient
from ..clients.elasticsearch_client import ElasticsearchClient
from ..clients.meilisearch_client import MeilisearchClient
from ..clients.mongodb_client import MongoDBClient
from ..config import settings
from ..core.logging import get_logger

logger = get_logger(__name__)

class DatabaseManager:
    def __init__(self):
        """모든 데이터베이스 클라이언트를 초기화합니다."""
        self.clients = {}
        self._initialize_clients()
    
    def _initialize_clients(self):
        """모든 데이터베이스 클라이언트를 설정합니다."""
        # Vector Databases
        self.clients['weaviate'] = WeaviateClient({
            'url': settings.weaviate_url,
            'class_name': 'Document'
        })
        
        self.clients['qdrant'] = QdrantClientWrapper({
            'url': settings.qdrant_url,
            'collection_name': 'default_collection'
        })
        
        self.clients['chroma'] = ChromaClient({
            'url': settings.chroma_url,
            'collection_name': 'default_collection'
        })
        
        # Traditional Databases
        self.clients['elasticsearch'] = ElasticsearchClient({
            'url': settings.elasticsearch_url,
            'index_name': 'documents'
        })
        
        self.clients['meilisearch'] = MeilisearchClient({
            'url': settings.meilisearch_url,
            'index_name': 'documents'
        })
        
        self.clients['mongodb'] = MongoDBClient({
            'url': settings.mongodb_url,
            'database_name': 'vector_db',
            'collection_name': 'documents'
        })
    
    async def connect_all(self) -> Dict[str, bool]:
        """모든 데이터베이스에 연결을 시도합니다."""
        results = {}
        
        for name, client in self.clients.items():
            try:
                connected = await client.connect()
                results[name] = connected
                logger.info(f"Connected to {name}", connected=connected)
            except Exception as e:
                results[name] = False
                logger.error(f"Failed to connect to {name}", error=str(e))
        
        return results
    
    async def disconnect_all(self):
        """모든 데이터베이스 연결을 해제합니다."""
        for name, client in self.clients.items():
            try:
                await client.disconnect()
                logger.info(f"Disconnected from {name}")
            except Exception as e:
                logger.error(f"Failed to disconnect from {name}", error=str(e))
    
    async def health_check(self) -> Dict[str, Any]:
        """모든 데이터베이스의 헬스체크를 수행합니다."""
        health_checks = []
        client_names = list(self.clients.keys())
        
        # 모든 클라이언트에 대해 병렬로 헬스체크 수행
        for name, client in self.clients.items():
            health_checks.append(self._safe_health_check(name, client))
        
        results = await asyncio.gather(*health_checks, return_exceptions=True)
        
        databases = []
        healthy_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 예외 발생한 경우
                databases.append({
                    'name': client_names[i],
                    'type': 'unknown',
                    'status': 'unhealthy',
                    'url': getattr(self.clients[client_names[i]], 'url', 'unknown'),
                    'error': str(result)
                })
            elif isinstance(result, dict):
                databases.append(result)
                if result.get('status') == 'healthy':
                    healthy_count += 1
            else:
                # 예상치 못한 결과
                databases.append({
                    'name': client_names[i],
                    'type': 'unknown',
                    'status': 'unknown',
                    'url': getattr(self.clients[client_names[i]], 'url', 'unknown'),
                    'error': f'Unexpected result: {result}'
                })
        
        return {
            'databases': databases,
            'healthy_count': healthy_count,
            'total_count': len(self.clients),
            'overall_status': 'healthy' if healthy_count == len(self.clients) else 'degraded'
        }
    
    async def _safe_health_check(self, name: str, client) -> Dict[str, Any]:
        """개별 클라이언트의 안전한 헬스체크를 수행합니다."""
        try:
            result = await client.health_check()
            # 결과 표준화
            if not isinstance(result, dict):
                return {
                    'name': name,
                    'type': 'unknown',
                    'status': 'unhealthy',
                    'url': getattr(client, 'url', 'unknown'),
                    'error': 'Invalid health check result'
                }
            
            # 필수 필드 확인 및 기본값 설정
            result.setdefault('name', name)
            result.setdefault('type', 'vector' if name in ['weaviate', 'qdrant', 'chroma'] else 'traditional')
            result.setdefault('status', 'unknown')
            result.setdefault('url', getattr(client, 'url', 'unknown'))
            
            return result
        except Exception as e:
            return {
                'name': name,
                'type': 'vector' if name in ['weaviate', 'qdrant', 'chroma'] else 'traditional',
                'status': 'unhealthy',
                'url': getattr(client, 'url', 'unknown'),
                'error': str(e)
            }
    
    def get_client(self, database_name: str):
        """특정 데이터베이스 클라이언트를 반환합니다."""
        return self.clients.get(database_name)
    
    def get_all_clients(self) -> Dict[str, Any]:
        """모든 클라이언트를 반환합니다."""
        return self.clients
    
    def get_vector_clients(self) -> Dict[str, Any]:
        """벡터 데이터베이스 클라이언트들만 반환합니다."""
        return {
            name: client for name, client in self.clients.items()
            if name in ['weaviate', 'qdrant', 'chroma']
        }
    
    def get_traditional_clients(self) -> Dict[str, Any]:
        """전통적인 데이터베이스 클라이언트들만 반환합니다."""
        return {
            name: client for name, client in self.clients.items()
            if name in ['elasticsearch', 'meilisearch', 'mongodb']
        }
