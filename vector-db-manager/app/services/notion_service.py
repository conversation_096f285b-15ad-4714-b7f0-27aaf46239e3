# Notion 문서 관리 서비스
# - MongoDB 특화 Notion 문서 CRUD
# - 자동 메타데이터 처리
# - 검색 및 필터링
# - 통계 및 분석

import os
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from bson import ObjectId
from ..clients.mongodb_client import MongoDBClient
from ..services.ollama_service import get_ollama_service
from ..models.notion import (
    NotionDocument, NotionDocumentCreate, NotionDocumentUpdate,
    NotionDocumentMetadata, ProcessingStatus, BatchProcessRequest,
    BatchProcessStatus, NotionSearchRequest, CategoryStats, KeywordStats,
    OllamaExtractRequest, OllamaMetadata, NotionTextCreate
)
from ..core.logging import get_logger
from ..core.exceptions import DatabaseConnectionError, SearchError
from ..config import settings

logger = get_logger(__name__)

class NotionDocumentService:
    """Notion 문서 관리 서비스"""
    
    def __init__(self, mongodb_client: MongoDBClient):
        self.mongodb_client = mongodb_client
        self.collection_name = "notion_documents"
        
    async def ensure_collection_setup(self):
        """컬렉션 설정 및 인덱스 생성"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            # 텍스트 인덱스 생성
            await collection.create_index([
                ("title", "text"),
                ("content", "text"),
                ("summary", "text"),
                ("keywords", "text")
            ])
            
            # 기타 인덱스
            await collection.create_index("category")
            await collection.create_index("metadata.created_at")
            await collection.create_index("metadata.processing_status")
            
            logger.info("Notion documents collection setup completed")
            
        except Exception as e:
            logger.error("Failed to setup collection", error=str(e))
    
    async def create_document(self, request: NotionDocumentCreate) -> Optional[NotionDocument]:
        """새 문서 생성"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            # 메타데이터 생성
            metadata = NotionDocumentMetadata(
                original_filename=request.original_filename,
                processing_status=ProcessingStatus.PENDING if request.auto_process else ProcessingStatus.COMPLETED
            )
            
            # 문서 데이터 구성
            doc_data = {
                "title": request.title or "Untitled",
                "category": request.category or "Uncategorized", 
                "keywords": request.keywords or [],
                "content": request.content,
                "summary": None,
                "metadata": metadata.model_dump(),
                "ollama_metadata": None
            }
            
            # MongoDB에 삽입
            result = await collection.insert_one(doc_data)
            doc_id = str(result.inserted_id)
            
            # 자동 처리가 활성화된 경우 백그라운드에서 처리
            if request.auto_process:
                asyncio.create_task(self._process_document_async(doc_id, request.content))
            
            # 생성된 문서 반환
            return await self.get_document(doc_id)
            
        except Exception as e:
            logger.error("Failed to create document", error=str(e))
            return None
    
    async def get_document(self, document_id: str) -> Optional[NotionDocument]:
        """문서 조회"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            doc = await collection.find_one({"_id": ObjectId(document_id)})
            if not doc:
                return None
            
            # Pydantic 모델로 변환
            return self._doc_to_model(doc)
            
        except Exception as e:
            logger.error("Failed to get document", document_id=document_id, error=str(e))
            return None
    
    async def update_document(self, document_id: str, request: NotionDocumentUpdate) -> Optional[NotionDocument]:
        """문서 수정"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            # 업데이트 데이터 구성
            update_data = {"metadata.updated_at": datetime.utcnow()}
            
            if request.title is not None:
                update_data["title"] = request.title
            if request.category is not None:
                update_data["category"] = request.category
            if request.keywords is not None:
                update_data["keywords"] = request.keywords
            if request.content is not None:
                update_data["content"] = request.content
            
            # 재처리 요청 시 상태 변경
            if request.reprocess and request.content:
                update_data["metadata.processing_status"] = ProcessingStatus.PENDING.value
                
            result = await collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )
            
            if result.modified_count == 0:
                return None
            
            # 재처리 백그라운드 작업
            if request.reprocess and request.content:
                asyncio.create_task(self._process_document_async(document_id, request.content))
            
            return await self.get_document(document_id)
            
        except Exception as e:
            logger.error("Failed to update document", document_id=document_id, error=str(e))
            return None
    
    async def delete_document(self, document_id: str) -> bool:
        """문서 삭제"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            result = await collection.delete_one({"_id": ObjectId(document_id)})
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error("Failed to delete document", document_id=document_id, error=str(e))
            return False
    
    async def search_documents(self, request: NotionSearchRequest) -> Tuple[List[NotionDocument], int]:
        """문서 검색"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            # 검색 쿼리 구성
            query = {}
            
            # 텍스트 검색
            if request.query:
                query["$text"] = {"$search": request.query}
            
            # 필터 조건
            if request.category:
                query["category"] = request.category
            if request.keywords:
                query["keywords"] = {"$in": request.keywords}
            if request.processing_status:
                query["metadata.processing_status"] = request.processing_status.value
            if request.date_from or request.date_to:
                date_filter = {}
                if request.date_from:
                    date_filter["$gte"] = request.date_from
                if request.date_to:
                    date_filter["$lte"] = request.date_to
                query["metadata.created_at"] = date_filter
            
            # 정렬 설정
            sort_order = 1 if request.sort_order == "asc" else -1
            sort_field = f"metadata.{request.sort_by}" if request.sort_by in ["created_at", "updated_at"] else request.sort_by
            
            # 전체 개수 조회
            total = await collection.count_documents(query)
            
            # 문서 조회
            cursor = collection.find(query).sort(sort_field, sort_order).skip(request.offset).limit(request.limit)
            
            documents = []
            async for doc in cursor:
                documents.append(self._doc_to_model(doc))
            
            return documents, total
            
        except Exception as e:
            logger.error("Failed to search documents", error=str(e))
            raise SearchError(f"Document search failed: {str(e)}")
    
    async def get_categories(self) -> List[CategoryStats]:
        """카테고리 통계 조회"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            pipeline = [
                {"$group": {
                    "_id": "$category",
                    "count": {"$sum": 1},
                    "last_updated": {"$max": "$metadata.updated_at"}
                }},
                {"$sort": {"count": -1}}
            ]
            
            results = []
            async for item in collection.aggregate(pipeline):
                results.append(CategoryStats(
                    category=item["_id"],
                    count=item["count"],
                    last_updated=item["last_updated"]
                ))
            
            return results
            
        except Exception as e:
            logger.error("Failed to get categories", error=str(e))
            return []
    
    async def get_keywords(self, limit: int = 50) -> List[KeywordStats]:
        """키워드 통계 조회"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            pipeline = [
                {"$unwind": "$keywords"},
                {"$group": {
                    "_id": "$keywords",
                    "count": {"$sum": 1}
                }},
                {"$sort": {"count": -1}},
                {"$limit": limit}
            ]
            
            results = []
            async for item in collection.aggregate(pipeline):
                # 간단한 가중치 계산 (사용 빈도 기반)
                weight = min(item["count"] / 10.0, 1.0)
                results.append(KeywordStats(
                    keyword=item["_id"],
                    count=item["count"],
                    weight=weight
                ))
            
            return results
            
        except Exception as e:
            logger.error("Failed to get keywords", error=str(e))
            return []
    
    async def _process_document_async(self, document_id: str, content: str):
        """문서 비동기 처리 (Ollama 메타데이터 추출)"""
        try:
            # 처리 상태 업데이트
            await self._update_processing_status(document_id, ProcessingStatus.PROCESSING)
            
            # Ollama 서비스로 메타데이터 추출
            ollama_service = await get_ollama_service()
            request = OllamaExtractRequest(
                content=content, 
                extract_summary=True,
                model=getattr(settings, 'ollama_default_model', 'gemma3:1b')
            )
            
            result = await ollama_service.extract_metadata(request)
            
            if result:
                # 성공적으로 추출된 경우 문서 업데이트
                await self._update_extracted_metadata(document_id, result)
                await self._update_processing_status(document_id, ProcessingStatus.COMPLETED)
                logger.info("Document processed successfully", document_id=document_id)
            else:
                # 추출 실패
                await self._update_processing_status(document_id, ProcessingStatus.FAILED)
                logger.error("Failed to extract metadata", document_id=document_id)
                
        except Exception as e:
            await self._update_processing_status(document_id, ProcessingStatus.FAILED)
            logger.error("Document processing error", document_id=document_id, error=str(e))
    
    async def _update_processing_status(self, document_id: str, status: ProcessingStatus):
        """처리 상태 업데이트"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            update_data = {
                "metadata.processing_status": status.value,
                "metadata.updated_at": datetime.utcnow()
            }
            
            if status == ProcessingStatus.COMPLETED:
                update_data["metadata.processed_at"] = datetime.utcnow()
            
            await collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )
            
        except Exception as e:
            logger.error("Failed to update processing status", document_id=document_id, error=str(e))
    
    async def _update_extracted_metadata(self, document_id: str, extracted: Any):
        """추출된 메타데이터로 문서 업데이트"""
        try:
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            
            # 기존 데이터와 병합
            current_doc = await collection.find_one({"_id": ObjectId(document_id)})
            if not current_doc:
                return
            
            update_data = {}
            
            # 제목이 "Untitled"인 경우에만 업데이트
            if current_doc.get("title") in [None, "Untitled", ""]:
                update_data["title"] = extracted.title
            
            # 카테고리가 "Uncategorized"인 경우에만 업데이트
            if current_doc.get("category") in [None, "Uncategorized", ""]:
                update_data["category"] = extracted.category
            
            # 키워드가 비어있는 경우에만 업데이트
            if not current_doc.get("keywords"):
                update_data["keywords"] = extracted.keywords
            
            # 요약은 항상 업데이트
            if extracted.summary:
                update_data["summary"] = extracted.summary
            
            # Ollama 메타데이터 저장
            update_data["ollama_metadata"] = OllamaMetadata(
                confidence_score=extracted.confidence_score,
                processing_time=None,  # 이미 계산됨
                model_version=getattr(settings, 'ollama_default_model', 'gemma3:1b')
            ).model_dump()
            
            await collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )
            
        except Exception as e:
            logger.error("Failed to update extracted metadata", document_id=document_id, error=str(e))
    
    async def create_document_from_text(self, request: NotionTextCreate) -> Optional[NotionDocument]:
        """텍스트로부터 새 문서 생성"""
        try:
            from ..utils.text_converter import smart_text_processor
            
            # 텍스트 스마트 처리
            processing_result = smart_text_processor.process_text(
                request.text, 
                force_conversion=request.convert_to_markdown
            )
            
            if not processing_result['processing_successful']:
                logger.error("Text processing failed", error=processing_result.get('error'))
                return None
            
            # 변환된 텍스트 사용
            final_content = processing_result['converted_text']
            basic_metadata = processing_result['basic_metadata']
            
            # 제목 결정 로직
            final_title = (
                request.title or  # 사용자 지정 제목
                basic_metadata.get('estimated_title') or  # 텍스트에서 추출한 제목
                "Untitled"  # 기본값
            )
            
            # 메타데이터 생성
            metadata = NotionDocumentMetadata(
                original_filename=None,  # 텍스트 입력이므로 파일명 없음
                source="text-input",  # 소스 타입 명시
                processing_status=ProcessingStatus.PENDING if request.auto_process else ProcessingStatus.COMPLETED
            )
            
            # 문서 데이터 구성
            doc_data = {
                "title": final_title,
                "category": request.category or "Uncategorized",
                "keywords": request.keywords or [],
                "content": final_content,
                "summary": None,
                "metadata": metadata.model_dump(),
                "ollama_metadata": None,
                "text_processing_info": {  # 추가 정보
                    "detected_type": processing_result['detected_type'],
                    "was_converted": processing_result['was_converted'],
                    "source_description": request.source_description,
                    "basic_metadata": basic_metadata
                }
            }
            
            # MongoDB에 삽입
            db = self.mongodb_client.client[self.mongodb_client.database_name]
            collection = db[self.collection_name]
            result = await collection.insert_one(doc_data)
            doc_id = str(result.inserted_id)
            
            # 자동 처리가 활성화된 경우 백그라운드에서 처리
            if request.auto_process:
                asyncio.create_task(self._process_document_async(doc_id, final_content))
            
            # 생성된 문서 반환
            return await self.get_document(doc_id)
            
        except Exception as e:
            logger.error("Failed to create document from text", error=str(e))
            return None
    
    def _doc_to_model(self, doc: Dict[str, Any]) -> NotionDocument:
        """MongoDB 문서를 Pydantic 모델로 변환"""
        # metadata 처리
        metadata_dict = doc.get("metadata", {})
        metadata = NotionDocumentMetadata(**metadata_dict)
        
        # ollama_metadata 처리
        ollama_metadata = None
        if doc.get("ollama_metadata"):
            ollama_metadata = OllamaMetadata(**doc["ollama_metadata"])
        
        return NotionDocument(
            id=str(doc["_id"]),
            title=doc.get("title", "Untitled"),
            category=doc.get("category", "Uncategorized"),
            keywords=doc.get("keywords", []),
            content=doc.get("content", ""),
            summary=doc.get("summary"),
            metadata=metadata,
            ollama_metadata=ollama_metadata
        )
