# Ollama 통합 서비스
# - Ollama API 통신
# - 메타데이터 추출 및 파싱
# - 프롬프트 엔지니어링
# - 에러 처리 및 재시도 로직

import httpx
import json
import asyncio
from typing import Dict, Any, Optional, List
from ..core.logging import get_logger
from ..models.notion import OllamaExtractRequest, OllamaExtractResponse
from ..config import settings

logger = get_logger(__name__)

class OllamaService:
    """Ollama 서비스 클래스 - 새로운 메타데이터 서버 연동"""
    
    def __init__(self):
        self.base_url = settings.ollama_url  # 기본 Ollama 서버
        self.http_server_url = settings.ollama_http_server_url  # HTTP API 서버 (11501)
        self.terminal_server_url = settings.ollama_terminal_server_url  # 터미널 서버 (11502)
        self.client = httpx.AsyncClient(timeout=300.0)  # 5분 타임아웃
        self.default_model = settings.ollama_default_model
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def health_check(self) -> Dict[str, Any]:
        """Ollama 서버들 상태 확인"""
        health_status = {
            "ollama_base": False,
            "http_server": False,
            "terminal_server": False,
            "timestamp": None
        }
        
        # 기본 Ollama 서버 체크
        try:
            response = await self.client.get(f"{self.base_url}/api/tags", timeout=5.0)
            health_status["ollama_base"] = response.status_code == 200
        except Exception as e:
            logger.debug("Base Ollama health check failed", error=str(e))
        
        # HTTP API 서버 체크 (11501)
        try:
            response = await self.client.get(f"{self.http_server_url}/api/metadata/health", timeout=5.0)
            if response.status_code == 200:
                data = response.json()
                health_status["http_server"] = data.get('success', False)
        except Exception as e:
            logger.debug("HTTP server health check failed", error=str(e))
            
        # 터미널 서버 체크 (11502)  
        try:
            response = await self.client.get(f"{self.terminal_server_url}/api/metadata/health", timeout=5.0)
            if response.status_code == 200:
                data = response.json()
                health_status["terminal_server"] = data.get('success', False)
        except Exception as e:
            logger.debug("Terminal server health check failed", error=str(e))
            
        health_status["timestamp"] = asyncio.get_event_loop().time()
        
        # 전체 상태 결정
        health_status["overall_healthy"] = (
            health_status["ollama_base"] and 
            (health_status["http_server"] or health_status["terminal_server"])
        )
        
        return health_status
    
    async def list_models(self) -> List[str]:
        """사용 가능한 모델 목록 조회"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                return [model['name'] for model in data.get('models', [])]
            return []
        except Exception as e:
            logger.error("Failed to list Ollama models", error=str(e))
            return []
    
    def _create_extraction_prompt(self, content: str, extract_summary: bool = True) -> str:
        """메타데이터 추출용 프롬프트 생성"""
        summary_requirement = "4. summary: 문서의 핵심 내용을 2-3문장으로 요약" if extract_summary else ""
        content_preview = content[:3000] + ("..." if len(content) > 3000 else "")
        comma_if_summary = "," if extract_summary else ""
        summary_field = '"summary": "문서 요약"' if extract_summary else ""
        
        prompt = f"""
다음 마크다운 문서를 분석하여 메타데이터를 추출해주세요.
응답은 반드시 JSON 형식으로만 해주세요. 다른 텍스트는 포함하지 마세요.

요구사항:
1. title: 문서의 적절한 제목 (기존 제목이 없으면 내용을 바탕으로 생성)
2. category: 문서의 주제 카테고리 (예: AI, 개발, 학습, 비즈니스, 개인노트 등)
3. keywords: 주요 키워드 3-8개 (중요도 순)
{summary_requirement}

문서 내용:
{content_preview}

JSON 응답 형식:
{{
    "title": "문서 제목",
    "category": "카테고리",
    "keywords": ["키워드1", "키워드2", "키워드3"]{comma_if_summary}
    {summary_field}
}}
"""
        return prompt
    
    async def extract_metadata(self, request: OllamaExtractRequest) -> Optional[OllamaExtractResponse]:
        """마크다운 문서에서 메타데이터 추출 - 새로운 메타데이터 서버 사용"""
        import time
        start_time = time.time()
        
        try:
            # 새로운 메타데이터 서버 API 호출 (자동 방식 선택)
            response = await self.client.post(
                f"{self.terminal_server_url}/api/metadata/extract",
                json={
                    "text": request.content,
                    "textType": "markdown",
                    "model": request.model,
                    "preferHttp": False  # 터미널 방식 우선
                },
                timeout=120.0  # 2분 타임아웃
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    metadata = data['data']['metadata']
                    
                    # 응답 구조 변환
                    result = OllamaExtractResponse(
                        title=metadata.get('title', 'Untitled'),
                        category=metadata.get('category', 'Other'),
                        keywords=metadata.get('keywords', []),
                        summary=metadata.get('summary', '') if request.extract_summary else None,
                        processing_time=time.time() - start_time,
                        model_used=request.model,
                        success=True
                    )
                    
                    logger.info("Metadata extraction successful via new server", 
                              title=result.title, 
                              category=result.category, 
                              keywords_count=len(result.keywords),
                              processing_time=result.processing_time)
                    
                    return result
                else:
                    logger.error("Invalid response from metadata server", response_data=data)
            else:
                logger.error("Metadata server returned error", status_code=response.status_code, response=response.text)
                
        except Exception as e:
            logger.error("Metadata extraction failed", error=str(e), model=request.model)
            
        # Fallback: 기존 방식 시도
        return await self._extract_metadata_fallback(request, start_time)
        
    async def _extract_metadata_fallback(self, request: OllamaExtractRequest, start_time: float) -> Optional[OllamaExtractResponse]:
        """Fallback 메타데이터 추출 (기존 방식)"""
        try:
            logger.info("Using fallback metadata extraction method")
            
            prompt = self._create_extraction_prompt(request.content, request.extract_summary)
            
            # 기본 Ollama API 호출
            response = await self.client.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": request.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.3,  # 일관성을 위해 낮은 온도
                        "top_p": 0.9,
                        "repeat_penalty": 1.1
                    }
                }
            )
            
            if response.status_code != 200:
                logger.error("Ollama API error", status_code=response.status_code, response=response.text)
                return None
            
            response_data = response.json()
            generated_text = response_data.get("response", "").strip()
            
            # JSON 응답 파싱
            metadata = await self._parse_json_response(generated_text)
            if not metadata:
                return None
            
            processing_time = time.time() - start_time
            
            # 응답 모델 생성
            return OllamaExtractResponse(
                title=metadata.get("title", "Untitled"),
                category=metadata.get("category", "Uncategorized"),
                keywords=metadata.get("keywords", []),
                summary=metadata.get("summary") if request.extract_summary else None,
                confidence_score=self._calculate_confidence(metadata, generated_text)
            )
            
        except Exception as e:
            logger.error("Metadata extraction failed", error=str(e), model=request.model)
            return None
    
    async def _parse_json_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """Ollama 응답에서 JSON 추출 및 파싱"""
        try:
            # 여러 JSON 추출 시도
            attempts = [
                response_text,  # 원본 그대로
                response_text.strip(),  # 공백 제거
                self._extract_json_from_text(response_text),  # JSON 부분만 추출
            ]
            
            for attempt in attempts:
                if not attempt:
                    continue
                try:
                    return json.loads(attempt)
                except json.JSONDecodeError:
                    continue
            
            logger.warning("Failed to parse JSON response", response=response_text[:200])
            return None
            
        except Exception as e:
            logger.error("JSON parsing error", error=str(e))
            return None
    
    def _extract_json_from_text(self, text: str) -> str:
        """텍스트에서 JSON 부분만 추출"""
        import re
        
        # JSON 패턴 찾기
        json_patterns = [
            r'\{.*\}',  # 중괄호로 감싸진 전체
            r'\{[^}]*"title"[^}]*\}',  # title이 포함된 JSON
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                return matches[0]
        
        return text
    
    def _calculate_confidence(self, metadata: Dict[str, Any], response_text: str) -> float:
        """메타데이터 추출 신뢰도 계산"""
        confidence = 0.5  # 기본값
        
        # JSON 파싱 성공 시 보너스
        confidence += 0.3
        
        # 필수 필드 존재 확인
        if metadata.get("title") and len(metadata["title"]) > 3:
            confidence += 0.1
        if metadata.get("category") and metadata["category"] != "Uncategorized":
            confidence += 0.1
        if metadata.get("keywords") and len(metadata["keywords"]) >= 3:
            confidence += 0.1
        
        # 응답 품질 평가
        if len(response_text) > 50 and '```' not in response_text:
            confidence += 0.1
            
        return min(confidence, 1.0)
    
    async def batch_extract(self, contents: List[str], model: str = "gemma3:1b") -> List[Optional[OllamaExtractResponse]]:
        """여러 문서의 메타데이터를 배치로 추출"""
        tasks = []
        for content in contents:
            request = OllamaExtractRequest(content=content, model=model)
            tasks.append(self.extract_metadata(request))
        
        # 동시에 처리 (단, 너무 많으면 제한)
        batch_size = 3  # Ollama 서버 부하 고려
        results = []
        
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i+batch_size]
            batch_results = await asyncio.gather(*batch, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error("Batch extraction error", error=str(result))
                    results.append(None)
                else:
                    results.append(result)
            
            # 배치 간 약간의 대기 (서버 부하 방지)
            if i + batch_size < len(tasks):
                await asyncio.sleep(1)
        
        return results

# 전역 Ollama 서비스 인스턴스
_ollama_service = None

async def get_ollama_service() -> OllamaService:
    """Ollama 서비스 인스턴스 반환"""
    global _ollama_service
    if _ollama_service is None:
        ollama_url = getattr(settings, 'ollama_url', 'http://localhost:11434')
        _ollama_service = OllamaService(ollama_url)
    return _ollama_service
