# 통합 검색 서비스
# - 전체 데이터베이스 검색
# - 결과 통합 및 랭킹
# - 검색 결과 필터링

from typing import List, Dict, Any, Optional
import asyncio
import time
from .database_manager import DatabaseManager
from ..core.logging import get_logger

logger = get_logger(__name__)

class SearchService:
    def __init__(self, database_manager: DatabaseManager):
        """검색 서비스를 초기화합니다."""
        self.db_manager = database_manager
    
    async def unified_search(
        self, 
        query: str, 
        databases: Optional[List[str]] = None, 
        limit: int = 10
    ) -> Dict[str, Any]:
        """통합 검색을 수행합니다."""
        start_time = time.time()
        
        # 검색할 데이터베이스 선택
        if databases is None:
            databases = list(self.db_manager.get_all_clients().keys())
        
        # 병렬 검색 수행
        search_tasks = []
        for db_name in databases:
            client = self.db_manager.get_client(db_name)
            if client:
                search_tasks.append(self._search_database(client, query, limit))
        
        results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # 결과 통합
        all_results = []
        database_results = {}
        
        for i, (db_name, result) in enumerate(zip(databases, results)):
            if isinstance(result, list):
                all_results.extend(result)
                database_results[db_name] = len(result)
            else:
                logger.error(f"Search failed for {db_name}", error=str(result))
                database_results[db_name] = 0
        
        # 결과 랭킹 및 제한
        ranked_results = self._rank_results(all_results)[:limit]
        
        search_time = (time.time() - start_time) * 1000
        
        return {
            'results': ranked_results,
            'total': len(ranked_results),
            'query': query,
            'search_time': search_time,
            'databases_searched': databases,
            'database_results': database_results
        }
    
    async def _search_database(self, client, query: str, limit: int) -> List[Dict[str, Any]]:
        """개별 데이터베이스에서 검색을 수행합니다."""
        try:
            return await client.search(query, limit)
        except Exception as e:
            logger.error(f"Search failed for {client.name}", error=str(e))
            return []
    
    def _rank_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """검색 결과를 점수순으로 정렬합니다."""
        # 점수가 있는 결과들을 점수순으로 정렬
        scored_results = [r for r in results if r.get('score') is not None]
        unscored_results = [r for r in results if r.get('score') is None]
        
        # 점수순 정렬 (높은 점수가 먼저)
        scored_results.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        # 점수가 없는 결과들을 뒤에 추가
        return scored_results + unscored_results
    
    async def vector_search(
        self, 
        vector: List[float], 
        databases: Optional[List[str]] = None, 
        limit: int = 10
    ) -> Dict[str, Any]:
        """벡터 검색을 수행합니다."""
        start_time = time.time()
        
        # 벡터 데이터베이스만 선택
        vector_clients = self.db_manager.get_vector_clients()
        
        if databases:
            vector_clients = {
                name: client for name, client in vector_clients.items()
                if name in databases
            }
        
        # 병렬 벡터 검색 수행
        search_tasks = []
        for db_name, client in vector_clients.items():
            search_tasks.append(self._vector_search_database(client, vector, limit))
        
        results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # 결과 통합
        all_results = []
        database_results = {}
        
        for db_name, result in zip(vector_clients.keys(), results):
            if isinstance(result, list):
                all_results.extend(result)
                database_results[db_name] = len(result)
            else:
                logger.error(f"Vector search failed for {db_name}", error=str(result))
                database_results[db_name] = 0
        
        # 유사도순 정렬
        all_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        ranked_results = all_results[:limit]
        
        search_time = (time.time() - start_time) * 1000
        
        return {
            'results': ranked_results,
            'total': len(ranked_results),
            'search_time': search_time,
            'databases_searched': list(vector_clients.keys()),
            'database_results': database_results
        }
    
    async def _vector_search_database(self, client, vector: List[float], limit: int) -> List[Dict[str, Any]]:
        """개별 벡터 데이터베이스에서 검색을 수행합니다."""
        try:
            return await client.vector_search(vector, limit)
        except Exception as e:
            logger.error(f"Vector search failed for {client.name}", error=str(e))
            return []
