# 벡터 관련 서비스
# - 벡터 임베딩 생성
# - 벡터 유사도 검색
# - 벡터 데이터 관리

from typing import List, Dict, Any, Optional
import asyncio
import time
from .database_manager import DatabaseManager
from ..core.logging import get_logger

logger = get_logger(__name__)

class VectorService:
    def __init__(self, database_manager: DatabaseManager):
        """벡터 서비스를 초기화합니다."""
        self.db_manager = database_manager
    
    async def similarity_search(
        self, 
        query_vector: List[float], 
        databases: Optional[List[str]] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """벡터 유사도 검색을 수행합니다."""
        start_time = time.time()
        
        # 벡터 데이터베이스만 선택
        vector_clients = self.db_manager.get_vector_clients()
        
        if databases:
            vector_clients = {
                name: client for name, client in vector_clients.items()
                if name in databases
            }
        
        # 병렬 벡터 검색 수행
        search_tasks = []
        for db_name, client in vector_clients.items():
            search_tasks.append(self._vector_search_database(client, query_vector, limit))
        
        results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # 결과 통합
        all_results = []
        database_results = {}
        
        for db_name, result in zip(vector_clients.keys(), results):
            if isinstance(result, list):
                all_results.extend(result)
                database_results[db_name] = len(result)
            else:
                logger.error(f"Vector search failed for {db_name}", error=str(result))
                database_results[db_name] = 0
        
        # 유사도순 정렬
        all_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        ranked_results = all_results[:limit]
        
        search_time = (time.time() - start_time) * 1000
        
        return {
            'results': ranked_results,
            'total': len(ranked_results),
            'search_time': search_time,
            'databases_searched': list(vector_clients.keys()),
            'database_results': database_results
        }
    
    async def _vector_search_database(self, client, vector: List[float], limit: int) -> List[Dict[str, Any]]:
        """개별 벡터 데이터베이스에서 검색을 수행합니다."""
        try:
            return await client.vector_search(vector, limit)
        except Exception as e:
            logger.error(f"Vector search failed for {client.name}", error=str(e))
            return []
    
    def validate_vector_dimension(self, vector: List[float], expected_dim: int = None) -> bool:
        """벡터 차원을 검증합니다."""
        if not vector:
            return False
        
        if expected_dim and len(vector) != expected_dim:
            return False
        
        return all(isinstance(x, (int, float)) for x in vector)
    
    async def get_supported_databases(self) -> List[str]:
        """벡터 검색을 지원하는 데이터베이스 목록을 반환합니다."""
        return list(self.db_manager.get_vector_clients().keys())
