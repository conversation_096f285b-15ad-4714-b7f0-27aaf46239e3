# WebSocket 서비스
# - ollama-server와의 실시간 통신
# - frontend 클라이언트와의 WebSocket 연결 관리
# - 실시간 상태 브로드캐스트

import asyncio
import json
import websockets
from typing import Dict, Any, Set, Optional, Callable
from websockets.server import WebSocketServerProtocol
from ..core.logging import get_logger
from ..config import settings

logger = get_logger(__name__)

class WebSocketService:
    """WebSocket 서비스 클래스"""
    
    def __init__(self):
        self.clients: Set[WebSocketServerProtocol] = set()
        self.ollama_connection: Optional[WebSocketServerProtocol] = None
        self.server = None
        self.running = False
        
    async def start_server(self):
        """WebSocket 서버 시작"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                settings.websocket_host,
                settings.websocket_port
            )
            self.running = True
            logger.info(f"WebSocket server started on {settings.websocket_host}:{settings.websocket_port}")
            
            # ollama-server WebSocket 연결 시작
            asyncio.create_task(self.connect_to_ollama())
            
        except Exception as e:
            logger.error("Failed to start WebSocket server", error=str(e))
            raise
    
    async def stop_server(self):
        """WebSocket 서버 중지"""
        self.running = False
        
        # 모든 클라이언트 연결 종료
        if self.clients:
            await asyncio.gather(
                *[client.close() for client in self.clients],
                return_exceptions=True
            )
            self.clients.clear()
        
        # ollama-server 연결 종료
        if self.ollama_connection:
            await self.ollama_connection.close()
            self.ollama_connection = None
        
        # 서버 종료
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            
        logger.info("WebSocket server stopped")
    
    async def handle_client(self, websocket: WebSocketServerProtocol, path: str):
        """클라이언트 연결 처리"""
        self.clients.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"Client connected: {client_info}")
        
        try:
            # 연결 확인 메시지 전송
            await websocket.send(json.dumps({
                "type": "connection",
                "status": "connected",
                "message": "Connected to Vector DB Manager WebSocket",
                "timestamp": asyncio.get_event_loop().time()
            }))
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.process_client_message(websocket, data)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from client {client_info}")
                except Exception as e:
                    logger.error(f"Error processing message from {client_info}", error=str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected: {client_info}")
        except Exception as e:
            logger.error(f"Client connection error: {client_info}", error=str(e))
        finally:
            self.clients.discard(websocket)
    
    async def process_client_message(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """클라이언트 메시지 처리"""
        message_type = data.get("type")
        
        if message_type == "ping":
            await websocket.send(json.dumps({
                "type": "pong",
                "timestamp": asyncio.get_event_loop().time()
            }))
        elif message_type == "subscribe":
            # 특정 이벤트 구독 (향후 확장용)
            events = data.get("events", [])
            logger.debug(f"Client subscribed to events: {events}")
        elif message_type == "request_status":
            # 현재 상태 요청
            await self.send_current_status(websocket)
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    async def send_current_status(self, websocket: WebSocketServerProtocol):
        """현재 상태 전송"""
        try:
            from .ollama_service import get_ollama_service
            ollama_service = await get_ollama_service()
            health_status = await ollama_service.health_check()
            queue_status = await ollama_service.get_queue_status()
            
            status_message = {
                "type": "status",
                "data": {
                    "health": health_status,
                    "queue": queue_status
                },
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await websocket.send(json.dumps(status_message))
            
        except Exception as e:
            logger.error("Failed to send current status", error=str(e))
    
    async def connect_to_ollama(self):
        """ollama-server WebSocket에 연결"""
        while self.running:
            try:
                logger.info(f"Connecting to ollama-server WebSocket: {settings.ollama_websocket_url}")
                
                async with websockets.connect(settings.ollama_websocket_url) as websocket:
                    self.ollama_connection = websocket
                    logger.info("Connected to ollama-server WebSocket")
                    
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            await self.broadcast_ollama_message(data)
                        except json.JSONDecodeError as e:
                            logger.error("Invalid JSON from ollama-server", error=str(e))
                        except Exception as e:
                            logger.error("Error processing ollama message", error=str(e))
                            
            except Exception as e:
                logger.error("ollama-server WebSocket connection failed", error=str(e))
                self.ollama_connection = None
                
                if self.running:
                    logger.info("Retrying ollama-server connection in 5 seconds...")
                    await asyncio.sleep(5)
    
    async def broadcast_ollama_message(self, data: Dict[str, Any]):
        """ollama-server 메시지를 모든 클라이언트에 브로드캐스트"""
        if not self.clients:
            return
        
        message = {
            "type": "ollama_update",
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        # 모든 클라이언트에 메시지 전송
        disconnected_clients = set()
        
        for client in self.clients:
            try:
                await client.send(json.dumps(message))
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logger.error("Failed to send message to client", error=str(e))
                disconnected_clients.add(client)
        
        # 연결이 끊어진 클라이언트 제거
        self.clients -= disconnected_clients
        
        if disconnected_clients:
            logger.debug(f"Removed {len(disconnected_clients)} disconnected clients")
    
    async def broadcast_to_clients(self, message: Dict[str, Any]):
        """모든 클라이언트에 메시지 브로드캐스트"""
        if not self.clients:
            return
        
        message["timestamp"] = asyncio.get_event_loop().time()
        
        disconnected_clients = set()
        
        for client in self.clients:
            try:
                await client.send(json.dumps(message))
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logger.error("Failed to broadcast message", error=str(e))
                disconnected_clients.add(client)
        
        # 연결이 끊어진 클라이언트 제거
        self.clients -= disconnected_clients
    
    def get_stats(self) -> Dict[str, Any]:
        """WebSocket 서비스 통계"""
        return {
            "connected_clients": len(self.clients),
            "ollama_connected": self.ollama_connection is not None,
            "server_running": self.running,
            "server_address": f"{settings.websocket_host}:{settings.websocket_port}"
        }

# 전역 WebSocket 서비스 인스턴스
_websocket_service = None

async def get_websocket_service() -> WebSocketService:
    """WebSocket 서비스 인스턴스 반환"""
    global _websocket_service
    if _websocket_service is None:
        _websocket_service = WebSocketService()
    return _websocket_service
