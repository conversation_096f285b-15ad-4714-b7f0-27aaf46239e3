# 도우미 함수들
# - 데이터 변환 함수
# - 유틸리티 함수들
# - 공통 처리 함수들

from typing import Dict, Any, List, Optional
import re
import hashlib
from datetime import datetime

def normalize_search_results(results: List[Dict[str, Any]], source: str) -> List[Dict[str, Any]]:
    """검색 결과를 표준 형식으로 정규화합니다."""
    normalized = []
    
    for result in results:
        normalized_result = {
            "id": str(result.get("id", "")),
            "content": str(result.get("content", "")),
            "score": float(result.get("score", 0.0)),
            "similarity": float(result.get("similarity", result.get("score", 0.0))),
            "metadata": result.get("metadata", {}),
            "source": source,
            "timestamp": datetime.now().isoformat()
        }
        normalized.append(normalized_result)
    
    return normalized

def merge_search_results(results_list: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
    """여러 검색 결과를 병합하고 중복을 제거합니다."""
    merged = []
    seen_ids = set()
    
    for results in results_list:
        for result in results:
            result_id = result.get("id", "")
            content = result.get("content", "")
            
            # ID나 내용으로 중복 확인
            unique_key = f"{result_id}_{hash_content(content)}"
            
            if unique_key not in seen_ids:
                seen_ids.add(unique_key)
                merged.append(result)
    
    return merged

def hash_content(content: str) -> str:
    """내용의 해시값을 생성합니다."""
    return hashlib.md5(content.encode('utf-8')).hexdigest()[:8]

def clean_query(query: str) -> str:
    """검색 쿼리를 정제합니다."""
    if not query:
        return ""
    
    # 특수문자 제거 및 공백 정리
    cleaned = re.sub(r'[^\w\s가-힣]', ' ', query)
    cleaned = re.sub(r'\s+', ' ', cleaned)
    return cleaned.strip()

def calculate_relevance_score(
    text_score: float, 
    vector_score: float, 
    text_weight: float = 0.6, 
    vector_weight: float = 0.4
) -> float:
    """텍스트와 벡터 점수를 결합하여 관련성 점수를 계산합니다."""
    return (text_score * text_weight) + (vector_score * vector_weight)

def format_response_time(time_ms: float) -> str:
    """응답 시간을 사람이 읽기 쉬운 형식으로 변환합니다."""
    if time_ms < 1000:
        return f"{time_ms:.2f}ms"
    else:
        return f"{time_ms/1000:.2f}s"

def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """텍스트에서 키워드를 추출합니다."""
    if not text:
        return []
    
    # 간단한 키워드 추출 (실제로는 더 복잡한 NLP 처리 가능)
    words = re.findall(r'\b\w{3,}\b', text.lower())
    
    # 불용어 제거 (간단한 예시)
    stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by'}
    keywords = [word for word in words if word not in stop_words]
    
    # 빈도순 정렬 후 상위 키워드 반환
    from collections import Counter
    keyword_counts = Counter(keywords)
    return [word for word, _ in keyword_counts.most_common(max_keywords)]

def paginate_results(
    results: List[Dict[str, Any]], 
    offset: int = 0, 
    limit: int = 10
) -> Dict[str, Any]:
    """검색 결과를 페이지네이션합니다."""
    total = len(results)
    start = max(0, offset)
    end = min(total, start + limit)
    
    return {
        "results": results[start:end],
        "pagination": {
            "total": total,
            "offset": start,
            "limit": limit,
            "has_more": end < total
        }
    }

def validate_search_params(query: str, limit: int, offset: int) -> Dict[str, Any]:
    """검색 매개변수의 유효성을 검사합니다."""
    errors = []
    
    if not query or len(query.strip()) < 2:
        errors.append("Query must be at least 2 characters long")
    
    if limit < 1 or limit > 100:
        errors.append("Limit must be between 1 and 100")
    
    if offset < 0:
        errors.append("Offset must be non-negative")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors
    }

def build_search_filters(params: Dict[str, Any]) -> Dict[str, Any]:
    """검색 매개변수에서 필터를 구성합니다."""
    filters = {}
    
    # 날짜 범위 필터
    if params.get("start_date") and params.get("end_date"):
        filters["date_range"] = {
            "gte": params["start_date"],
            "lte": params["end_date"]
        }
    
    # 카테고리 필터
    if params.get("category"):
        filters["category"] = params["category"]
    
    # 태그 필터
    if params.get("tags"):
        filters["tags"] = params["tags"]
    
    return filters
