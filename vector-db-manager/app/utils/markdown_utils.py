# 마크다운 및 파일 처리 유틸리티
# - 마크다운 파일 파싱
# - 폴더 배치 처리
# - 파일 검증 및 전처리
# - 메타데이터 추출 지원

import os
import asyncio
import aiofiles
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import magic
import hashlib
from datetime import datetime
from ..core.logging import get_logger

logger = get_logger(__name__)

class MarkdownProcessor:
    """마크다운 파일 처리 클래스"""
    
    @staticmethod
    def extract_title_from_content(content: str) -> Optional[str]:
        """마크다운 내용에서 제목 추출"""
        lines = content.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        return None
    
    @staticmethod
    def clean_content(content: str) -> str:
        """마크다운 내용 정리"""
        # 불필요한 공백 제거
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # 탭을 스페이스로 변환
            line = line.replace('\t', '    ')
            cleaned_lines.append(line.rstrip())
        
        # 연속된 빈 줄을 하나로 축소
        result = []
        prev_empty = False
        
        for line in cleaned_lines:
            if not line.strip():
                if not prev_empty:
                    result.append('')
                prev_empty = True
            else:
                result.append(line)
                prev_empty = False
        
        return '\n'.join(result).strip()
    
    @staticmethod
    def calculate_file_hash(content: str) -> str:
        """파일 내용의 해시값 계산"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    def validate_markdown_file(file_path: Path) -> Tuple[bool, str]:
        """마크다운 파일 검증"""
        try:
            # 파일 존재 확인
            if not file_path.exists():
                return False, "File does not exist"
            
            # 파일 크기 확인 (10MB 제한)
            if file_path.stat().st_size > 10 * 1024 * 1024:
                return False, "File too large (max 10MB)"
            
            # 파일 확장자 확인
            if file_path.suffix.lower() not in ['.md', '.markdown']:
                return False, "Not a markdown file"
            
            # 파일 타입 확인 (magic number)
            try:
                file_type = magic.from_file(str(file_path), mime=True)
                if not file_type.startswith('text/'):
                    return False, "File is not a text file"
            except:
                # magic이 실패하면 확장자로만 판단
                pass
            
            return True, "Valid markdown file"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"

class BatchProcessor:
    """배치 처리 클래스"""
    
    def __init__(self):
        self.supported_extensions = {'.md', '.markdown'}
    
    async def scan_directory(self, directory_path: str, recursive: bool = True) -> List[Path]:
        """디렉토리에서 마크다운 파일 스캔"""
        try:
            path = Path(directory_path)
            if not path.exists() or not path.is_dir():
                logger.error("Invalid directory path", path=directory_path)
                return []
            
            files = []
            pattern = "**/*.md" if recursive else "*.md"
            
            for file_path in path.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                    # 파일 검증
                    is_valid, error = MarkdownProcessor.validate_markdown_file(file_path)
                    if is_valid:
                        files.append(file_path)
                    else:
                        logger.warning("Invalid file skipped", file=str(file_path), error=error)
            
            logger.info(f"Found {len(files)} markdown files", directory=directory_path)
            return files
            
        except Exception as e:
            logger.error("Directory scan failed", directory=directory_path, error=str(e))
            return []
    
    async def read_file_async(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """비동기로 파일 읽기"""
        try:
            async with aiofiles.open(file_path, mode='r', encoding='utf-8') as file:
                content = await file.read()
            
            # 내용 정리
            cleaned_content = MarkdownProcessor.clean_content(content)
            
            # 기본 메타데이터 생성
            stat = file_path.stat()
            
            return {
                'file_path': str(file_path),
                'filename': file_path.name,
                'content': cleaned_content,
                'size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'hash': MarkdownProcessor.calculate_file_hash(cleaned_content),
                'extracted_title': MarkdownProcessor.extract_title_from_content(cleaned_content)
            }
            
        except UnicodeDecodeError as e:
            logger.error("File encoding error", file=str(file_path), error=str(e))
            return None
        except Exception as e:
            logger.error("Failed to read file", file=str(file_path), error=str(e))
            return None
    
    async def process_files_batch(self, file_paths: List[Path], batch_size: int = 5) -> List[Optional[Dict[str, Any]]]:
        """파일들을 배치로 처리"""
        results = []
        
        for i in range(0, len(file_paths), batch_size):
            batch = file_paths[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}", files=len(batch))
            
            # 배치 내 파일들을 병렬로 처리
            tasks = [self.read_file_async(file_path) for file_path in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error("Batch processing error", error=str(result))
                    results.append(None)
                else:
                    results.append(result)
            
            # 배치 간 짧은 대기 (시스템 부하 방지)
            if i + batch_size < len(file_paths):
                await asyncio.sleep(0.1)
        
        return results
    
    def filter_duplicates(self, file_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """중복 파일 제거 (해시 기반)"""
        seen_hashes = set()
        unique_files = []
        
        for file_data in file_data_list:
            if file_data is None:
                continue
                
            file_hash = file_data.get('hash')
            if file_hash not in seen_hashes:
                seen_hashes.add(file_hash)
                unique_files.append(file_data)
            else:
                logger.info("Duplicate file detected", file=file_data.get('filename'))
        
        logger.info(f"Filtered {len(file_data_list) - len(unique_files)} duplicates")
        return unique_files
    
    async def estimate_processing_time(self, file_count: int) -> Dict[str, Any]:
        """처리 시간 추정"""
        # 파일당 평균 처리 시간 (Ollama 포함)
        avg_time_per_file = 15.0  # 초
        
        total_time = file_count * avg_time_per_file
        
        return {
            'file_count': file_count,
            'estimated_time_seconds': total_time,
            'estimated_time_minutes': total_time / 60,
            'avg_time_per_file': avg_time_per_file
        }

# 전역 인스턴스
markdown_processor = MarkdownProcessor()
batch_processor = BatchProcessor()
