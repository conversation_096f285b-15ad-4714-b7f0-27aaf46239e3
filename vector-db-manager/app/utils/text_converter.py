# 텍스트 변환 및 감지 유틸리티
# - 텍스트 타입 자동 감지
# - HTML/리치텍스트 → 마크다운 변환  
# - Notion 특화 형식 처리
# - 마크다운 정리 및 최적화

import re
import html
from typing import Dict, Any, Optional, Tuple
from enum import Enum
import bleach
from bs4 import BeautifulSoup
from markdownify import markdownify as md
import html2text
from ..core.logging import get_logger

logger = get_logger(__name__)

class TextType(str, Enum):
    """텍스트 타입 열거형"""
    MARKDOWN = "markdown"
    HTML = "html"
    RICH_TEXT = "rich_text"
    NOTION_FORMAT = "notion_format"
    PLAIN_TEXT = "plain_text"

class TextTypeDetector:
    """텍스트 타입 감지 클래스"""
    
    @staticmethod
    def detect_type(text: str) -> TextType:
        """텍스트 타입을 자동으로 감지"""
        text_clean = text.strip()
        
        # HTML 태그 감지
        if TextTypeDetector._is_html(text_clean):
            return TextType.HTML
        
        # 마크다운 패턴 감지
        if TextTypeDetector._is_markdown(text_clean):
            return TextType.MARKDOWN
        
        # Notion 특화 형식 감지
        if TextTypeDetector._is_notion_format(text_clean):
            return TextType.NOTION_FORMAT
        
        # 리치 텍스트 패턴 감지
        if TextTypeDetector._is_rich_text(text_clean):
            return TextType.RICH_TEXT
        
        return TextType.PLAIN_TEXT
    
    @staticmethod
    def _is_html(text: str) -> bool:
        """HTML 형식인지 확인"""
        html_patterns = [
            r'<[^>]+>',  # HTML 태그
            r'&\w+;',    # HTML 엔티티
        ]
        
        for pattern in html_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    @staticmethod
    def _is_markdown(text: str) -> bool:
        """마크다운 형식인지 확인"""
        markdown_patterns = [
            r'^#{1,6}\s+',           # 헤더
            r'^\*{1,3}[^*]+\*{1,3}', # 볼드/이탤릭
            r'^\- |\* |\+ ',         # 리스트
            r'^\d+\. ',              # 번호 리스트
            r'```',                  # 코드 블록
            r'`[^`]+`',              # 인라인 코드
            r'\[.+\]\(.+\)',         # 링크
            r'!\[.*\]\(.+\)',        # 이미지
        ]
        
        lines = text.split('\n')
        markdown_score = 0
        
        for line in lines:
            for pattern in markdown_patterns:
                if re.search(pattern, line.strip(), re.MULTILINE):
                    markdown_score += 1
                    break
        
        # 전체 라인 대비 마크다운 패턴 비율
        return markdown_score / max(len(lines), 1) > 0.3
    
    @staticmethod
    def _is_notion_format(text: str) -> bool:
        """Notion 특화 형식인지 확인"""
        notion_patterns = [
            r'□\s+',                    # 체크박스 (빈)
            r'☑\s+',                    # 체크박스 (체크됨)
            r'→\s+',                    # 화살표
            r'📅\s+',                   # 날짜 이모지
            r'🔗\s+',                   # 링크 이모지
            r'\|\s+\w+\s+\|',          # 테이블 구분자
        ]
        
        for pattern in notion_patterns:
            if re.search(pattern, text, re.MULTILINE):
                return True
        return False
    
    @staticmethod
    def _is_rich_text(text: str) -> bool:
        """리치 텍스트 형식인지 확인"""
        # 특수 문자나 유니코드 문자가 많은 경우
        special_chars = re.findall(r'[^\w\s\-.,!?(){}[\]"\'/:;]', text)
        return len(special_chars) / max(len(text), 1) > 0.05

class TextConverter:
    """텍스트 변환 클래스"""
    
    def __init__(self):
        # HTML to text 변환기 설정
        self.h2t = html2text.HTML2Text()
        self.h2t.ignore_links = False
        self.h2t.ignore_images = False
        self.h2t.ignore_emphasis = False
        self.h2t.body_width = 0  # 줄바꿈 비활성화
    
    def convert_to_markdown(self, text: str, source_type: Optional[TextType] = None) -> str:
        """텍스트를 마크다운으로 변환"""
        try:
            # 타입이 지정되지 않은 경우 자동 감지
            if source_type is None:
                source_type = TextTypeDetector.detect_type(text)
            
            logger.info(f"Converting text from {source_type} to markdown")
            
            if source_type == TextType.MARKDOWN:
                return self._clean_markdown(text)
            elif source_type == TextType.HTML:
                return self._html_to_markdown(text)
            elif source_type == TextType.RICH_TEXT:
                return self._rich_text_to_markdown(text)
            elif source_type == TextType.NOTION_FORMAT:
                return self._notion_to_markdown(text)
            else:  # PLAIN_TEXT
                return self._plain_text_to_markdown(text)
                
        except Exception as e:
            logger.error("Text conversion failed", error=str(e))
            return text  # 변환 실패 시 원본 반환
    
    def _html_to_markdown(self, html_text: str) -> str:
        """HTML을 마크다운으로 변환"""
        try:
            # BeautifulSoup으로 HTML 정리
            soup = BeautifulSoup(html_text, 'html.parser')
            
            # 불필요한 태그 제거
            for tag in soup(['script', 'style', 'meta', 'link']):
                tag.decompose()
            
            clean_html = str(soup)
            
            # markdownify로 변환
            markdown = md(clean_html, heading_style="ATX", bullets="-")
            
            return self._clean_markdown(markdown)
            
        except Exception as e:
            logger.warning("HTML conversion failed, trying html2text", error=str(e))
            return self.h2t.handle(html_text)
    
    def _rich_text_to_markdown(self, rich_text: str) -> str:
        """리치 텍스트를 마크다운으로 변환"""
        text = rich_text
        
        # 일반적인 리치 텍스트 패턴 변환
        conversions = [
            # 볼드체 변환
            (r'(\*\*|__)(.*?)\1', r'**\2**'),
            # 이탤릭체 변환  
            (r'(\*|_)(.*?)\1', r'*\2*'),
            # 코드 변환
            (r'`([^`]+)`', r'`\1`'),
            # 링크 변환
            (r'https?://[^\s]+', r'[\0](\0)'),
        ]
        
        for pattern, replacement in conversions:
            text = re.sub(pattern, replacement, text)
        
        return self._clean_markdown(text)
    
    def _notion_to_markdown(self, notion_text: str) -> str:
        """Notion 형식을 마크다운으로 변환"""
        text = notion_text
        
        # Notion 특화 변환
        conversions = [
            # 체크박스
            (r'□\s+', '- [ ] '),
            (r'☑\s+', '- [x] '),
            # 화살표를 리스트로
            (r'→\s+', '- '),
            # 이모지 패턴 정리
            (r'📅\s+', '**날짜:** '),
            (r'🔗\s+', '**링크:** '),
            # 테이블 구분자 개선
            (r'\|\s+(\w+)\s+\|', r'| \1 |'),
        ]
        
        for pattern, replacement in conversions:
            text = re.sub(pattern, replacement, text, flags=re.MULTILINE)
        
        return self._clean_markdown(text)
    
    def _plain_text_to_markdown(self, plain_text: str) -> str:
        """일반 텍스트를 마크다운으로 변환"""
        lines = plain_text.split('\n')
        result_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                result_lines.append('')
                continue
            
            # 간단한 구조화 시도
            if line.isupper() and len(line) < 50:
                # 전체 대문자인 짧은 라인을 헤더로
                result_lines.append(f"## {line.title()}")
            elif line.endswith(':') and len(line) < 50:
                # 콜론으로 끝나는 라인을 소제목으로
                result_lines.append(f"### {line}")
            else:
                result_lines.append(line)
        
        return '\n'.join(result_lines)
    
    def _clean_markdown(self, markdown_text: str) -> str:
        """마크다운 텍스트 정리"""
        text = markdown_text
        
        # 정리 작업
        cleanups = [
            # 연속된 빈 줄 축소
            (r'\n{3,}', '\n\n'),
            # 불필요한 공백 제거
            (r'[ \t]+\n', '\n'),
            # 마크다운 문법 정리
            (r'^[ \t]*#[ \t]*', '# ', re.MULTILINE),
            # 리스트 정리
            (r'^[ \t]*[-*+][ \t]*', '- ', re.MULTILINE),
        ]
        
        for pattern, replacement, *flags in cleanups:
            flag = flags[0] if flags else 0
            text = re.sub(pattern, replacement, text, flags=flag)
        
        return text.strip()

class SmartTextProcessor:
    """스마트 텍스트 처리 통합 클래스"""
    
    def __init__(self):
        self.detector = TextTypeDetector()
        self.converter = TextConverter()
    
    def process_text(self, text: str, force_conversion: bool = False) -> Dict[str, Any]:
        """텍스트를 분석하고 처리"""
        try:
            # 텍스트 타입 감지
            detected_type = self.detector.detect_type(text)
            
            # 변환 여부 결정
            needs_conversion = force_conversion or detected_type != TextType.MARKDOWN
            
            if needs_conversion:
                converted_text = self.converter.convert_to_markdown(text, detected_type)
            else:
                converted_text = self.converter._clean_markdown(text)
            
            # 기본 메타데이터 추출
            basic_metadata = self._extract_basic_metadata(converted_text)
            
            return {
                'original_text': text,
                'converted_text': converted_text,
                'detected_type': detected_type.value,
                'was_converted': needs_conversion,
                'basic_metadata': basic_metadata,
                'processing_successful': True
            }
            
        except Exception as e:
            logger.error("Smart text processing failed", error=str(e))
            return {
                'original_text': text,
                'converted_text': text,
                'detected_type': 'unknown',
                'was_converted': False,
                'basic_metadata': {},
                'processing_successful': False,
                'error': str(e)
            }
    
    def _extract_basic_metadata(self, markdown_text: str) -> Dict[str, Any]:
        """기본 메타데이터 추출"""
        metadata = {
            'estimated_title': None,
            'has_code_blocks': False,
            'has_links': False,
            'has_images': False,
            'line_count': 0,
            'word_count': 0
        }
        
        lines = markdown_text.split('\n')
        metadata['line_count'] = len(lines)
        metadata['word_count'] = len(markdown_text.split())
        
        # 제목 추출 시도
        for line in lines:
            if line.strip().startswith('# '):
                metadata['estimated_title'] = line.strip()[2:].strip()
                break
            elif line.strip().startswith('## '):
                metadata['estimated_title'] = line.strip()[3:].strip()
                break
        
        # 패턴 감지
        metadata['has_code_blocks'] = '```' in markdown_text
        metadata['has_links'] = bool(re.search(r'\[.+\]\(.+\)', markdown_text))
        metadata['has_images'] = bool(re.search(r'!\[.*\]\(.+\)', markdown_text))
        
        return metadata

# 전역 인스턴스
smart_text_processor = SmartTextProcessor()
