# 유효성 검사 함수들
# - 입력 데이터 검증
# - 데이터베이스 연결 검증
# - 설정 검증

from typing import Any, Dict, List
from ..core.exceptions import ValidationError

def validate_search_query(query: str) -> bool:
    """검색 쿼리의 유효성을 검사합니다."""
    if not query or not query.strip():
        raise ValidationError("query", "Query cannot be empty")
    
    if len(query.strip()) < 2:
        raise ValidationError("query", "Query must be at least 2 characters long")
    
    if len(query) > 1000:
        raise ValidationError("query", "Query must be less than 1000 characters")
    
    return True

def validate_vector(vector: List[float], expected_dimension: int = None) -> bool:
    """벡터의 유효성을 검사합니다."""
    if not vector:
        raise ValidationError("vector", "Vector cannot be empty")
    
    if not all(isinstance(x, (int, float)) for x in vector):
        raise ValidationError("vector", "Vector must contain only numbers")
    
    if expected_dimension and len(vector) != expected_dimension:
        raise ValidationError("vector", f"Vector dimension mismatch. Expected: {expected_dimension}, Got: {len(vector)}")
    
    return True

def validate_database_config(config: Dict[str, Any]) -> bool:
    """데이터베이스 설정의 유효성을 검사합니다."""
    required_fields = ['url']
    
    for field in required_fields:
        if field not in config:
            raise ValidationError(field, f"Required field '{field}' is missing")
    
    # URL 형식 기본 검증
    url = config.get('url', '')
    if not url.startswith(('http://', 'https://', 'mongodb://')):
        raise ValidationError("url", "Invalid URL format")
    
    return True

# TODO: 더 많은 검증 함수들 추가
