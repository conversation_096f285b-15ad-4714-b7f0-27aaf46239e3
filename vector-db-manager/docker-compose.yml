services:
  # Vector DB Manager FastAPI 애플리케이션
  vectordb-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vector-db-manager-app
    ports:
      - "7200:8000"  # 외부 포트 7200, 내부 포트 8000
    environment:
      - DEBUG=true
      - LOG_LEVEL=INFO
      
      # Vector Databases (외부 네트워크의 다른 컨테이너들)
      - WEAVIATE_URL=http://weaviate:8080
      - QDRANT_URL=http://qdrant:6333
      - CHROMA_URL=http://chroma:8000
      
      # Traditional Databases (외부 네트워크의 다른 컨테이너들)
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - MEILISEARCH_URL=http://meilisearch:7700
      - MONGODB_URL=**************************************/
      
      # API 설정
      - API_V1_PREFIX=/api/v1
      - SECRET_KEY=your-secret-key-change-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      
    volumes:
      # 개발 모드에서 코드 변경사항 반영을 위한 볼륨 마운트 (선택사항)
      - ./app:/app/app:ro
      - ./logs:/app/logs
      
    networks:
      - dbs-network
      
    restart: unless-stopped
    
    # 헬스체크 설정
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 외부 네트워크 사용 (데이터베이스 컨테이너들과 동일한 네트워크)
networks:
  dbs-network:
    external: true
    name: dbs_dbs-network
