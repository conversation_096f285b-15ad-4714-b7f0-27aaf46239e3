services:
  # Weaviate 벡터 데이터베이스
  weaviate:
    image: semitechnologies/weaviate:latest
    container_name: weaviate
    ports:
      - "7210:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 20
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: ''
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      CLUSTER_HOSTNAME: 'weaviate-node1'
    volumes:
      - ./data/weaviate:/var/lib/weaviate
    networks:
      - dbs-network
    restart: unless-stopped

  # Qdrant 벡터 데이터베이스
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    ports:
      - "7211:6333"
    volumes:
      - ./data/qdrant:/qdrant/storage
    networks:
      - dbs-network
    restart: unless-stopped

  # ChromaDB 벡터 데이터베이스
  chroma:
    image: ghcr.io/chroma-core/chroma:latest
    container_name: chroma
    ports:
      - "7212:8000"
    environment:
      - CHROMA_SERVER_ENABLE_MULTI_TENANCY=false
      - CHROMA_SERVER_ENABLE_TELEMETRY=false
    volumes:
      - ./data/chroma:/chroma/chroma
    networks:
      - dbs-network
    restart: unless-stopped

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.0
    container_name: elasticsearch
    ports:
      - "7213:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - ./data/elasticsearch:/usr/share/elasticsearch/data
    networks:
      - dbs-network
    restart: unless-stopped

  # Meilisearch
  meilisearch:
    image: getmeili/meilisearch:latest
    container_name: meilisearch
    ports:
      - "7214:7700"
    environment:
      - MEILI_ENV=development
      - MEILI_MASTER_KEY=your-master-key
      - MEILI_NO_ANALYTICS=true
    volumes:
      - ./data/meilisearch:/meili_data
    networks:
      - dbs-network
    restart: unless-stopped

  # MongoDB
  mongodb:
    image: mongo:6.0
    container_name: mongodb
    ports:
      - "7215:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - ./data/mongodb:/data/db
    networks:
      - dbs-network
    restart: unless-stopped

  # FastAPI 애플리케이션 (선택사항, 개발 시)
  vectordb-manager:
    build: 
      context: ..
      dockerfile: docker/Dockerfile
    container_name: vector-db-manager
    ports:
      - "7200:8000"  # 외부 포트 7200, 내부 포트 8000
    environment:
      - DEBUG=true
      - WEAVIATE_URL=http://weaviate:8080
      - QDRANT_URL=http://qdrant:6333
      - CHROMA_URL=http://chroma:8000
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - MEILISEARCH_URL=http://meilisearch:7700
      - MONGODB_URL=**************************************/
    depends_on:
      - weaviate
      - qdrant
      - chroma
      - elasticsearch
      - meilisearch
      - mongodb
    networks:
      - dbs-network
    restart: unless-stopped

networks:
  dbs-network:
    driver: bridge
