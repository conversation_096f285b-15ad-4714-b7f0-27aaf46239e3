# Notion 마크다운 텍스트 처리 API 문서

## 📋 개요

이 문서는 Notion 마크다운 문서 관리 시스템에 추가된 텍스트 직접 입력 및 스마트 변환 기능에 대한 상세 API 문서입니다.

### 🎯 주요 기능

1. **텍스트 직접 입력** - Frontend에서 복사한 텍스트를 바로 문서로 생성
2. **스마트 텍스트 변환** - 다양한 형식의 텍스트를 마크다운으로 자동 변환
3. **기존 기능 유지** - 파일 업로드 기능과 완전 호환

---

## 🚀 새로운 API 엔드포인트

### 1. 텍스트로 문서 생성

**엔드포인트:** `POST /api/v1/notion/create-from-text`

**설명:** 사용자가 입력한 텍스트(마크다운, HTML, Notion 형식 등)를 자동으로 분석하고 변환하여 문서를 생성합니다.

#### 요청 구조

```json
{
  "text": "string (필수)",
  "title": "string (선택사항)",
  "category": "string (선택사항)",
  "keywords": ["string"] (선택사항),
  "auto_process": true,
  "convert_to_markdown": true,
  "source_description": "string (선택사항)"
}
```

#### 요청 필드 설명

| 필드 | 타입 | 필수 | 기본값 | 설명 |
|------|------|------|--------|------|
| `text` | string | ✅ | - | 입력 텍스트 (마크다운, HTML, Notion 형식 등) |
| `title` | string | ❌ | null | 문서 제목 (미입력시 자동 추출) |
| `category` | string | ❌ | null | 카테고리 (미입력시 Ollama가 자동 분류) |
| `keywords` | array | ❌ | null | 키워드 목록 (미입력시 Ollama가 자동 추출) |
| `auto_process` | boolean | ❌ | true | Ollama 자동 메타데이터 추출 여부 |
| `convert_to_markdown` | boolean | ❌ | true | 마크다운 변환 여부 |
| `source_description` | string | ❌ | null | 텍스트 출처 설명 (예: "LLM 응답", "Notion 복사본") |

#### 응답 구조

```json
{
  "success": true,
  "message": "Document created from text successfully",
  "data": {
    "id": "string",
    "title": "string",
    "category": "string",
    "keywords": ["string"],
    "content": "string",
    "summary": "string",
    "metadata": {
      "source": "text-input",
      "created_at": "datetime",
      "processing_status": "pending|processing|completed|failed"
    },
    "text_processing_info": {
      "detected_type": "markdown|html|notion_format|rich_text|plain_text",
      "was_converted": true,
      "source_description": "string",
      "basic_metadata": {
        "estimated_title": "string",
        "has_code_blocks": true,
        "has_links": true,
        "has_images": false,
        "line_count": 42,
        "word_count": 156
      }
    }
  }
}
```

#### 사용 예시

**마크다운 텍스트 입력:**
```bash
curl -X POST "http://localhost:7200/api/v1/notion/create-from-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "# AI 학습 노트\n\n## 딥러닝 기초\n- 신경망\n- 역전파\n\n```python\nprint(\"Hello AI\")\n```",
    "auto_process": true,
    "source_description": "개인 학습 노트"
  }'
```

**HTML 텍스트 입력:**
```bash
curl -X POST "http://localhost:7200/api/v1/notion/create-from-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "<h1>웹 개발</h1><p><strong>React</strong>와 <em>Node.js</em></p><ul><li>프론트엔드</li><li>백엔드</li></ul>",
    "convert_to_markdown": true,
    "source_description": "웹사이트 복사본"
  }'
```

**Notion 형식 텍스트 입력:**
```bash
curl -X POST "http://localhost:7200/api/v1/notion/create-from-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "프로젝트 할일\n\n□ 기획서 작성\n☑ 디자인 완료\n→ 개발 진행\n📅 2024-01-15",
    "title": "프로젝트 관리",
    "category": "업무",
    "source_description": "Notion 복사본"
  }'
```

---

### 2. 텍스트 변환 미리보기

**엔드포인트:** `POST /api/v1/notion/convert-text`

**설명:** 텍스트를 마크다운으로 변환한 결과를 미리 확인할 수 있습니다. 실제 문서 생성 전에 변환 결과를 검토하는 용도입니다.

#### 요청 구조

```json
{
  "text": "string (필수)",
  "target_format": "markdown",
  "source_type": "string (선택사항)",
  "force_conversion": false
}
```

#### 요청 필드 설명

| 필드 | 타입 | 필수 | 기본값 | 설명 |
|------|------|------|--------|------|
| `text` | string | ✅ | - | 변환할 텍스트 |
| `target_format` | string | ❌ | "markdown" | 대상 형식 (현재 markdown만 지원) |
| `source_type` | string | ❌ | null | 소스 타입 힌트 (자동 감지 대신 지정) |
| `force_conversion` | boolean | ❌ | false | 이미 마크다운이어도 강제 변환 |

#### 응답 구조

```json
{
  "success": true,
  "message": "Text converted successfully",
  "data": {
    "original_text": "string",
    "converted_text": "string",
    "detected_type": "markdown|html|notion_format|rich_text|plain_text",
    "was_converted": true,
    "basic_metadata": {
      "estimated_title": "string",
      "has_code_blocks": false,
      "has_links": true,
      "has_images": false,
      "line_count": 15,
      "word_count": 89
    },
    "processing_successful": true,
    "error": null
  }
}
```

#### 사용 예시

```bash
curl -X POST "http://localhost:7200/api/v1/notion/convert-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "<h2>제목</h2><p>내용입니다.</p>",
    "force_conversion": true
  }'
```

---

## 🧠 스마트 텍스트 처리 시스템

### 텍스트 타입 자동 감지

시스템이 자동으로 감지하는 텍스트 타입:

| 타입 | 설명 | 감지 패턴 예시 |
|------|------|--------------|
| `markdown` | 마크다운 형식 | `# 제목`, `**볼드**`, `- 리스트`, `` `코드` `` |
| `html` | HTML 형식 | `<h1>`, `<p>`, `&nbsp;`, `<strong>` |
| `notion_format` | Notion 특화 형식 | `□ 체크박스`, `☑ 완료`, `→ 화살표`, `📅 날짜` |
| `rich_text` | 리치 텍스트 | 특수 문자가 많은 형식화된 텍스트 |
| `plain_text` | 일반 텍스트 | 특별한 형식이 없는 순수 텍스트 |

### 변환 규칙

#### HTML → 마크다운 변환
```html
<!-- 입력 -->
<h1>제목</h1>
<p>이것은 <strong>중요한</strong> 내용입니다.</p>
<ul>
  <li>항목 1</li>
  <li>항목 2</li>
</ul>
<code>console.log('hello');</code>
```

```markdown
<!-- 출력 -->
# 제목

이것은 **중요한** 내용입니다.

- 항목 1
- 항목 2

`console.log('hello');`
```

#### Notion → 마크다운 변환
```
<!-- 입력 -->
프로젝트 관리

□ 할 일 1
☑ 완료된 할 일
→ 진행 중인 작업
📅 2024-01-15
🔗 https://example.com
```

```markdown
<!-- 출력 -->
프로젝트 관리

- [ ] 할 일 1
- [x] 완료된 할 일
- 진행 중인 작업
**날짜:** 2024-01-15
**링크:** https://example.com
```

---

## 🔄 처리 워크플로우

### 1. 텍스트 입력 → 문서 생성 플로우

```mermaid
graph TB
    A[사용자 텍스트 입력] --> B[텍스트 타입 자동 감지]
    B --> C{변환 필요?}
    C -->|Yes| D[스마트 변환 수행]
    C -->|No| E[마크다운 정리만 수행]
    D --> F[통합된 마크다운 생성]
    E --> F
    F --> G[기본 메타데이터 추출]
    G --> H[MongoDB 저장]
    H --> I{auto_process?}
    I -->|Yes| J[Ollama 백그라운드 처리]
    I -->|No| K[처리 완료]
    J --> L[메타데이터 자동 추출]
    L --> M[문서 업데이트]
    M --> K
```

### 2. 백그라운드 처리 상태

| 상태 | 설명 | 다음 단계 |
|------|------|-----------|
| `pending` | 처리 대기 중 | → `processing` |
| `processing` | Ollama 메타데이터 추출 중 | → `completed` or `failed` |
| `completed` | 모든 처리 완료 | - |
| `failed` | 처리 실패 | 수동 재처리 가능 |

---

## 📱 Frontend 통합 가이드

### React/JavaScript 예시

#### 1. 텍스트 에디터 통합

```javascript
import React, { useState } from 'react';

const TextEditor = () => {
  const [text, setText] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handlePaste = async (e) => {
    const pastedText = e.clipboardData.getData('text');
    setText(pastedText);
    
    // 자동 변환 미리보기
    const preview = await convertText(pastedText);
    console.log('변환 미리보기:', preview);
  };

  const createDocument = async () => {
    setIsLoading(true);
    try {
      const result = await fetch('/api/v1/notion/create-from-text', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: text,
          auto_process: true,
          convert_to_markdown: true,
          source_description: 'Frontend 에디터'
        })
      });
      
      const response = await result.json();
      console.log('문서 생성 완료:', response);
    } catch (error) {
      console.error('문서 생성 실패:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <textarea
        value={text}
        onChange={(e) => setText(e.target.value)}
        onPaste={handlePaste}
        placeholder="텍스트를 붙여넣기하세요..."
        rows={10}
        cols={50}
      />
      <button onClick={createDocument} disabled={isLoading}>
        {isLoading ? '처리 중...' : '문서 생성'}
      </button>
    </div>
  );
};

const convertText = async (text) => {
  const response = await fetch('/api/v1/notion/convert-text', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ text, force_conversion: false })
  });
  return response.json();
};
```

#### 2. 클립보드 감지 및 자동 처리

```javascript
const ClipboardProcessor = () => {
  const [documents, setDocuments] = useState([]);

  // 클립보드 변화 감지
  useEffect(() => {
    const handleClipboard = async () => {
      try {
        const text = await navigator.clipboard.readText();
        if (text.length > 100) { // 충분한 길이의 텍스트만 처리
          const shouldProcess = confirm('클립보드 내용을 문서로 생성하시겠습니까?');
          if (shouldProcess) {
            await createFromClipboard(text);
          }
        }
      } catch (err) {
        console.log('클립보드 접근 권한 필요');
      }
    };

    // Ctrl+V 감지
    const handleKeyPress = (e) => {
      if (e.ctrlKey && e.key === 'v') {
        setTimeout(handleClipboard, 100);
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);

  const createFromClipboard = async (text) => {
    const response = await fetch('/api/v1/notion/create-from-text', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text,
        auto_process: true,
        source_description: '클립보드 자동 생성'
      })
    });
    
    const result = await response.json();
    if (result.success) {
      setDocuments(prev => [...prev, result.data]);
    }
  };

  return (
    <div>
      <h3>자동 생성된 문서들</h3>
      {documents.map(doc => (
        <div key={doc.id}>
          <h4>{doc.title}</h4>
          <p>타입: {doc.text_processing_info.detected_type}</p>
          <p>변환됨: {doc.text_processing_info.was_converted ? 'Yes' : 'No'}</p>
        </div>
      ))}
    </div>
  );
};
```

---

## 🛠️ 기술 구현 상세

### 새로 추가된 파일 구조

```
app/
├── utils/
│   ├── text_converter.py          # 텍스트 변환 핵심 로직
│   └── markdown_utils.py          # 기존 마크다운 처리 유틸리티
├── models/
│   └── notion.py                  # 새로운 Pydantic 모델 추가
├── services/
│   └── notion_service.py          # 텍스트 처리 메서드 추가
└── api/v1/endpoints/
    └── mongodb.py                 # 새로운 API 엔드포인트

tests/
└── test_utils/
    └── test_text_converter.py     # 텍스트 변환 테스트
```

### 주요 클래스 구조

#### TextTypeDetector
```python
class TextTypeDetector:
    @staticmethod
    def detect_type(text: str) -> TextType
    def _is_html(text: str) -> bool
    def _is_markdown(text: str) -> bool
    def _is_notion_format(text: str) -> bool
    def _is_rich_text(text: str) -> bool
```

#### TextConverter
```python
class TextConverter:
    def convert_to_markdown(text: str, source_type: TextType) -> str
    def _html_to_markdown(html_text: str) -> str
    def _notion_to_markdown(notion_text: str) -> str
    def _rich_text_to_markdown(rich_text: str) -> str
    def _clean_markdown(markdown_text: str) -> str
```

#### SmartTextProcessor
```python
class SmartTextProcessor:
    def process_text(text: str, force_conversion: bool) -> Dict[str, Any]
    def _extract_basic_metadata(markdown_text: str) -> Dict[str, Any]
```

### 의존성 패키지

새로 추가된 패키지들:
```txt
# Text Conversion
html2text==2020.1.16
markdownify==0.11.6
beautifulsoup4==4.12.2
bleach==6.1.0
```

---

## 🧪 테스트 및 검증

### 단위 테스트 실행

```bash
# 텍스트 변환 테스트
pytest tests/test_utils/test_text_converter.py -v

# 전체 Notion 서비스 테스트
pytest tests/test_services/test_notion_service.py -v
```

### API 테스트 시나리오

#### 1. 마크다운 텍스트 처리
```bash
curl -X POST "http://localhost:7200/api/v1/notion/create-from-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "# 테스트\n\n**볼드** 텍스트",
    "auto_process": false
  }'
```

#### 2. HTML 텍스트 변환
```bash
curl -X POST "http://localhost:7200/api/v1/notion/convert-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "<h1>HTML 제목</h1><p><em>이탤릭</em> 텍스트</p>"
  }'
```

#### 3. Notion 형식 변환
```bash
curl -X POST "http://localhost:7200/api/v1/notion/create-from-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "□ 할일\n☑ 완료",
    "title": "체크리스트",
    "category": "업무"
  }'
```

---

## 🔧 설정 및 환경변수

### Ollama 설정
```env
OLLAMA_URL=http://localhost:11434
OLLAMA_DEFAULT_MODEL=gemma3:1b
```

### MongoDB 설정
```env
MONGODB_URL=***************************************/
```

---

## 📊 성능 고려사항

### 처리 시간 예상

| 텍스트 타입 | 변환 시간 | Ollama 처리 시간 | 총 예상 시간 |
|------------|----------|------------------|-------------|
| 마크다운 | ~0.1초 | ~5-15초 | ~5-15초 |
| HTML | ~0.5초 | ~5-15초 | ~6-16초 |
| Notion 형식 | ~0.3초 | ~5-15초 | ~6-16초 |
| 리치 텍스트 | ~0.4초 | ~5-15초 | ~6-16초 |

### 최적화 전략

1. **백그라운드 처리**: Ollama 메타데이터 추출은 비동기로 처리
2. **캐싱**: 동일한 텍스트의 변환 결과 캐싱 고려
3. **배치 처리**: 여러 문서 동시 처리 시 제한적 병렬 처리
4. **타임아웃**: Ollama API 호출에 5분 타임아웃 설정

---

## 🐛 에러 처리 및 디버깅

### 일반적인 에러 상황

#### 1. 텍스트 변환 실패
```json
{
  "success": false,
  "error": "Text conversion failed: unsupported format"
}
```

**해결 방법**: `force_conversion=false`로 원본 텍스트 그대로 저장

#### 2. Ollama 서버 연결 실패
```json
{
  "success": true,
  "data": {
    "metadata": {
      "processing_status": "failed"
    }
  }
}
```

**해결 방법**: Ollama 서버 상태 확인 후 수동 재처리

#### 3. 텍스트 타입 감지 실패
```json
{
  "detected_type": "plain_text",
  "was_converted": false
}
```

**해결 방법**: `source_type` 파라미터로 수동 지정

### 디버깅 로그

```python
# 로그 레벨을 DEBUG로 설정하여 상세 정보 확인
LOG_LEVEL=DEBUG

# 주요 로그 포인트:
logger.info(f"Converting text from {source_type} to markdown")
logger.warning("Failed to parse JSON response", response=response_text[:200])
logger.error("Text conversion failed", error=str(e))
```

---

## 🚀 향후 확장 계획

### Phase 2 기능
- **이미지 텍스트 추출**: OCR을 통한 이미지 내 텍스트 변환
- **PDF 변환**: PDF 문서를 마크다운으로 변환
- **음성 변환**: 음성을 텍스트로 변환 후 문서 생성
- **실시간 협업**: 여러 사용자가 동시에 텍스트 편집

### Phase 3 기능
- **AI 기반 요약**: 긴 텍스트의 자동 요약 생성
- **번역 통합**: 다국어 텍스트 자동 번역 후 저장
- **템플릿 시스템**: 자주 사용하는 형식의 템플릿 제공
- **플러그인 아키텍처**: 커스텀 변환기 추가 지원

---

## 📞 지원 및 문의

### API 문제 신고
- **GitHub Issues**: 버그 리포트 및 기능 요청
- **로그 수집**: 에러 발생 시 관련 로그 첨부 필요

### 개발자 가이드
- **코드 기여**: Pull Request 환영
- **테스트 가이드**: 새 기능 추가 시 테스트 코드 필수
- **문서 업데이트**: API 변경 시 문서 동시 업데이트

---

*문서 버전: 1.0*  
*최종 업데이트: 2025-07-13*  
*API 버전: v1*
