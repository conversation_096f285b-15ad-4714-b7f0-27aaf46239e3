# TODO: Python 프로젝트 설정
# - 프로젝트 메타데이터
# - 빌드 설정
# - 도구 설정 (black, isort, mypy 등)

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "vector-db-manager"
version = "1.0.0"
description = "통합 벡터 데이터베이스 관리 시스템"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
