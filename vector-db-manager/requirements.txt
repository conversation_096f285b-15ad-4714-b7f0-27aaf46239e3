# Python 패키지 의존성 목록
# - FastAPI 및 관련 패키지
# - 각 데이터베이스 클라이언트 패키지
# - 유틸리티 패키지들

fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Vector DB Clients
weaviate-client==3.25.3
qdrant-client==1.7.0
chromadb==0.4.18
numpy>=1.21.0,<2.0.0  # ChromaDB compatibility fix

# Traditional DB Clients
elasticsearch[async]==8.11.1
meilisearch==0.25.0
pymongo==4.6.1
motor==3.3.2

# Utilities
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
aiofiles==23.2.1
httpx==0.25.2
python-dotenv==1.0.0
websockets==12.0

# Logging & Monitoring
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Notion & Ollama Integration
markdown==3.5.1
python-magic==0.4.27
# httpx는 이미 있음 - Ollama API 호출용

# Text Conversion
html2text==2020.1.16
markdownify==0.11.6
beautifulsoup4==4.12.2
bleach==6.1.0
