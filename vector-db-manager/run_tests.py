#!/usr/bin/env python3
"""
Vector DB Manager 통합 테스트 실행 스크립트
- 모든 테스트를 실행하고 결과를 출력합니다
- 개별 테스트 카테고리별로 실행 가능합니다
"""

import sys
import subprocess
import argparse
import time
from pathlib import Path

def run_command(cmd, description):
    """명령어 실행 및 결과 출력"""
    separator = '=' * 60
    print(f"\n{separator}")
    print(f"🔄 {description}")
    print(separator)
    
    start_time = time.time()
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        end_time = time.time()
        
        print(f"⏱️  실행 시간: {end_time - start_time:.2f}초")
        
        if result.stdout:
            print(f"\n📋 출력:")
            print(result.stdout)
        
        if result.stderr:
            print(f"\n⚠️  경고/오류:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 성공")
            return True
        else:
            print(f"❌ {description} - 실패 (종료 코드: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"💥 {description} - 예외 발생: {e}")
        return False

def run_all_tests():
    """모든 테스트 실행"""
    print("🚀 Vector DB Manager 통합 테스트 시작")
    print(f"📁 작업 디렉토리: {Path.cwd()}")
    
    results = []
    
    # 1. 코드 스타일 체크 (선택적)
    if Path("requirements-dev.txt").exists():
        results.append(run_command(
            "python -m flake8 app/ --max-line-length=100 --ignore=E501,W503",
            "코드 스타일 체크 (flake8)"
        ))
    
    # 2. 타입 체크 (선택적)
    if Path("requirements-dev.txt").exists():
        results.append(run_command(
            "python -m mypy app/ --ignore-missing-imports",
            "타입 체크 (mypy)"
        ))
    
    # 3. API 엔드포인트 테스트
    results.append(run_command(
        "python -m pytest tests/test_api/ -v --tb=short",
        "API 엔드포인트 테스트"
    ))
    
    # 4. 클라이언트 테스트
    results.append(run_command(
        "python -m pytest tests/test_clients/ -v --tb=short",
        "데이터베이스 클라이언트 테스트"
    ))
    
    # 5. 서비스 테스트
    results.append(run_command(
        "python -m pytest tests/test_services/ -v --tb=short",
        "서비스 레이어 테스트"
    ))
    
    # 6. 전체 테스트 커버리지
    results.append(run_command(
        "python -m pytest tests/ --cov=app --cov-report=term-missing",
        "전체 테스트 커버리지"
    ))
    
    # 결과 요약
    separator = '=' * 60
    print(f"\n{separator}")
    print("📊 테스트 결과 요약")
    print(separator)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 성공: {passed}/{total}")
    print(f"❌ 실패: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 모든 테스트가 성공했습니다!")
        return 0
    else:
        print("⚠️  일부 테스트가 실패했습니다.")
        return 1

def run_api_tests():
    """API 테스트만 실행"""
    return run_command(
        "python -m pytest tests/test_api/ -v --tb=short",
        "API 엔드포인트 테스트"
    )

def run_client_tests():
    """클라이언트 테스트만 실행"""
    print("🗄️  데이터베이스 클라이언트 테스트 시작")
    
    # 전체 클라이언트 테스트
    overall_result = run_command(
        "python -m pytest tests/test_clients/ -v --tb=short",
        "모든 데이터베이스 클라이언트 테스트"
    )
    
    # 개별 클라이언트 테스트
    individual_tests = [
        ("tests/test_clients/test_qdrant_client.py", "Qdrant 클라이언트"),
        ("tests/test_clients/test_weaviate_client.py", "Weaviate 클라이언트"),
        ("tests/test_clients/test_chroma_client.py", "ChromaDB 클라이언트"),
        ("tests/test_clients/test_meilisearch_client.py", "Meilisearch 클라이언트"),
        ("tests/test_clients/test_elasticsearch_client.py", "Elasticsearch 클라이언트"),
        ("tests/test_clients/test_mongodb_client.py", "MongoDB 클라이언트"),
    ]
    
    individual_results = []
    for test_file, description in individual_tests:
        if Path(test_file).exists():
            result = run_command(
                f"python -m pytest {test_file} -v --tb=short",
                f"{description} 테스트"
            )
            individual_results.append(result)
        else:
            print(f"⚠️  {test_file} 파일이 존재하지 않습니다.")
    
    # 결과 요약
    passed_individual = sum(individual_results)
    total_individual = len(individual_results)
    
    print(f"\n📊 클라이언트 테스트 요약:")
    print(f"전체 실행: {'✅' if overall_result else '❌'}")
    print(f"개별 테스트: {passed_individual}/{total_individual} 성공")
    
    return overall_result

def run_service_tests():
    """서비스 테스트만 실행"""
    return run_command(
        "python -m pytest tests/test_services/ -v --tb=short",
        "서비스 레이어 테스트"
    )

def run_integration_tests():
    """통합 테스트 실행"""
    print("🔗 통합 테스트 시작")
    
    # 서버가 실행 중인지 확인
    server_check = run_command(
        "curl -s http://localhost:8000/health || echo 'Server not running'",
        "서버 상태 확인"
    )
    
    if not server_check:
        print("⚠️  서버가 실행되지 않았습니다. 테스트를 위해 서버를 시작해주세요.")
        print("💡 서버 시작: ./scripts/run_dev.sh")
        return False
    
    # 실제 API 호출 테스트
    tests = [
        ("curl -s http://localhost:8000/health", "기본 헬스체크"),
        ("curl -s http://localhost:8000/api/v1/unified/health", "통합 헬스체크"),
        ("curl -s http://localhost:8000/api/v1/unified/databases", "데이터베이스 목록"),
        ("curl -s -X POST http://localhost:8000/api/v1/unified/search -H 'Content-Type: application/json' -d '{\"query\":\"test\",\"limit\":5}'", "통합 검색"),
    ]
    
    results = []
    for cmd, desc in tests:
        results.append(run_command(cmd, desc))
    
    passed = sum(results)
    total = len(results)
    print(f"\n✅ 통합 테스트 결과: {passed}/{total} 성공")
    
    return passed == total

def main():
    """메인 함수"""
    parser = argparse.ArgumentParser(description="Vector DB Manager 테스트 실행")
    parser.add_argument(
        "--type", 
        choices=["all", "api", "clients", "services", "integration"],
        default="all",
        help="실행할 테스트 타입"
    )
    
    args = parser.parse_args()
    
    # 현재 디렉토리가 프로젝트 루트인지 확인
    if not Path("app").exists() or not Path("tests").exists():
        print("❌ 프로젝트 루트 디렉토리에서 실행해주세요.")
        return 1
    
    # 가상환경 확인
    if not sys.prefix != sys.base_prefix:
        print("⚠️  가상환경이 활성화되지 않았을 수 있습니다.")
    
    # 테스트 실행
    if args.type == "all":
        return run_all_tests()
    elif args.type == "api":
        return 0 if run_api_tests() else 1
    elif args.type == "clients":
        return 0 if run_client_tests() else 1
    elif args.type == "services":
        return 0 if run_service_tests() else 1
    elif args.type == "integration":
        return 0 if run_integration_tests() else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
