#!/bin/bash
# 개발 서버 실행 스크립트
# - 환경 변수 로드
# - 개발 서버 시작
# - 핫 리로드 설정

echo "Starting Vector DB Manager development server..."

# 환경 변수 설정
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# .env 파일이 있는지 확인
if [ ! -f .env ]; then
    echo "Warning: .env file not found. Using default settings."
fi

# 개발 서버 시작
echo "Server will be available at: http://localhost:8000"
echo "API documentation: http://localhost:8000/docs"
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
