#!/bin/bash

# Vector DB Manager 애플리케이션 시작 스크립트

echo "🚀 Vector DB Manager 시작 중..."

# 네트워크가 존재하는지 확인하고 생성
if ! docker network ls | grep -q "dbs-network"; then
    echo "📡 dbs-network 네트워크 생성 중..."
    docker network create dbs-network
fi

# 애플리케이션 빌드 및 시작
echo "🔨 애플리케이션 빌드 및 시작 중..."
docker-compose up --build -d

# 상태 확인
echo "📊 컨테이너 상태 확인 중..."
docker-compose ps

echo ""
echo "✅ Vector DB Manager가 시작되었습니다!"
echo "🌐 애플리케이션 URL: http://localhost:7200"
echo "📖 API 문서: http://localhost:7200/docs"
echo "🔍 헬스체크: http://localhost:7200/health"
echo ""
echo "로그 확인: docker-compose logs -f vectordb-manager"
echo "중지: docker-compose down"
