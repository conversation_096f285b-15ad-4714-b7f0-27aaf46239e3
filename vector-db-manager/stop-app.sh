#!/bin/bash

# Vector DB Manager 애플리케이션 중지 스크립트

echo "🛑 Vector DB Manager 중지 중..."

# 컨테이너 중지 및 제거
docker-compose down

echo "✅ Vector DB Manager가 중지되었습니다."

# 선택적으로 이미지 제거 (--remove-images 옵션이 있을 때)
if [[ "$1" == "--remove-images" ]]; then
    echo "🗑️  이미지 제거 중..."
    docker-compose down --rmi all
    echo "✅ 이미지가 제거되었습니다."
fi

# 선택적으로 볼륨 제거 (--remove-volumes 옵션이 있을 때)
if [[ "$1" == "--remove-volumes" ]] || [[ "$2" == "--remove-volumes" ]]; then
    echo "🗑️  볼륨 제거 중..."
    docker-compose down -v
    echo "✅ 볼륨이 제거되었습니다."
fi
