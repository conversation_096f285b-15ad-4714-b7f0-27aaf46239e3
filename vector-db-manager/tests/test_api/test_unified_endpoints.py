"""
통합 API 엔드포인트 테스트
- 통합 검색 API 테스트
- 헬스체크 API 테스트
- 에러 응답 테스트
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestUnifiedEndpoints:
    
    def test_health_check(self):
        """헬스체크 API 테스트"""
        response = client.get("/api/v1/unified/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "databases" in data
        assert "healthy_count" in data
        assert "total_count" in data
        assert isinstance(data["databases"], list)
        
        # 각 데이터베이스 상태 확인
        for db in data["databases"]:
            assert "name" in db
            assert "type" in db
            assert "status" in db
            assert "url" in db
    
    def test_unified_search_text(self):
        """통합 텍스트 검색 API 테스트"""
        search_payload = {
            "query": "test search",
            "search_type": "text",
            "limit": 5
        }
        
        response = client.post("/api/v1/unified/search", json=search_payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "results" in data
        assert "total" in data
        assert "query" in data
        assert "search_time" in data
        assert "databases_searched" in data
        
        assert data["query"] == "test search"
        assert isinstance(data["results"], list)
        assert isinstance(data["search_time"], (int, float))
    
    def test_vector_search(self):
        """벡터 검색 API 테스트"""
        vector_payload = {
            "vector": [0.1, 0.2, 0.3, 0.4, 0.5] * 20,  # 100차원 벡터
            "limit": 3
        }
        
        response = client.post("/api/v1/unified/vector-search", json=vector_payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "results" in data
        assert "total" in data
        assert "search_time" in data
        
        assert isinstance(data["results"], list)
        assert isinstance(data["search_time"], (int, float))
    
    def test_list_databases(self):
        """데이터베이스 목록 API 테스트"""
        response = client.get("/api/v1/unified/databases")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "data" in data
        assert "databases" in data["data"]
        
        databases = data["data"]["databases"]
        assert isinstance(databases, list)
        assert len(databases) > 0
        
        # 각 데이터베이스 정보 확인
        for db in databases:
            assert "name" in db
            assert "type" in db
            assert "connected" in db
            assert "url" in db
    
    def test_unified_search_with_filters(self):
        """필터를 사용한 통합 검색 테스트"""
        search_payload = {
            "query": "test",
            "search_type": "text",
            "limit": 10,
            "filters": {"category": "technology"},
            "databases": ["mongodb", "elasticsearch"]
        }
        
        response = client.post("/api/v1/unified/search", json=search_payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert data["query"] == "test"
    
    def test_invalid_search_payload(self):
        """잘못된 검색 요청 테스트"""
        # 빈 쿼리
        response = client.post("/api/v1/unified/search", json={})
        assert response.status_code == 422
        
        # 잘못된 검색 타입
        invalid_payload = {
            "query": "test",
            "search_type": "invalid_type"
        }
        response = client.post("/api/v1/unified/search", json=invalid_payload)
        assert response.status_code == 200  # 기본값으로 처리됨
    
    def test_invalid_vector_search(self):
        """잘못된 벡터 검색 요청 테스트"""
        # 빈 벡터
        response = client.post("/api/v1/unified/vector-search", json={})
        assert response.status_code == 422
        
        # 잘못된 벡터 형식
        invalid_payload = {
            "vector": "not_a_vector"
        }
        response = client.post("/api/v1/unified/vector-search", json=invalid_payload)
        assert response.status_code == 422
    
    def test_root_endpoint(self):
        """루트 엔드포인트 테스트"""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_health_endpoint(self):
        """기본 헬스체크 엔드포인트 테스트"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
