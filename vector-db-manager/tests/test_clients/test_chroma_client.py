"""ChromaDB 클라이언트 테스트"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from app.clients.chroma_client import ChromaClient
from app.core.exceptions import DatabaseConnectionError, DatabaseOperationError


class TestChromaClient:
    """ChromaDB 클라이언트 테스트 클래스"""
    
    @pytest.fixture
    def client(self):
        """ChromaDB 클라이언트 인스턴스를 생성합니다."""
        return ChromaClient(
            url="localhost:8000",
            settings={}
        )
    
    @pytest.mark.asyncio
    async def test_connect_success(self, client):
        """연결 성공 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', True):
            with patch('app.clients.chroma_client.chromadb') as mock_chromadb:
                mock_http_client = MagicMock()
                mock_chromadb.HttpClient.return_value = mock_http_client
                
                await client.connect()
                
                assert client.client is not None
                mock_chromadb.HttpClient.assert_called_once_with(
                    host="localhost",
                    port=8000,
                    settings={}
                )
    
    @pytest.mark.asyncio
    async def test_connect_chromadb_unavailable(self, client):
        """ChromaDB 패키지 사용 불가능 시 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', False):
            with pytest.raises(DatabaseConnectionError, match="ChromaDB package not available"):
                await client.connect()
    
    @pytest.mark.asyncio
    async def test_connect_failure(self, client):
        """연결 실패 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', True):
            with patch('app.clients.chroma_client.chromadb') as mock_chromadb:
                mock_chromadb.HttpClient.side_effect = Exception("Connection failed")
                
                with pytest.raises(DatabaseConnectionError, match="Failed to connect to ChromaDB"):
                    await client.connect()
    
    @pytest.mark.asyncio
    async def test_disconnect(self, client):
        """연결 해제 테스트"""
        client.client = MagicMock()
        
        await client.disconnect()
        
        assert client.client is None
    
    @pytest.mark.asyncio
    async def test_health_check_chromadb_unavailable(self, client):
        """ChromaDB 패키지 사용 불가능 시 헬스체크 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', False):
            result = await client.health_check()
            
            assert result["name"] == "chroma"
            assert result["type"] == "vector"
            assert result["status"] == "unavailable"
            assert "ChromaDB package not available" in result["error"]
    
    @pytest.mark.asyncio
    async def test_health_check_v2_heartbeat_success(self, client):
        """v2 heartbeat API 성공 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', True):
            client.client = MagicMock()
            
            with patch('requests.get') as mock_get:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {"nanosecond_heartbeat": **********}
                mock_get.return_value = mock_response
                
                result = await client.health_check()
                
                assert result["name"] == "chroma"
                assert result["type"] == "vector"
                assert result["status"] == "healthy"
                assert result["api_version"] == "v2"
                assert "heartbeat" in result
                mock_get.assert_called_once_with("http://localhost:8000/api/v2/heartbeat", timeout=5)
    
    @pytest.mark.asyncio
    async def test_health_check_client_heartbeat_fallback(self, client):
        """클라이언트 heartbeat fallback 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', True):
            mock_client = MagicMock()
            mock_client.heartbeat.return_value = **********
            client.client = mock_client
            
            with patch('requests.get', side_effect=Exception("v2 failed")):
                result = await client.health_check()
                
                assert result["name"] == "chroma"
                assert result["type"] == "vector"
                assert result["status"] == "healthy"
                assert result["api_version"] == "client_heartbeat"
                assert "heartbeat" in result
    
    @pytest.mark.asyncio
    async def test_health_check_collection_list_fallback(self, client):
        """컬렉션 리스트 fallback 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', True):
            mock_client = MagicMock()
            mock_client.heartbeat.side_effect = AttributeError("No heartbeat method")
            mock_client.list_collections.return_value = ["collection1", "collection2"]
            client.client = mock_client
            
            with patch('requests.get', side_effect=Exception("v2 failed")):
                result = await client.health_check()
                
                assert result["name"] == "chroma"
                assert result["type"] == "vector"
                assert result["status"] == "healthy"
                assert result["api_version"] == "collection_list"
                assert result["collections_count"] == 2
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, client):
        """헬스체크 실패 테스트"""
        with patch('app.clients.chroma_client.CHROMADB_AVAILABLE', True):
            client.client = MagicMock()
            
            with patch('requests.get', side_effect=Exception("Connection error")):
                with patch.object(client.client, 'heartbeat', side_effect=Exception("Heartbeat failed")):
                    with patch.object(client.client, 'list_collections', side_effect=Exception("List failed")):
                        result = await client.health_check()
                        
                        assert result["name"] == "chroma"
                        assert result["type"] == "vector"
                        assert result["status"] == "unhealthy"
                        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_create_collection_success(self, client):
        """컬렉션 생성 성공 테스트"""
        mock_client = MagicMock()
        mock_collection = MagicMock()
        mock_collection.name = "test_collection"
        mock_client.create_collection.return_value = mock_collection
        client.client = mock_client
        
        result = await client.create_collection("test_collection", {"embedding_function": "default"})
        
        assert result["success"] is True
        assert result["data"]["name"] == "test_collection"
        mock_client.create_collection.assert_called_once_with(
            name="test_collection",
            embedding_function="default"
        )
    
    @pytest.mark.asyncio
    async def test_create_collection_no_client(self, client):
        """클라이언트 연결 없이 컬렉션 생성 시도 테스트"""
        with pytest.raises(DatabaseOperationError, match="Client not connected"):
            await client.create_collection("test_collection", {})
    
    @pytest.mark.asyncio
    async def test_create_collection_failure(self, client):
        """컬렉션 생성 실패 테스트"""
        mock_client = MagicMock()
        mock_client.create_collection.side_effect = Exception("Creation failed")
        client.client = mock_client
        
        result = await client.create_collection("test_collection", {})
        
        assert result["success"] is False
        assert "Creation failed" in result["error"]
    
    @pytest.mark.asyncio
    async def test_insert_data_success(self, client):
        """데이터 삽입 성공 테스트"""
        mock_client = MagicMock()
        mock_collection = MagicMock()
        mock_client.get_collection.return_value = mock_collection
        client.client = mock_client
        
        data = [
            {"id": "1", "vector": [0.1, 0.2, 0.3], "metadata": {"text": "hello"}}
        ]
        
        result = await client.insert_data("test_collection", data)
        
        assert result["success"] is True
        assert result["data"]["inserted_count"] == 1
        mock_collection.add.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_similar_success(self, client):
        """유사도 검색 성공 테스트"""
        mock_client = MagicMock()
        mock_collection = MagicMock()
        mock_collection.query.return_value = {
            "ids": [["1", "2"]],
            "distances": [[0.1, 0.2]],
            "metadatas": [[{"text": "hello"}, {"text": "world"}]]
        }
        mock_client.get_collection.return_value = mock_collection
        client.client = mock_client
        
        query_vector = [0.1, 0.2, 0.3]
        
        result = await client.search_similar("test_collection", query_vector, limit=2)
        
        assert result["success"] is True
        assert len(result["data"]["results"]) == 2
        assert result["data"]["results"][0]["id"] == "1"
        assert result["data"]["results"][0]["score"] == 0.1
    
    @pytest.mark.asyncio
    async def test_get_collection_info_success(self, client):
        """컬렉션 정보 조회 성공 테스트"""
        mock_client = MagicMock()
        mock_collection = MagicMock()
        mock_collection.name = "test_collection"
        mock_collection.count.return_value = 100
        mock_client.get_collection.return_value = mock_collection
        client.client = mock_client
        
        result = await client.get_collection_info("test_collection")
        
        assert result["success"] is True
        assert result["data"]["name"] == "test_collection"
        assert result["data"]["count"] == 100
    
    @pytest.mark.asyncio
    async def test_delete_collection_success(self, client):
        """컬렉션 삭제 성공 테스트"""
        mock_client = MagicMock()
        client.client = mock_client
        
        result = await client.delete_collection("test_collection")
        
        assert result["success"] is True
        mock_client.delete_collection.assert_called_once_with(name="test_collection")
    
    @pytest.mark.asyncio
    async def test_list_collections_success(self, client):
        """컬렉션 리스트 조회 성공 테스트"""
        mock_client = MagicMock()
        mock_collections = [MagicMock(name="collection1"), MagicMock(name="collection2")]
        mock_client.list_collections.return_value = mock_collections
        client.client = mock_client
        
        result = await client.list_collections()
        
        assert result["success"] is True
        assert len(result["data"]["collections"]) == 2
        assert result["data"]["collections"][0]["name"] == "collection1"
