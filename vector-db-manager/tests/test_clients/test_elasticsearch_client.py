"""
Elasticsearch 클라이언트 테스트
- 연결 테스트
- 인덱스 관리 테스트
- 검색 테스트
"""

import pytest
import asyncio
from app.clients.elasticsearch_client import ElasticsearchClient

class TestElasticsearchClient:
    
    @pytest.fixture
    def elasticsearch_client(self):
        """Elasticsearch 클라이언트 픽스처"""
        config = {
            'url': 'http://localhost:7213',
            'index_name': 'test_documents'
        }
        return ElasticsearchClient(config)
    
    @pytest.mark.asyncio
    async def test_connection(self, elasticsearch_client):
        """연결 테스트"""
        try:
            result = await elasticsearch_client.connect()
            assert isinstance(result, bool)
        except Exception as e:
            pytest.skip(f"Elasticsearch server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_health_check(self, elasticsearch_client):
        """헬스체크 테스트"""
        try:
            result = await elasticsearch_client.health_check()
            
            assert isinstance(result, dict)
            assert "name" in result
            assert "type" in result
            assert "status" in result
            assert "url" in result
            assert result["name"] == "elasticsearch"
            assert result["type"] == "traditional"
        except Exception as e:
            pytest.skip(f"Elasticsearch server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_text_search(self, elasticsearch_client):
        """텍스트 검색 테스트"""
        try:
            results = await elasticsearch_client.search("test query", limit=5)
            assert isinstance(results, list)
        except Exception as e:
            pytest.skip(f"Elasticsearch server not available or no index: {e}")
    
    @pytest.mark.asyncio
    async def test_insert_documents(self, elasticsearch_client):
        """문서 삽입 테스트"""
        try:
            test_docs = [
                {
                    "content": "Test document 1",
                    "title": "Test 1",
                    "metadata": {"category": "test"}
                },
                {
                    "content": "Test document 2",
                    "title": "Test 2", 
                    "metadata": {"category": "test"}
                }
            ]
            
            result = await elasticsearch_client.insert(test_docs)
            assert isinstance(result, bool)
        except Exception as e:
            pytest.skip(f"Elasticsearch server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_disconnect(self, elasticsearch_client):
        """연결 해제 테스트"""
        try:
            await elasticsearch_client.connect()
            await elasticsearch_client.disconnect()
            assert elasticsearch_client._connected is False
        except Exception as e:
            pytest.skip(f"Elasticsearch server not available: {e}")
