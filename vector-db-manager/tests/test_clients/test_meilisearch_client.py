"""Meilisearch 클라이언트 테스트"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from app.clients.meilisearch_client import MeilisearchClient
from app.core.exceptions import DatabaseConnectionError, DatabaseOperationError


class TestMeilisearchClient:
    """Meilisearch 클라이언트 테스트 클래스"""
    
    @pytest.fixture
    def client(self):
        """Meilisearch 클라이언트 인스턴스를 생성합니다."""
        return MeilisearchClient(
            url="http://localhost:7700",
            api_key="test_key"
        )
    
    @pytest.mark.asyncio
    async def test_connect_success(self, client):
        """연결 성공 테스트"""
        with patch('app.clients.meilisearch_client.MEILISEARCH_AVAILABLE', True):
            with patch('app.clients.meilisearch_client.meilisearch') as mock_meilisearch:
                mock_meili_client = MagicMock()
                mock_meilisearch.Client.return_value = mock_meili_client
                
                await client.connect()
                
                assert client.client is not None
                mock_meilisearch.Client.assert_called_once_with(
                    "http://localhost:7700",
                    "test_key"
                )
    
    @pytest.mark.asyncio
    async def test_connect_meilisearch_unavailable(self, client):
        """Meilisearch 패키지 사용 불가능 시 테스트"""
        with patch('app.clients.meilisearch_client.MEILISEARCH_AVAILABLE', False):
            with pytest.raises(DatabaseConnectionError, match="Meilisearch package not available"):
                await client.connect()
    
    @pytest.mark.asyncio
    async def test_connect_failure(self, client):
        """연결 실패 테스트"""
        with patch('app.clients.meilisearch_client.MEILISEARCH_AVAILABLE', True):
            with patch('app.clients.meilisearch_client.meilisearch') as mock_meilisearch:
                mock_meilisearch.Client.side_effect = Exception("Connection failed")
                
                with pytest.raises(DatabaseConnectionError, match="Failed to connect to Meilisearch"):
                    await client.connect()
    
    @pytest.mark.asyncio
    async def test_disconnect(self, client):
        """연결 해제 테스트"""
        client.client = MagicMock()
        
        await client.disconnect()
        
        assert client.client is None
    
    @pytest.mark.asyncio
    async def test_health_check_meilisearch_unavailable(self, client):
        """Meilisearch 패키지 사용 불가능 시 헬스체크 테스트"""
        with patch('app.clients.meilisearch_client.MEILISEARCH_AVAILABLE', False):
            result = await client.health_check()
            
            assert result["name"] == "meilisearch"
            assert result["type"] == "search"
            assert result["status"] == "unavailable"
            assert "Meilisearch package not available" in result["error"]
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, client):
        """헬스체크 성공 테스트"""
        with patch('app.clients.meilisearch_client.MEILISEARCH_AVAILABLE', True):
            mock_client = MagicMock()
            mock_client.health.return_value = {"status": "available"}
            client.client = mock_client
            
            result = await client.health_check()
            
            assert result["name"] == "meilisearch"
            assert result["type"] == "search"
            assert result["status"] == "healthy"
            assert "health" in result
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, client):
        """헬스체크 실패 테스트"""
        with patch('app.clients.meilisearch_client.MEILISEARCH_AVAILABLE', True):
            mock_client = MagicMock()
            mock_client.health.side_effect = Exception("Health check failed")
            client.client = mock_client
            
            result = await client.health_check()
            
            assert result["name"] == "meilisearch"
            assert result["type"] == "search"
            assert result["status"] == "unhealthy"
            assert "Health check failed" in result["error"]
    
    @pytest.mark.asyncio
    async def test_create_index_success(self, client):
        """인덱스 생성 성공 테스트"""
        mock_client = MagicMock()
        mock_task = {"taskUid": 1, "status": "enqueued"}
        mock_client.create_index.return_value = mock_task
        client.client = mock_client
        
        result = await client.create_index("test_index", {"primaryKey": "id"})
        
        assert result["success"] is True
        assert result["data"]["task"]["taskUid"] == 1
        mock_client.create_index.assert_called_once_with("test_index", {"primary_key": "id"})
    
    @pytest.mark.asyncio
    async def test_create_index_no_client(self, client):
        """클라이언트 연결 없이 인덱스 생성 시도 테스트"""
        with pytest.raises(DatabaseOperationError, match="Client not connected"):
            await client.create_index("test_index", {})
    
    @pytest.mark.asyncio
    async def test_create_index_failure(self, client):
        """인덱스 생성 실패 테스트"""
        mock_client = MagicMock()
        mock_client.create_index.side_effect = Exception("Creation failed")
        client.client = mock_client
        
        result = await client.create_index("test_index", {})
        
        assert result["success"] is False
        assert "Creation failed" in result["error"]
    
    @pytest.mark.asyncio
    async def test_insert_documents_success(self, client):
        """문서 삽입 성공 테스트"""
        mock_client = MagicMock()
        mock_index = MagicMock()
        mock_task = {"taskUid": 1, "status": "enqueued"}
        mock_index.add_documents.return_value = mock_task
        mock_client.index.return_value = mock_index
        client.client = mock_client
        
        documents = [
            {"id": 1, "title": "Document 1", "content": "Content 1"},
            {"id": 2, "title": "Document 2", "content": "Content 2"}
        ]
        
        result = await client.insert_documents("test_index", documents)
        
        assert result["success"] is True
        assert result["data"]["task"]["taskUid"] == 1
        mock_index.add_documents.assert_called_once_with(documents)
    
    @pytest.mark.asyncio
    async def test_search_documents_success(self, client):
        """문서 검색 성공 테스트"""
        mock_client = MagicMock()
        mock_index = MagicMock()
        mock_search_result = {
            "hits": [
                {"id": 1, "title": "Document 1", "_score": 0.9},
                {"id": 2, "title": "Document 2", "_score": 0.8}
            ],
            "estimatedTotalHits": 2,
            "processingTimeMs": 5
        }
        mock_index.search.return_value = mock_search_result
        mock_client.index.return_value = mock_index
        client.client = mock_client
        
        result = await client.search_documents("test_index", "test query", {"limit": 10})
        
        assert result["success"] is True
        assert len(result["data"]["hits"]) == 2
        assert result["data"]["estimated_total_hits"] == 2
        mock_index.search.assert_called_once_with("test query", {"limit": 10})
    
    @pytest.mark.asyncio
    async def test_get_index_info_success(self, client):
        """인덱스 정보 조회 성공 테스트"""
        mock_client = MagicMock()
        mock_index_info = {
            "uid": "test_index",
            "primaryKey": "id",
            "createdAt": "2023-01-01T00:00:00Z",
            "updatedAt": "2023-01-01T00:00:00Z"
        }
        mock_client.get_index.return_value = MagicMock(**mock_index_info)
        client.client = mock_client
        
        result = await client.get_index_info("test_index")
        
        assert result["success"] is True
        assert result["data"]["uid"] == "test_index"
        assert result["data"]["primary_key"] == "id"
    
    @pytest.mark.asyncio
    async def test_delete_index_success(self, client):
        """인덱스 삭제 성공 테스트"""
        mock_client = MagicMock()
        mock_task = {"taskUid": 1, "status": "enqueued"}
        mock_client.delete_index.return_value = mock_task
        client.client = mock_client
        
        result = await client.delete_index("test_index")
        
        assert result["success"] is True
        assert result["data"]["task"]["taskUid"] == 1
        mock_client.delete_index.assert_called_once_with("test_index")
    
    @pytest.mark.asyncio
    async def test_list_indexes_success(self, client):
        """인덱스 리스트 조회 성공 테스트"""
        mock_client = MagicMock()
        mock_indexes = {
            "results": [
                {"uid": "index1", "primaryKey": "id"},
                {"uid": "index2", "primaryKey": "key"}
            ],
            "total": 2
        }
        mock_client.get_indexes.return_value = mock_indexes
        client.client = mock_client
        
        result = await client.list_indexes()
        
        assert result["success"] is True
        assert len(result["data"]["indexes"]) == 2
        assert result["data"]["total"] == 2
    
    @pytest.mark.asyncio
    async def test_update_settings_success(self, client):
        """설정 업데이트 성공 테스트"""
        mock_client = MagicMock()
        mock_index = MagicMock()
        mock_task = {"taskUid": 1, "status": "enqueued"}
        mock_index.update_settings.return_value = mock_task
        mock_client.index.return_value = mock_index
        client.client = mock_client
        
        settings = {
            "searchableAttributes": ["title", "content"],
            "filterableAttributes": ["category"]
        }
        
        result = await client.update_settings("test_index", settings)
        
        assert result["success"] is True
        assert result["data"]["task"]["taskUid"] == 1
        mock_index.update_settings.assert_called_once_with(settings)
    
    @pytest.mark.asyncio
    async def test_get_task_status_success(self, client):
        """작업 상태 조회 성공 테스트"""
        mock_client = MagicMock()
        mock_task_status = {
            "uid": 1,
            "status": "succeeded",
            "type": "documentAdditionOrUpdate",
            "enqueuedAt": "2023-01-01T00:00:00Z"
        }
        mock_client.get_task.return_value = mock_task_status
        client.client = mock_client
        
        result = await client.get_task_status(1)
        
        assert result["success"] is True
        assert result["data"]["status"] == "succeeded"
        assert result["data"]["uid"] == 1
        mock_client.get_task.assert_called_once_with(1)
