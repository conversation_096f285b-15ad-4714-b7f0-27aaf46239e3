"""
MongoDB 클라이언트 테스트
- 연결 테스트
- 텍스트 검색 테스트
- 문서 관리 테스트
"""

import pytest
import asyncio
from app.clients.mongodb_client import MongoDBClient

class TestMongoDBClient:
    
    @pytest.fixture
    def mongodb_client(self):
        """MongoDB 클라이언트 픽스처"""
        config = {
            'url': 'mongodb://localhost:7215',
            'database_name': 'test_vector_db',
            'collection_name': 'test_documents'
        }
        return MongoDBClient(config)
    
    @pytest.mark.asyncio
    async def test_connection(self, mongodb_client):
        """연결 테스트"""
        try:
            result = await mongodb_client.connect()
            assert isinstance(result, bool)
        except Exception as e:
            pytest.skip(f"MongoDB server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_health_check(self, mongodb_client):
        """헬스체크 테스트"""
        try:
            result = await mongodb_client.health_check()
            
            assert isinstance(result, dict)
            assert "name" in result
            assert "type" in result
            assert "status" in result
            assert "url" in result
            assert result["name"] == "mongodb"
            assert result["type"] == "traditional"
        except Exception as e:
            pytest.skip(f"MongoDB server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_text_search(self, mongodb_client):
        """텍스트 검색 테스트"""
        try:
            results = await mongodb_client.search("test query", limit=5)
            assert isinstance(results, list)
        except Exception as e:
            # 텍스트 인덱스가 없을 수 있음
            pytest.skip(f"MongoDB text search not available: {e}")
    
    @pytest.mark.asyncio
    async def test_insert_documents(self, mongodb_client):
        """문서 삽입 테스트"""
        try:
            test_docs = [
                {
                    "content": "Test document 1",
                    "title": "Test 1",
                    "metadata": {"category": "test"}
                },
                {
                    "content": "Test document 2", 
                    "title": "Test 2",
                    "metadata": {"category": "test"}
                }
            ]
            
            result = await mongodb_client.insert(test_docs)
            assert isinstance(result, bool)
        except Exception as e:
            pytest.skip(f"MongoDB server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_disconnect(self, mongodb_client):
        """연결 해제 테스트"""
        try:
            await mongodb_client.connect()
            await mongodb_client.disconnect()
            assert mongodb_client._connected is False
        except Exception as e:
            pytest.skip(f"MongoDB server not available: {e}")
