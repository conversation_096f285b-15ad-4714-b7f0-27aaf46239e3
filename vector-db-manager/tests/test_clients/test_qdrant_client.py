"""
Qdrant 클라이언트 테스트
- 연결 테스트
- 벡터 검색 테스트
- 컬렉션 관리 테스트
"""

import pytest
import asyncio
from app.clients.qdrant_client import QdrantClientWrapper

class TestQdrantClient:
    
    @pytest.fixture
    def qdrant_client(self):
        """Qdrant 클라이언트 픽스처"""
        config = {
            'url': 'http://localhost:7211',
            'collection_name': 'test_collection'
        }
        return QdrantClientWrapper(config)
    
    @pytest.mark.asyncio
    async def test_connection(self, qdrant_client):
        """연결 테스트"""
        try:
            result = await qdrant_client.connect()
            assert isinstance(result, bool)
        except Exception as e:
            # Qdrant 서버가 실행되지 않은 경우
            pytest.skip(f"Qdrant server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_health_check(self, qdrant_client):
        """헬스체크 테스트"""
        try:
            result = await qdrant_client.health_check()
            
            assert isinstance(result, dict)
            assert "name" in result
            assert "type" in result
            assert "status" in result
            assert "url" in result
            assert result["name"] == "qdrant"
            assert result["type"] == "vector"
        except Exception as e:
            pytest.skip(f"Qdrant server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_text_search(self, qdrant_client):
        """텍스트 검색 테스트 (제한적)"""
        try:
            results = await qdrant_client.search("test query", limit=5)
            assert isinstance(results, list)
        except Exception as e:
            pytest.skip(f"Qdrant server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_vector_search(self, qdrant_client):
        """벡터 검색 테스트"""
        try:
            test_vector = [0.1] * 100  # 100차원 테스트 벡터
            results = await qdrant_client.vector_search(test_vector, limit=3)
            assert isinstance(results, list)
        except Exception as e:
            pytest.skip(f"Qdrant server not available: {e}")
    
    @pytest.mark.asyncio
    async def test_disconnect(self, qdrant_client):
        """연결 해제 테스트"""
        try:
            await qdrant_client.connect()
            await qdrant_client.disconnect()
            assert qdrant_client._connected is False
        except Exception as e:
            pytest.skip(f"Qdrant server not available: {e}")
