"""
DatabaseManager 서비스 테스트
- 클라이언트 초기화 테스트
- 연결 관리 테스트
- 헬스체크 테스트
"""

import pytest
import asyncio
from app.services.database_manager import DatabaseManager

class TestDatabaseManager:
    
    @pytest.fixture
    def db_manager(self):
        """DatabaseManager 픽스처"""
        return DatabaseManager()
    
    def test_initialization(self, db_manager):
        """초기화 테스트"""
        assert db_manager.clients is not None
        assert isinstance(db_manager.clients, dict)
        assert len(db_manager.clients) > 0
        
        # 예상되는 클라이언트들이 있는지 확인
        expected_clients = ['weaviate', 'qdrant', 'chroma', 'elasticsearch', 'meilisearch', 'mongodb']
        for client_name in expected_clients:
            assert client_name in db_manager.clients
    
    @pytest.mark.asyncio
    async def test_connect_all(self, db_manager):
        """전체 연결 테스트"""
        results = await db_manager.connect_all()
        
        assert isinstance(results, dict)
        assert len(results) > 0
        
        # 각 결과가 boolean인지 확인
        for name, connected in results.items():
            assert isinstance(connected, bool)
            assert name in db_manager.clients
    
    @pytest.mark.asyncio
    async def test_health_check(self, db_manager):
        """헬스체크 테스트"""
        result = await db_manager.health_check()
        
        assert isinstance(result, dict)
        assert "databases" in result
        assert "healthy_count" in result
        assert "total_count" in result
        assert "overall_status" in result
        
        assert isinstance(result["databases"], list)
        assert isinstance(result["healthy_count"], int)
        assert isinstance(result["total_count"], int)
        assert result["overall_status"] in ["healthy", "degraded"]
        
        # 각 데이터베이스 상태 확인
        for db_status in result["databases"]:
            assert "name" in db_status
            assert "type" in db_status
            assert "status" in db_status
            assert "url" in db_status
