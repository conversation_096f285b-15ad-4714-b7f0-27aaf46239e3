"""
텍스트 변환 유틸리티 테스트
- 텍스트 타입 감지 테스트
- 변환 기능 테스트
- 통합 처리 테스트
"""

import pytest
from app.utils.text_converter import (
    TextTypeDetector, TextConverter, SmartTextProcessor, TextType
)

class TestTextTypeDetector:
    
    def test_detect_markdown(self):
        """마크다운 감지 테스트"""
        markdown_text = """
# 제목
## 부제목
- 리스트 항목
- 다른 항목

**볼드** 텍스트와 *이탤릭* 텍스트

```python
print("코드 블록")
```
"""
        detected_type = TextTypeDetector.detect_type(markdown_text)
        assert detected_type == TextType.MARKDOWN
    
    def test_detect_html(self):
        """HTML 감지 테스트"""
        html_text = """
<h1>제목</h1>
<p>이것은 <strong>HTML</strong> 텍스트입니다.</p>
<ul>
    <li>리스트 항목 1</li>
    <li>리스트 항목 2</li>
</ul>
"""
        detected_type = TextTypeDetector.detect_type(html_text)
        assert detected_type == TextType.HTML
    
    def test_detect_notion_format(self):
        """Notion 형식 감지 테스트"""
        notion_text = """
제목

□ 할 일 1
☑ 완료된 할 일
→ 화살표 항목
📅 2024-01-01
"""
        detected_type = TextTypeDetector.detect_type(notion_text)
        assert detected_type == TextType.NOTION_FORMAT
    
    def test_detect_plain_text(self):
        """일반 텍스트 감지 테스트"""
        plain_text = "이것은 일반 텍스트입니다. 특별한 형식이 없습니다."
        detected_type = TextTypeDetector.detect_type(plain_text)
        assert detected_type == TextType.PLAIN_TEXT

class TestTextConverter:
    
    @pytest.fixture
    def converter(self):
        return TextConverter()
    
    def test_html_to_markdown(self, converter):
        """HTML → 마크다운 변환 테스트"""
        html_text = """
<h1>제목</h1>
<p>이것은 <strong>중요한</strong> 텍스트입니다.</p>
<ul>
    <li>항목 1</li>
    <li>항목 2</li>
</ul>
"""
        result = converter._html_to_markdown(html_text)
        assert "# 제목" in result
        assert "**중요한**" in result
        assert "- 항목 1" in result
    
    def test_notion_to_markdown(self, converter):
        """Notion → 마크다운 변환 테스트"""
        notion_text = """
□ 할 일 1
☑ 완료된 할 일
→ 일반 항목
"""
        result = converter._notion_to_markdown(notion_text)
        assert "- [ ] 할 일 1" in result
        assert "- [x] 완료된 할 일" in result
        assert "- 일반 항목" in result
    
    def test_clean_markdown(self, converter):
        """마크다운 정리 테스트"""
        messy_markdown = """


#    제목    


-   리스트 항목    


"""
        result = converter._clean_markdown(messy_markdown)
        assert result.count('\n\n') <= 1  # 연속된 빈 줄 축소
        assert result.startswith("# 제목")
        assert "- 리스트 항목" in result

class TestSmartTextProcessor:
    
    @pytest.fixture
    def processor(self):
        return SmartTextProcessor()
    
    def test_process_markdown_text(self, processor):
        """마크다운 텍스트 처리 테스트"""
        markdown_text = """
# AI와 머신러닝
이것은 AI에 대한 문서입니다.

## 주요 내용
- 딥러닝
- 자연어 처리
"""
        result = processor.process_text(markdown_text)
        
        assert result['processing_successful'] is True
        assert result['detected_type'] == TextType.MARKDOWN.value
        assert result['basic_metadata']['estimated_title'] == "AI와 머신러닝"
        assert result['basic_metadata']['word_count'] > 0
    
    def test_process_html_text(self, processor):
        """HTML 텍스트 처리 테스트"""
        html_text = """
<h1>웹 개발 가이드</h1>
<p>이것은 <strong>웹 개발</strong>에 대한 내용입니다.</p>
<code>console.log('Hello');</code>
"""
        result = processor.process_text(html_text, force_conversion=True)
        
        assert result['processing_successful'] is True
        assert result['was_converted'] is True
        assert "# 웹 개발 가이드" in result['converted_text']
        assert "**웹 개발**" in result['converted_text']
    
    def test_process_notion_text(self, processor):
        """Notion 텍스트 처리 테스트"""
        notion_text = """
프로젝트 관리

□ 기획서 작성
☑ 디자인 완료
→ 개발 진행 중
📅 2024-01-15
"""
        result = processor.process_text(notion_text)
        
        assert result['processing_successful'] is True
        assert result['detected_type'] == TextType.NOTION_FORMAT.value
        assert "- [ ] 기획서 작성" in result['converted_text']
        assert "- [x] 디자인 완료" in result['converted_text']
    
    def test_extract_basic_metadata(self, processor):
        """기본 메타데이터 추출 테스트"""
        text_with_features = """
# 테스트 문서

이것은 테스트입니다.

```python
print("코드")
```

[링크](https://example.com)
![이미지](image.jpg)
"""
        metadata = processor._extract_basic_metadata(text_with_features)
        
        assert metadata['estimated_title'] == "테스트 문서"
        assert metadata['has_code_blocks'] is True
        assert metadata['has_links'] is True
        assert metadata['has_images'] is True
        assert metadata['line_count'] > 0
        assert metadata['word_count'] > 0
